###

# curl -X 'POST'
#  'https://api.longship.io/v1/chargepoints/DE_EUL_EHAN_0001_01/getlocallistversion'
#  -H 'accept: application/json'
#  -H 'Ocp-Apim-Subscription-Key: d6c0cd5f912b435ebfc1632a4b865742'
#  -H 'x-api-key: acc46b4ebc6a4bc3b28d748e473a77f2'
#  -H 'Content-Type: application/json'
#  -d '{}'
POST https://api.longship.io/v1/chargepoints/DE_EUL_EBOE0004_02/sendlocallist
accept: application/json
Ocp-Apim-Subscription-Key: d6c0cd5f912b435ebfc1632a4b865742
x-api-key: acc46b4ebc6a4bc3b28d748e473a77f2
Content-Type: application/json

{
  "listVersion": 1,
  "localAuthorizationList": [
    {
      "idTag": "42a44103",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "623f2922",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "92506402",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "92e4a821",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "b26f1b22",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "b2ee3222",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "f2c2c721",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    },
    {
      "idTag": "f2d9e421",
      "idTagInfo": {
        "expiryDate": "2040-01-01",
        "parentIdTag": "",
        "status": "Accepted"
      }
    }
  ],
  "updateType": "Full"
}

###


