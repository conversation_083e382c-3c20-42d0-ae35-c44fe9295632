import { z } from 'zod';
import { Prisma } from '../../client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;


/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const CdrScalarFieldEnumSchema = z.enum(['id','auth_method','authorization_reference','country_code','credit','currency','end_date_time','last_updated','party_id','session_id','start_date_time','total_energy','total_parking_time','total_time']);

export const CounterScalarFieldEnumSchema = z.enum(['id','sequence_value']);

export const EmpScalarFieldEnumSchema = z.enum(['id','amount_to_block','country_code','name','party_id']);

export const EmpAddressScalarFieldEnumSchema = z.enum(['id','city','country','empId','from','house_number','postal_code','street','to','ust_id','vat_id']);

export const EmpPriceScalarFieldEnumSchema = z.enum(['id','start','end','energy_price','blocking_fee','blocking_fee_start','blocking_fee_max','session_fee','tax_rate','empId','current_type']);

export const InvoiceScalarFieldEnumSchema = z.enum(['id','authorization_reference','create_date','currency','emp_country_code','emp_party_id','invoice_date','invoice_number','kind_of_invoice','last_update','local_end_datetime','local_start_datetime','mailToSend','paid_date','sentAsEmail','service_period_from','service_period_to','status','subject','sum_gross','sum_net','sum_tax']);

export const LocationScalarFieldEnumSchema = z.enum(['id','address','charging_when_closed','city','country','country_code','last_updated','name','parking_type','party_id','postal_code','publish','publish_allowed_to','time_zone']);

export const LocationPriceScalarFieldEnumSchema = z.enum(['id','locationId','start','end','energy_price','blocking_fee','current_type','parking_price','blocking_fee_start','blocking_fee_max','session_fee','tax_rate','empId']);

export const OcpiConnectionScalarFieldEnumSchema = z.enum(['id','cpo_secret','empId','emp_secret','inital_token','name','url','version']);

export const PaymentIntentScalarFieldEnumSchema = z.enum(['id','amount','amount_capturable','amount_received','authorization_reference','cdrId','createdAt','energy_price','evseId','invoiceMail','locationId','min_kwh_in_kwh','min_time_in_min','parking_price','sessionId','session_fee','session_start_token_uid','status','tax_rate','updatedAt']);

export const SessionScalarFieldEnumSchema = z.enum(['id','auth_method','authorization_reference','connector_id','country_code','currency','end_date_time','evse_uid','kwh','last_updated','location_id','party_id','start_date_time','status']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const CurrentTypeSchema = z.enum(['AC','DC']);

export type CurrentTypeType = `${z.infer<typeof CurrentTypeSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// CDR SCHEMA
/////////////////////////////////////////

export const CdrSchema = z.object({
  id: z.string(),
  auth_method: z.string(),
  authorization_reference: z.string(),
  country_code: z.string(),
  credit: z.boolean(),
  currency: z.string(),
  end_date_time: z.string(),
  last_updated: z.string(),
  party_id: z.string(),
  session_id: z.string(),
  start_date_time: z.string(),
  /**
   * Multiple data types found: Float: 47.1%, Int: 52.9% out of 17 sampled entries
   */
  total_energy: JsonValueSchema.nullable(),
  /**
   * Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
   */
  total_parking_time: JsonValueSchema.nullable(),
  total_time: z.number(),
})

export type Cdr = z.infer<typeof CdrSchema>

/////////////////////////////////////////
// CDR PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrPartialSchema = CdrSchema.partial()

export type CdrPartial = z.infer<typeof CdrPartialSchema>

// CDR OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrOptionalDefaultsSchema = CdrSchema.merge(z.object({
}))

export type CdrOptionalDefaults = z.infer<typeof CdrOptionalDefaultsSchema>

/////////////////////////////////////////
// COUNTER SCHEMA
/////////////////////////////////////////

export const CounterSchema = z.object({
  id: z.string(),
  sequence_value: z.bigint(),
})

export type Counter = z.infer<typeof CounterSchema>

/////////////////////////////////////////
// COUNTER PARTIAL SCHEMA
/////////////////////////////////////////

export const CounterPartialSchema = CounterSchema.partial()

export type CounterPartial = z.infer<typeof CounterPartialSchema>

// COUNTER OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CounterOptionalDefaultsSchema = CounterSchema.merge(z.object({
}))

export type CounterOptionalDefaults = z.infer<typeof CounterOptionalDefaultsSchema>

/////////////////////////////////////////
// EMP SCHEMA
/////////////////////////////////////////

export const EmpSchema = z.object({
  id: z.string(),
  amount_to_block: z.number(),
  country_code: z.string(),
  name: z.string(),
  party_id: z.string(),
})

export type Emp = z.infer<typeof EmpSchema>

/////////////////////////////////////////
// EMP PARTIAL SCHEMA
/////////////////////////////////////////

export const EmpPartialSchema = EmpSchema.partial()

export type EmpPartial = z.infer<typeof EmpPartialSchema>

// EMP OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const EmpOptionalDefaultsSchema = EmpSchema.merge(z.object({
  id: z.string().optional(),
}))

export type EmpOptionalDefaults = z.infer<typeof EmpOptionalDefaultsSchema>

/////////////////////////////////////////
// EMP ADDRESS SCHEMA
/////////////////////////////////////////

export const EmpAddressSchema = z.object({
  id: z.string(),
  city: z.string(),
  country: z.string(),
  empId: z.string(),
  from: z.date(),
  house_number: z.string(),
  postal_code: z.string(),
  street: z.string(),
  to: z.date(),
  ust_id: z.string(),
  vat_id: z.string(),
})

export type EmpAddress = z.infer<typeof EmpAddressSchema>

/////////////////////////////////////////
// EMP ADDRESS PARTIAL SCHEMA
/////////////////////////////////////////

export const EmpAddressPartialSchema = EmpAddressSchema.partial()

export type EmpAddressPartial = z.infer<typeof EmpAddressPartialSchema>

// EMP ADDRESS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const EmpAddressOptionalDefaultsSchema = EmpAddressSchema.merge(z.object({
  id: z.string().optional(),
}))

export type EmpAddressOptionalDefaults = z.infer<typeof EmpAddressOptionalDefaultsSchema>

/////////////////////////////////////////
// EMP PRICE SCHEMA
/////////////////////////////////////////

export const EmpPriceSchema = z.object({
  current_type: CurrentTypeSchema,
  id: z.string(),
  start: z.date(),
  end: z.date(),
  energy_price: z.number(),
  blocking_fee: z.number(),
  blocking_fee_start: z.number(),
  blocking_fee_max: z.number(),
  session_fee: z.number(),
  tax_rate: z.number(),
  empId: z.string().nullish(),
})

export type EmpPrice = z.infer<typeof EmpPriceSchema>

/////////////////////////////////////////
// EMP PRICE PARTIAL SCHEMA
/////////////////////////////////////////

export const EmpPricePartialSchema = EmpPriceSchema.partial()

export type EmpPricePartial = z.infer<typeof EmpPricePartialSchema>

// EMP PRICE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const EmpPriceOptionalDefaultsSchema = EmpPriceSchema.merge(z.object({
  current_type: CurrentTypeSchema.optional(),
  id: z.string().optional(),
}))

export type EmpPriceOptionalDefaults = z.infer<typeof EmpPriceOptionalDefaultsSchema>

/////////////////////////////////////////
// INVOICE SCHEMA
/////////////////////////////////////////

export const InvoiceSchema = z.object({
  id: z.string(),
  authorization_reference: z.string(),
  create_date: z.date(),
  currency: z.string(),
  emp_country_code: z.string(),
  emp_party_id: z.string(),
  invoice_date: z.date(),
  invoice_number: z.string().nullish(),
  kind_of_invoice: z.string(),
  last_update: z.date(),
  local_end_datetime: z.string().nullish(),
  local_start_datetime: z.string().nullish(),
  mailToSend: z.string().nullish(),
  paid_date: z.date().nullish(),
  sentAsEmail: z.boolean(),
  service_period_from: z.date(),
  service_period_to: z.date(),
  status: z.string(),
  subject: z.string(),
  sum_gross: z.number(),
  sum_net: z.number(),
  sum_tax: z.number(),
})

export type Invoice = z.infer<typeof InvoiceSchema>

/////////////////////////////////////////
// INVOICE PARTIAL SCHEMA
/////////////////////////////////////////

export const InvoicePartialSchema = InvoiceSchema.partial()

export type InvoicePartial = z.infer<typeof InvoicePartialSchema>

// INVOICE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const InvoiceOptionalDefaultsSchema = InvoiceSchema.merge(z.object({
  id: z.string().optional(),
}))

export type InvoiceOptionalDefaults = z.infer<typeof InvoiceOptionalDefaultsSchema>

/////////////////////////////////////////
// LOCATION SCHEMA
/////////////////////////////////////////

export const LocationSchema = z.object({
  id: z.string(),
  address: z.string(),
  charging_when_closed: z.boolean(),
  city: z.string(),
  country: z.string(),
  country_code: z.string(),
  last_updated: z.string(),
  name: z.string(),
  parking_type: z.string(),
  party_id: z.string(),
  postal_code: z.string(),
  publish: z.boolean(),
  /**
   * Could not determine type: the field only had null or empty values in the sample set.
   */
  publish_allowed_to: JsonValueSchema,
  time_zone: z.string(),
})

export type Location = z.infer<typeof LocationSchema>

/////////////////////////////////////////
// LOCATION PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationPartialSchema = LocationSchema.partial()

export type LocationPartial = z.infer<typeof LocationPartialSchema>

// LOCATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationOptionalDefaultsSchema = LocationSchema.merge(z.object({
}))

export type LocationOptionalDefaults = z.infer<typeof LocationOptionalDefaultsSchema>

/////////////////////////////////////////
// LOCATION PRICE SCHEMA
/////////////////////////////////////////

export const LocationPriceSchema = z.object({
  current_type: CurrentTypeSchema,
  id: z.string(),
  locationId: z.string().nullish(),
  start: z.date(),
  end: z.date(),
  energy_price: z.number(),
  blocking_fee: z.number(),
  parking_price: z.number().nullish(),
  blocking_fee_start: z.number(),
  blocking_fee_max: z.number(),
  session_fee: z.number(),
  tax_rate: z.number(),
  empId: z.string().nullish(),
})

export type LocationPrice = z.infer<typeof LocationPriceSchema>

/////////////////////////////////////////
// LOCATION PRICE PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationPricePartialSchema = LocationPriceSchema.partial()

export type LocationPricePartial = z.infer<typeof LocationPricePartialSchema>

// LOCATION PRICE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationPriceOptionalDefaultsSchema = LocationPriceSchema.merge(z.object({
  current_type: CurrentTypeSchema.optional(),
  id: z.string().optional(),
}))

export type LocationPriceOptionalDefaults = z.infer<typeof LocationPriceOptionalDefaultsSchema>

/////////////////////////////////////////
// OCPI CONNECTION SCHEMA
/////////////////////////////////////////

export const OcpiConnectionSchema = z.object({
  id: z.string(),
  cpo_secret: z.string(),
  empId: z.string(),
  emp_secret: z.string(),
  inital_token: z.string(),
  name: z.string(),
  url: z.string(),
  version: z.string(),
})

export type OcpiConnection = z.infer<typeof OcpiConnectionSchema>

/////////////////////////////////////////
// OCPI CONNECTION PARTIAL SCHEMA
/////////////////////////////////////////

export const OcpiConnectionPartialSchema = OcpiConnectionSchema.partial()

export type OcpiConnectionPartial = z.infer<typeof OcpiConnectionPartialSchema>

// OCPI CONNECTION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const OcpiConnectionOptionalDefaultsSchema = OcpiConnectionSchema.merge(z.object({
  id: z.string().optional(),
}))

export type OcpiConnectionOptionalDefaults = z.infer<typeof OcpiConnectionOptionalDefaultsSchema>

/////////////////////////////////////////
// PAYMENT INTENT SCHEMA
/////////////////////////////////////////

export const PaymentIntentSchema = z.object({
  id: z.string(),
  /**
   * Multiple data types found: Int: 65.6%, BigInt: 34.4% out of 131 sampled entries
   */
  amount: JsonValueSchema.nullable(),
  /**
   * Multiple data types found: Int: 65.6%, BigInt: 34.4% out of 131 sampled entries
   */
  amount_capturable: JsonValueSchema.nullable(),
  /**
   * Multiple data types found: Int: 65.6%, BigInt: 34.4% out of 131 sampled entries
   */
  amount_received: JsonValueSchema.nullable(),
  authorization_reference: z.string(),
  cdrId: z.string().nullish(),
  createdAt: z.date(),
  energy_price: z.number().nullish(),
  evseId: z.string().nullish(),
  invoiceMail: z.string().nullish(),
  locationId: z.string().nullish(),
  min_kwh_in_kwh: z.number().nullish(),
  /**
   * Multiple data types found: Int: 91.5%, BigInt: 8.5% out of 94 sampled entries
   */
  min_time_in_min: JsonValueSchema,
  /**
   * Multiple data types found: Float: 21.8%, Int: 78.2% out of 110 sampled entries
   */
  parking_price: JsonValueSchema,
  sessionId: z.string().nullish(),
  /**
   * Multiple data types found: Float: 21.8%, Int: 78.2% out of 110 sampled entries
   */
  session_fee: JsonValueSchema,
  session_start_token_uid: z.string(),
  status: z.string(),
  /**
   * Multiple data types found: Float: 21.8%, Int: 78.2% out of 110 sampled entries
   */
  tax_rate: JsonValueSchema,
  updatedAt: z.date(),
})

export type PaymentIntent = z.infer<typeof PaymentIntentSchema>

/////////////////////////////////////////
// PAYMENT INTENT PARTIAL SCHEMA
/////////////////////////////////////////

export const PaymentIntentPartialSchema = PaymentIntentSchema.partial()

export type PaymentIntentPartial = z.infer<typeof PaymentIntentPartialSchema>

// PAYMENT INTENT OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const PaymentIntentOptionalDefaultsSchema = PaymentIntentSchema.merge(z.object({
}))

export type PaymentIntentOptionalDefaults = z.infer<typeof PaymentIntentOptionalDefaultsSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  id: z.string(),
  auth_method: z.string(),
  authorization_reference: z.string(),
  connector_id: z.string(),
  country_code: z.string(),
  currency: z.string(),
  end_date_time: z.string().nullish(),
  evse_uid: z.string(),
  kwh: z.number(),
  last_updated: z.string(),
  location_id: z.string(),
  party_id: z.string(),
  start_date_time: z.string(),
  status: z.string(),
})

export type Session = z.infer<typeof SessionSchema>

/////////////////////////////////////////
// SESSION PARTIAL SCHEMA
/////////////////////////////////////////

export const SessionPartialSchema = SessionSchema.partial()

export type SessionPartial = z.infer<typeof SessionPartialSchema>

// SESSION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const SessionOptionalDefaultsSchema = SessionSchema.merge(z.object({
}))

export type SessionOptionalDefaults = z.infer<typeof SessionOptionalDefaultsSchema>

/////////////////////////////////////////
// MONGODB TYPES
/////////////////////////////////////////
// CDR CDR LOCATION
//------------------------------------------------------


/////////////////////////////////////////
// CDR CDR LOCATION SCHEMA
/////////////////////////////////////////

export const CdrCdrLocationSchema = z.object({
  address: z.string(),
  city: z.string(),
  connector_format: z.string(),
  connector_id: z.string(),
  connector_power_type: z.string(),
  connector_standard: z.string(),
  country: z.string(),
  evse_id: z.string(),
  evse_uid: z.string(),
  id: z.string(),
  name: z.string(),
  postal_code: z.string(),
})

export type CdrCdrLocation = z.infer<typeof CdrCdrLocationSchema>

/////////////////////////////////////////
// CDR CDR LOCATION PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrCdrLocationPartialSchema = CdrCdrLocationSchema.partial()

export type CdrCdrLocationPartial = z.infer<typeof CdrCdrLocationPartialSchema>

// CDR CDR LOCATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrCdrLocationOptionalDefaultsSchema = CdrCdrLocationSchema.merge(z.object({
}))

export type CdrCdrLocationOptionalDefaults = z.infer<typeof CdrCdrLocationOptionalDefaultsSchema>
// CDR CDR LOCATION COORDINATES
//------------------------------------------------------


/////////////////////////////////////////
// CDR CDR LOCATION COORDINATES SCHEMA
/////////////////////////////////////////

export const CdrCdrLocationCoordinatesSchema = z.object({
  latitude: z.string(),
  longitude: z.string(),
})

export type CdrCdrLocationCoordinates = z.infer<typeof CdrCdrLocationCoordinatesSchema>

/////////////////////////////////////////
// CDR CDR LOCATION COORDINATES PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrCdrLocationCoordinatesPartialSchema = CdrCdrLocationCoordinatesSchema.partial()

export type CdrCdrLocationCoordinatesPartial = z.infer<typeof CdrCdrLocationCoordinatesPartialSchema>

// CDR CDR LOCATION COORDINATES OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrCdrLocationCoordinatesOptionalDefaultsSchema = CdrCdrLocationCoordinatesSchema.merge(z.object({
}))

export type CdrCdrLocationCoordinatesOptionalDefaults = z.infer<typeof CdrCdrLocationCoordinatesOptionalDefaultsSchema>
// CDR CDR TOKEN
//------------------------------------------------------


/////////////////////////////////////////
// CDR CDR TOKEN SCHEMA
/////////////////////////////////////////

export const CdrCdrTokenSchema = z.object({
  contract_id: z.string(),
  type: z.string(),
  uid: z.string(),
})

export type CdrCdrToken = z.infer<typeof CdrCdrTokenSchema>

/////////////////////////////////////////
// CDR CDR TOKEN PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrCdrTokenPartialSchema = CdrCdrTokenSchema.partial()

export type CdrCdrTokenPartial = z.infer<typeof CdrCdrTokenPartialSchema>

// CDR CDR TOKEN OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrCdrTokenOptionalDefaultsSchema = CdrCdrTokenSchema.merge(z.object({
}))

export type CdrCdrTokenOptionalDefaults = z.infer<typeof CdrCdrTokenOptionalDefaultsSchema>
// CDR CHARGING PERIODS
//------------------------------------------------------


/////////////////////////////////////////
// CDR CHARGING PERIODS SCHEMA
/////////////////////////////////////////

export const CdrChargingPeriodsSchema = z.object({
  start_date_time: z.string(),
  tariff_id: z.string(),
})

export type CdrChargingPeriods = z.infer<typeof CdrChargingPeriodsSchema>

/////////////////////////////////////////
// CDR CHARGING PERIODS PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrChargingPeriodsPartialSchema = CdrChargingPeriodsSchema.partial()

export type CdrChargingPeriodsPartial = z.infer<typeof CdrChargingPeriodsPartialSchema>

// CDR CHARGING PERIODS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrChargingPeriodsOptionalDefaultsSchema = CdrChargingPeriodsSchema.merge(z.object({
}))

export type CdrChargingPeriodsOptionalDefaults = z.infer<typeof CdrChargingPeriodsOptionalDefaultsSchema>
// CDR CHARGING PERIODS DIMENSIONS
//------------------------------------------------------


/////////////////////////////////////////
// CDR CHARGING PERIODS DIMENSIONS SCHEMA
/////////////////////////////////////////

export const CdrChargingPeriodsDimensionsSchema = z.object({
  type: z.string(),
  /**
   * Multiple data types found: Float: 39.6%, Int: 60.4% out of 48 sampled entries
   */
  volume: JsonValueSchema.nullable(),
})

export type CdrChargingPeriodsDimensions = z.infer<typeof CdrChargingPeriodsDimensionsSchema>

/////////////////////////////////////////
// CDR CHARGING PERIODS DIMENSIONS PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrChargingPeriodsDimensionsPartialSchema = CdrChargingPeriodsDimensionsSchema.partial()

export type CdrChargingPeriodsDimensionsPartial = z.infer<typeof CdrChargingPeriodsDimensionsPartialSchema>

// CDR CHARGING PERIODS DIMENSIONS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrChargingPeriodsDimensionsOptionalDefaultsSchema = CdrChargingPeriodsDimensionsSchema.merge(z.object({
}))

export type CdrChargingPeriodsDimensionsOptionalDefaults = z.infer<typeof CdrChargingPeriodsDimensionsOptionalDefaultsSchema>
// CDR TARIFFS
//------------------------------------------------------


/////////////////////////////////////////
// CDR TARIFFS SCHEMA
/////////////////////////////////////////

export const CdrTariffsSchema = z.object({
  country_code: z.string(),
  currency: z.string(),
  id: z.string(),
  last_updated: z.string(),
  party_id: z.string(),
  type: z.string(),
})

export type CdrTariffs = z.infer<typeof CdrTariffsSchema>

/////////////////////////////////////////
// CDR TARIFFS PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTariffsPartialSchema = CdrTariffsSchema.partial()

export type CdrTariffsPartial = z.infer<typeof CdrTariffsPartialSchema>

// CDR TARIFFS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTariffsOptionalDefaultsSchema = CdrTariffsSchema.merge(z.object({
}))

export type CdrTariffsOptionalDefaults = z.infer<typeof CdrTariffsOptionalDefaultsSchema>
// CDR TARIFFS ELEMENTS
//------------------------------------------------------


/////////////////////////////////////////
// CDR TARIFFS ELEMENTS SCHEMA
/////////////////////////////////////////

export const CdrTariffsElementsSchema = z.object({
})

export type CdrTariffsElements = z.infer<typeof CdrTariffsElementsSchema>

/////////////////////////////////////////
// CDR TARIFFS ELEMENTS PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTariffsElementsPartialSchema = CdrTariffsElementsSchema.partial()

export type CdrTariffsElementsPartial = z.infer<typeof CdrTariffsElementsPartialSchema>

// CDR TARIFFS ELEMENTS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTariffsElementsOptionalDefaultsSchema = CdrTariffsElementsSchema.merge(z.object({
}))

export type CdrTariffsElementsOptionalDefaults = z.infer<typeof CdrTariffsElementsOptionalDefaultsSchema>
// CDR TARIFFS ELEMENTS PRICE COMPONENTS
//------------------------------------------------------


/////////////////////////////////////////
// CDR TARIFFS ELEMENTS PRICE COMPONENTS SCHEMA
/////////////////////////////////////////

export const CdrTariffsElementsPriceComponentsSchema = z.object({
  /**
   * Multiple data types found: Float: 50%, Int: 50% out of 34 sampled entries
   */
  price: JsonValueSchema.nullable(),
  step_size: z.number(),
  type: z.string(),
})

export type CdrTariffsElementsPriceComponents = z.infer<typeof CdrTariffsElementsPriceComponentsSchema>

/////////////////////////////////////////
// CDR TARIFFS ELEMENTS PRICE COMPONENTS PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTariffsElementsPriceComponentsPartialSchema = CdrTariffsElementsPriceComponentsSchema.partial()

export type CdrTariffsElementsPriceComponentsPartial = z.infer<typeof CdrTariffsElementsPriceComponentsPartialSchema>

// CDR TARIFFS ELEMENTS PRICE COMPONENTS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTariffsElementsPriceComponentsOptionalDefaultsSchema = CdrTariffsElementsPriceComponentsSchema.merge(z.object({
}))

export type CdrTariffsElementsPriceComponentsOptionalDefaults = z.infer<typeof CdrTariffsElementsPriceComponentsOptionalDefaultsSchema>
// CDR TARIFFS TARIFF ALT TEXT
//------------------------------------------------------


/////////////////////////////////////////
// CDR TARIFFS TARIFF ALT TEXT SCHEMA
/////////////////////////////////////////

export const CdrTariffsTariffAltTextSchema = z.object({
  language: z.string(),
  text: z.string(),
})

export type CdrTariffsTariffAltText = z.infer<typeof CdrTariffsTariffAltTextSchema>

/////////////////////////////////////////
// CDR TARIFFS TARIFF ALT TEXT PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTariffsTariffAltTextPartialSchema = CdrTariffsTariffAltTextSchema.partial()

export type CdrTariffsTariffAltTextPartial = z.infer<typeof CdrTariffsTariffAltTextPartialSchema>

// CDR TARIFFS TARIFF ALT TEXT OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTariffsTariffAltTextOptionalDefaultsSchema = CdrTariffsTariffAltTextSchema.merge(z.object({
}))

export type CdrTariffsTariffAltTextOptionalDefaults = z.infer<typeof CdrTariffsTariffAltTextOptionalDefaultsSchema>
// CDR TOTAL COST
//------------------------------------------------------


/////////////////////////////////////////
// CDR TOTAL COST SCHEMA
/////////////////////////////////////////

export const CdrTotalCostSchema = z.object({
  /**
   * Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
   */
  excl_vat: JsonValueSchema.nullable(),
})

export type CdrTotalCost = z.infer<typeof CdrTotalCostSchema>

/////////////////////////////////////////
// CDR TOTAL COST PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTotalCostPartialSchema = CdrTotalCostSchema.partial()

export type CdrTotalCostPartial = z.infer<typeof CdrTotalCostPartialSchema>

// CDR TOTAL COST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTotalCostOptionalDefaultsSchema = CdrTotalCostSchema.merge(z.object({
}))

export type CdrTotalCostOptionalDefaults = z.infer<typeof CdrTotalCostOptionalDefaultsSchema>
// CDR TOTAL ENERGY COST
//------------------------------------------------------


/////////////////////////////////////////
// CDR TOTAL ENERGY COST SCHEMA
/////////////////////////////////////////

export const CdrTotalEnergyCostSchema = z.object({
  /**
   * Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
   */
  excl_vat: JsonValueSchema.nullable(),
})

export type CdrTotalEnergyCost = z.infer<typeof CdrTotalEnergyCostSchema>

/////////////////////////////////////////
// CDR TOTAL ENERGY COST PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTotalEnergyCostPartialSchema = CdrTotalEnergyCostSchema.partial()

export type CdrTotalEnergyCostPartial = z.infer<typeof CdrTotalEnergyCostPartialSchema>

// CDR TOTAL ENERGY COST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTotalEnergyCostOptionalDefaultsSchema = CdrTotalEnergyCostSchema.merge(z.object({
}))

export type CdrTotalEnergyCostOptionalDefaults = z.infer<typeof CdrTotalEnergyCostOptionalDefaultsSchema>
// CDR TOTAL FIXED COST
//------------------------------------------------------


/////////////////////////////////////////
// CDR TOTAL FIXED COST SCHEMA
/////////////////////////////////////////

export const CdrTotalFixedCostSchema = z.object({
  /**
   * Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
   */
  excl_vat: JsonValueSchema.nullable(),
})

export type CdrTotalFixedCost = z.infer<typeof CdrTotalFixedCostSchema>

/////////////////////////////////////////
// CDR TOTAL FIXED COST PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTotalFixedCostPartialSchema = CdrTotalFixedCostSchema.partial()

export type CdrTotalFixedCostPartial = z.infer<typeof CdrTotalFixedCostPartialSchema>

// CDR TOTAL FIXED COST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTotalFixedCostOptionalDefaultsSchema = CdrTotalFixedCostSchema.merge(z.object({
}))

export type CdrTotalFixedCostOptionalDefaults = z.infer<typeof CdrTotalFixedCostOptionalDefaultsSchema>
// CDR TOTAL PARKING COST
//------------------------------------------------------


/////////////////////////////////////////
// CDR TOTAL PARKING COST SCHEMA
/////////////////////////////////////////

export const CdrTotalParkingCostSchema = z.object({
  /**
   * Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
   */
  excl_vat: JsonValueSchema.nullable(),
})

export type CdrTotalParkingCost = z.infer<typeof CdrTotalParkingCostSchema>

/////////////////////////////////////////
// CDR TOTAL PARKING COST PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTotalParkingCostPartialSchema = CdrTotalParkingCostSchema.partial()

export type CdrTotalParkingCostPartial = z.infer<typeof CdrTotalParkingCostPartialSchema>

// CDR TOTAL PARKING COST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTotalParkingCostOptionalDefaultsSchema = CdrTotalParkingCostSchema.merge(z.object({
}))

export type CdrTotalParkingCostOptionalDefaults = z.infer<typeof CdrTotalParkingCostOptionalDefaultsSchema>
// CDR TOTAL TIME COST
//------------------------------------------------------


/////////////////////////////////////////
// CDR TOTAL TIME COST SCHEMA
/////////////////////////////////////////

export const CdrTotalTimeCostSchema = z.object({
  /**
   * Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
   */
  excl_vat: JsonValueSchema.nullable(),
})

export type CdrTotalTimeCost = z.infer<typeof CdrTotalTimeCostSchema>

/////////////////////////////////////////
// CDR TOTAL TIME COST PARTIAL SCHEMA
/////////////////////////////////////////

export const CdrTotalTimeCostPartialSchema = CdrTotalTimeCostSchema.partial()

export type CdrTotalTimeCostPartial = z.infer<typeof CdrTotalTimeCostPartialSchema>

// CDR TOTAL TIME COST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CdrTotalTimeCostOptionalDefaultsSchema = CdrTotalTimeCostSchema.merge(z.object({
}))

export type CdrTotalTimeCostOptionalDefaults = z.infer<typeof CdrTotalTimeCostOptionalDefaultsSchema>
// INVOICE FILES
//------------------------------------------------------


/////////////////////////////////////////
// INVOICE FILES SCHEMA
/////////////////////////////////////////

export const InvoiceFilesSchema = z.object({
  application_type: z.string(),
  create_date: z.date(),
  kind_of_file: z.string(),
  name: z.string(),
  path: z.string(),
})

export type InvoiceFiles = z.infer<typeof InvoiceFilesSchema>

/////////////////////////////////////////
// INVOICE FILES PARTIAL SCHEMA
/////////////////////////////////////////

export const InvoiceFilesPartialSchema = InvoiceFilesSchema.partial()

export type InvoiceFilesPartial = z.infer<typeof InvoiceFilesPartialSchema>

// INVOICE FILES OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const InvoiceFilesOptionalDefaultsSchema = InvoiceFilesSchema.merge(z.object({
}))

export type InvoiceFilesOptionalDefaults = z.infer<typeof InvoiceFilesOptionalDefaultsSchema>
// INVOICE INVOICE POSITIONS
//------------------------------------------------------


/////////////////////////////////////////
// INVOICE INVOICE POSITIONS SCHEMA
/////////////////////////////////////////

export const InvoiceInvoicePositionsSchema = z.object({
  amount: z.number(),
  description: z.string(),
  position: z.bigint(),
  sum_gross: z.number(),
  sum_net: z.number(),
  sum_tax: z.number(),
  tax_rate: z.number(),
  title: z.string(),
  unit: z.string(),
  unit_price: z.number(),
  unit_price_gross: z.number(),
})

export type InvoiceInvoicePositions = z.infer<typeof InvoiceInvoicePositionsSchema>

/////////////////////////////////////////
// INVOICE INVOICE POSITIONS PARTIAL SCHEMA
/////////////////////////////////////////

export const InvoiceInvoicePositionsPartialSchema = InvoiceInvoicePositionsSchema.partial()

export type InvoiceInvoicePositionsPartial = z.infer<typeof InvoiceInvoicePositionsPartialSchema>

// INVOICE INVOICE POSITIONS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const InvoiceInvoicePositionsOptionalDefaultsSchema = InvoiceInvoicePositionsSchema.merge(z.object({
}))

export type InvoiceInvoicePositionsOptionalDefaults = z.infer<typeof InvoiceInvoicePositionsOptionalDefaultsSchema>
// INVOICE METADATA
//------------------------------------------------------


/////////////////////////////////////////
// INVOICE METADATA SCHEMA
/////////////////////////////////////////

export const InvoiceMetadataSchema = z.object({
  paymentIntent: z.string(),
})

export type InvoiceMetadata = z.infer<typeof InvoiceMetadataSchema>

/////////////////////////////////////////
// INVOICE METADATA PARTIAL SCHEMA
/////////////////////////////////////////

export const InvoiceMetadataPartialSchema = InvoiceMetadataSchema.partial()

export type InvoiceMetadataPartial = z.infer<typeof InvoiceMetadataPartialSchema>

// INVOICE METADATA OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const InvoiceMetadataOptionalDefaultsSchema = InvoiceMetadataSchema.merge(z.object({
}))

export type InvoiceMetadataOptionalDefaults = z.infer<typeof InvoiceMetadataOptionalDefaultsSchema>
// LOCATION COORDINATES
//------------------------------------------------------


/////////////////////////////////////////
// LOCATION COORDINATES SCHEMA
/////////////////////////////////////////

export const LocationCoordinatesSchema = z.object({
  latitude: z.string(),
  longitude: z.string(),
})

export type LocationCoordinates = z.infer<typeof LocationCoordinatesSchema>

/////////////////////////////////////////
// LOCATION COORDINATES PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationCoordinatesPartialSchema = LocationCoordinatesSchema.partial()

export type LocationCoordinatesPartial = z.infer<typeof LocationCoordinatesPartialSchema>

// LOCATION COORDINATES OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationCoordinatesOptionalDefaultsSchema = LocationCoordinatesSchema.merge(z.object({
}))

export type LocationCoordinatesOptionalDefaults = z.infer<typeof LocationCoordinatesOptionalDefaultsSchema>
// LOCATION EVSES
//------------------------------------------------------


/////////////////////////////////////////
// LOCATION EVSES SCHEMA
/////////////////////////////////////////

export const LocationEvsesSchema = z.object({
  capabilities: z.string().array(),
  evse_id: z.string().nullish(),
  last_updated: z.string(),
  physical_reference: z.string(),
  status: z.string(),
  uid: z.string(),
})

export type LocationEvses = z.infer<typeof LocationEvsesSchema>

/////////////////////////////////////////
// LOCATION EVSES PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationEvsesPartialSchema = LocationEvsesSchema.partial()

export type LocationEvsesPartial = z.infer<typeof LocationEvsesPartialSchema>

// LOCATION EVSES OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationEvsesOptionalDefaultsSchema = LocationEvsesSchema.merge(z.object({
}))

export type LocationEvsesOptionalDefaults = z.infer<typeof LocationEvsesOptionalDefaultsSchema>
// LOCATION EVSES CONNECTORS
//------------------------------------------------------


/////////////////////////////////////////
// LOCATION EVSES CONNECTORS SCHEMA
/////////////////////////////////////////

export const LocationEvsesConnectorsSchema = z.object({
  format: z.string(),
  id: z.string(),
  last_updated: z.string(),
  max_amperage: z.number(),
  max_electric_power: z.number(),
  max_voltage: z.number(),
  power_type: z.string(),
  standard: z.string(),
  tariff_ids: z.string().array(),
})

export type LocationEvsesConnectors = z.infer<typeof LocationEvsesConnectorsSchema>

/////////////////////////////////////////
// LOCATION EVSES CONNECTORS PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationEvsesConnectorsPartialSchema = LocationEvsesConnectorsSchema.partial()

export type LocationEvsesConnectorsPartial = z.infer<typeof LocationEvsesConnectorsPartialSchema>

// LOCATION EVSES CONNECTORS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationEvsesConnectorsOptionalDefaultsSchema = LocationEvsesConnectorsSchema.merge(z.object({
}))

export type LocationEvsesConnectorsOptionalDefaults = z.infer<typeof LocationEvsesConnectorsOptionalDefaultsSchema>
// LOCATION EVSES COORDINATES
//------------------------------------------------------


/////////////////////////////////////////
// LOCATION EVSES COORDINATES SCHEMA
/////////////////////////////////////////

export const LocationEvsesCoordinatesSchema = z.object({
  latitude: z.string(),
  longitude: z.string(),
})

export type LocationEvsesCoordinates = z.infer<typeof LocationEvsesCoordinatesSchema>

/////////////////////////////////////////
// LOCATION EVSES COORDINATES PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationEvsesCoordinatesPartialSchema = LocationEvsesCoordinatesSchema.partial()

export type LocationEvsesCoordinatesPartial = z.infer<typeof LocationEvsesCoordinatesPartialSchema>

// LOCATION EVSES COORDINATES OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationEvsesCoordinatesOptionalDefaultsSchema = LocationEvsesCoordinatesSchema.merge(z.object({
}))

export type LocationEvsesCoordinatesOptionalDefaults = z.infer<typeof LocationEvsesCoordinatesOptionalDefaultsSchema>
// LOCATION OPERATOR
//------------------------------------------------------


/////////////////////////////////////////
// LOCATION OPERATOR SCHEMA
/////////////////////////////////////////

export const LocationOperatorSchema = z.object({
  name: z.string(),
})

export type LocationOperator = z.infer<typeof LocationOperatorSchema>

/////////////////////////////////////////
// LOCATION OPERATOR PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationOperatorPartialSchema = LocationOperatorSchema.partial()

export type LocationOperatorPartial = z.infer<typeof LocationOperatorPartialSchema>

// LOCATION OPERATOR OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationOperatorOptionalDefaultsSchema = LocationOperatorSchema.merge(z.object({
}))

export type LocationOperatorOptionalDefaults = z.infer<typeof LocationOperatorOptionalDefaultsSchema>
// LOCATION OWNER
//------------------------------------------------------


/////////////////////////////////////////
// LOCATION OWNER SCHEMA
/////////////////////////////////////////

export const LocationOwnerSchema = z.object({
  name: z.string(),
})

export type LocationOwner = z.infer<typeof LocationOwnerSchema>

/////////////////////////////////////////
// LOCATION OWNER PARTIAL SCHEMA
/////////////////////////////////////////

export const LocationOwnerPartialSchema = LocationOwnerSchema.partial()

export type LocationOwnerPartial = z.infer<typeof LocationOwnerPartialSchema>

// LOCATION OWNER OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const LocationOwnerOptionalDefaultsSchema = LocationOwnerSchema.merge(z.object({
}))

export type LocationOwnerOptionalDefaults = z.infer<typeof LocationOwnerOptionalDefaultsSchema>
// SESSION CDR TOKEN
//------------------------------------------------------


/////////////////////////////////////////
// SESSION CDR TOKEN SCHEMA
/////////////////////////////////////////

export const SessionCdrTokenSchema = z.object({
  contract_id: z.string(),
  type: z.string(),
  uid: z.string(),
})

export type SessionCdrToken = z.infer<typeof SessionCdrTokenSchema>

/////////////////////////////////////////
// SESSION CDR TOKEN PARTIAL SCHEMA
/////////////////////////////////////////

export const SessionCdrTokenPartialSchema = SessionCdrTokenSchema.partial()

export type SessionCdrTokenPartial = z.infer<typeof SessionCdrTokenPartialSchema>

// SESSION CDR TOKEN OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const SessionCdrTokenOptionalDefaultsSchema = SessionCdrTokenSchema.merge(z.object({
}))

export type SessionCdrTokenOptionalDefaults = z.infer<typeof SessionCdrTokenOptionalDefaultsSchema>
// SESSION CHARGING PERIODS
//------------------------------------------------------


/////////////////////////////////////////
// SESSION CHARGING PERIODS SCHEMA
/////////////////////////////////////////

export const SessionChargingPeriodsSchema = z.object({
  start_date_time: z.string(),
  tariff_id: z.string(),
})

export type SessionChargingPeriods = z.infer<typeof SessionChargingPeriodsSchema>

/////////////////////////////////////////
// SESSION CHARGING PERIODS PARTIAL SCHEMA
/////////////////////////////////////////

export const SessionChargingPeriodsPartialSchema = SessionChargingPeriodsSchema.partial()

export type SessionChargingPeriodsPartial = z.infer<typeof SessionChargingPeriodsPartialSchema>

// SESSION CHARGING PERIODS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const SessionChargingPeriodsOptionalDefaultsSchema = SessionChargingPeriodsSchema.merge(z.object({
}))

export type SessionChargingPeriodsOptionalDefaults = z.infer<typeof SessionChargingPeriodsOptionalDefaultsSchema>
// SESSION CHARGING PERIODS DIMENSIONS
//------------------------------------------------------


/////////////////////////////////////////
// SESSION CHARGING PERIODS DIMENSIONS SCHEMA
/////////////////////////////////////////

export const SessionChargingPeriodsDimensionsSchema = z.object({
  type: z.string(),
  volume: z.number(),
})

export type SessionChargingPeriodsDimensions = z.infer<typeof SessionChargingPeriodsDimensionsSchema>

/////////////////////////////////////////
// SESSION CHARGING PERIODS DIMENSIONS PARTIAL SCHEMA
/////////////////////////////////////////

export const SessionChargingPeriodsDimensionsPartialSchema = SessionChargingPeriodsDimensionsSchema.partial()

export type SessionChargingPeriodsDimensionsPartial = z.infer<typeof SessionChargingPeriodsDimensionsPartialSchema>

// SESSION CHARGING PERIODS DIMENSIONS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const SessionChargingPeriodsDimensionsOptionalDefaultsSchema = SessionChargingPeriodsDimensionsSchema.merge(z.object({
}))

export type SessionChargingPeriodsDimensionsOptionalDefaults = z.infer<typeof SessionChargingPeriodsDimensionsOptionalDefaultsSchema>
// SESSION TOTAL COST
//------------------------------------------------------


/////////////////////////////////////////
// SESSION TOTAL COST SCHEMA
/////////////////////////////////////////

export const SessionTotalCostSchema = z.object({
  excl_vat: z.number(),
})

export type SessionTotalCost = z.infer<typeof SessionTotalCostSchema>

/////////////////////////////////////////
// SESSION TOTAL COST PARTIAL SCHEMA
/////////////////////////////////////////

export const SessionTotalCostPartialSchema = SessionTotalCostSchema.partial()

export type SessionTotalCostPartial = z.infer<typeof SessionTotalCostPartialSchema>

// SESSION TOTAL COST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const SessionTotalCostOptionalDefaultsSchema = SessionTotalCostSchema.merge(z.object({
}))

export type SessionTotalCostOptionalDefaults = z.infer<typeof SessionTotalCostOptionalDefaultsSchema>
