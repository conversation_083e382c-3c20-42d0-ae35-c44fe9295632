import { PrismaClient, Role } from '@prisma/client';

const prisma = new PrismaClient();

async function createMitarbeiterUserGroups() {
  console.log('🚀 Starting creation of "Mitarbeiter" UserGroups...');

  try {
    // 1. Find all OUs that have at least one CARD_HOLDER user
    const ousWithCardHolders = await prisma.ou.findMany({
      where: {
        User: {
          some: {
            role: Role.CARD_HOLDER,
          },
        },
        deleted: null,
        hide: false,
      },
      include: {
        User: {
          where: {
            role: Role.CARD_HOLDER,
          },
          select: {
            id: true,
            email: true,
            userGroupId: true,
          },
        },
        CompanyTarif: {
          select: {
            id: true,
            name: true,
          },
        },
        UserGroups: {
          where: {
            name: '<PERSON><PERSON>bei<PERSON>',
          },
          select: {
            id: true,
          },
        },
      },
    });

    console.log(`📊 Found ${ousWithCardHolders.length} OUs with CARD_HOLDER users`);

    let createdUserGroups = 0;
    let assignedUsers = 0;
    let assignedTarifs = 0;

    for (const ou of ousWithCardHolders) {
      console.log(`\n🏢 Processing OU: ${ou.name} (${ou.id})`);
      
      // Check if "Mitarbeiter" UserGroup already exists for this OU
      let mitarbeiterGroup = ou.UserGroups.find(ug => ug.id);
      
      if (!mitarbeiterGroup) {
        // Create "Mitarbeiter" UserGroup for this OU
        mitarbeiterGroup = await prisma.userGroup.create({
          data: {
            name: 'Mitarbeiter',
            description: 'Automatisch erstellte Nutzergruppe für Mitarbeiter',
            ouId: ou.id,
          },
        });
        
        createdUserGroups++;
        console.log(`  ✅ Created "Mitarbeiter" UserGroup: ${mitarbeiterGroup.id}`);
      } else {
        console.log(`  ℹ️  "Mitarbeiter" UserGroup already exists`);
      }

      // 2. Assign all CARD_HOLDER users without UserGroup to "Mitarbeiter"
      const usersToAssign = ou.User.filter(user => !user.userGroupId);
      
      if (usersToAssign.length > 0) {
        const userIds = usersToAssign.map(user => user.id);
        
        await prisma.user.updateMany({
          where: {
            id: { in: userIds },
          },
          data: {
            userGroupId: mitarbeiterGroup.id,
          },
        });
        
        assignedUsers += usersToAssign.length;
        console.log(`  👥 Assigned ${usersToAssign.length} CARD_HOLDER users to "Mitarbeiter"`);
        
        // Log assigned users
        usersToAssign.forEach(user => {
          console.log(`    - ${user.email}`);
        });
      } else {
        console.log(`  ℹ️  No unassigned CARD_HOLDER users found`);
      }

      // 3. Assign all CompanyTarifs from this OU to "Mitarbeiter" UserGroup
      if (ou.CompanyTarif.length > 0) {
        // Check which tarifs are not yet assigned to this UserGroup
        const existingAssignments = await prisma.companyTarifOnUserGroup.findMany({
          where: {
            userGroupId: mitarbeiterGroup.id,
          },
          select: {
            tarifId: true,
          },
        });
        
        const existingTarifIds = existingAssignments.map(a => a.tarifId);
        const tarifsToAssign = ou.CompanyTarif.filter(tarif => !existingTarifIds.includes(tarif.id));
        
        if (tarifsToAssign.length > 0) {
          await prisma.companyTarifOnUserGroup.createMany({
            data: tarifsToAssign.map(tarif => ({
              tarifId: tarif.id,
              userGroupId: mitarbeiterGroup.id,
            })),
          });
          
          assignedTarifs += tarifsToAssign.length;
          console.log(`  💰 Assigned ${tarifsToAssign.length} CompanyTarifs to "Mitarbeiter"`);
          
          // Log assigned tarifs
          tarifsToAssign.forEach(tarif => {
            console.log(`    - ${tarif.name}`);
          });
        } else {
          console.log(`  ℹ️  All CompanyTarifs already assigned to "Mitarbeiter"`);
        }
      } else {
        console.log(`  ℹ️  No CompanyTarifs found for this OU`);
      }
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log(`📈 Summary:`);
    console.log(`  - Created UserGroups: ${createdUserGroups}`);
    console.log(`  - Assigned Users: ${assignedUsers}`);
    console.log(`  - Assigned Tarifs: ${assignedTarifs}`);

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  createMitarbeiterUserGroups()
    .then(() => {
      console.log('✅ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

export default createMitarbeiterUserGroups;
