# UserGroup Migration Scripts

## create-mitarbeiter-usergroups.ts

Dieses Skript erstellt automatisch "Mitarbeiter" UserGroups für alle OUs, die mindestens einen CARD_HOLDER User haben, und ordnet diese entsprechend zu.

### Was das Skript macht:

1. **Findet alle OUs** mit mindestens einem CARD_HOLDER User
2. **Erstellt "Mitarbeiter" UserGroup** für jede gefundene OU (falls noch nicht vorhanden)
3. **Ordnet alle CARD_HOLDER Users ohne UserGroup** der "Mitarbeiter" UserGroup zu
4. **Ordnet alle CompanyTarifs der OU** der "Mitarbeiter" UserGroup zu

### Ausführung:

```bash
# Via npm script (empfohlen)
npm run create-mitarbeiter-groups

# Oder direkt mit ts-node
npx ts-node scripts/create-mitarbeiter-usergroups.ts
```

### Sicherheitsfeatures:

- **Idempotent:** Kann mehrfach ausgeführt werden ohne Duplikate zu erstellen
- **Prüfungen:** Überprüft bestehende UserGroups und Zuordnungen
- **Logging:** Detaillierte Ausgabe aller Aktionen
- **Rollback-sicher:** Keine Daten werden gelöscht, nur hinzugefügt

### Beispiel-Ausgabe:

```
🚀 Starting creation of "Mitarbeiter" UserGroups...
📊 Found 3 OUs with CARD_HOLDER users

🏢 Processing OU: Firma A (ou-id-1)
  ✅ Created "Mitarbeiter" UserGroup: usergroup-id-1
  👥 Assigned 5 CARD_HOLDER users to "Mitarbeiter"
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
  💰 Assigned 3 CompanyTarifs to "Mitarbeiter"
    - Standard Tarif
    - Nacht Tarif
    - Wochenende Tarif

🏢 Processing OU: Firma B (ou-id-2)
  ℹ️  "Mitarbeiter" UserGroup already exists
  👥 Assigned 2 CARD_HOLDER users to "Mitarbeiter"
    - <EMAIL>
    - <EMAIL>
  ℹ️  All CompanyTarifs already assigned to "Mitarbeiter"

🎉 Migration completed successfully!
📈 Summary:
  - Created UserGroups: 2
  - Assigned Users: 7
  - Assigned Tarifs: 3
```

### Voraussetzungen:

- Prisma Client muss konfiguriert sein
- Datenbankverbindung muss funktionieren
- ts-node muss installiert sein (ist in devDependencies)

### Nach der Ausführung:

- Alle CARD_HOLDER Users ohne UserGroup haben jetzt die "Mitarbeiter" UserGroup
- Alle CompanyTarifs sind der "Mitarbeiter" UserGroup zugeordnet
- Die UserGroups sind OU-spezifisch und isoliert
- Bestehende Zuordnungen bleiben unverändert
