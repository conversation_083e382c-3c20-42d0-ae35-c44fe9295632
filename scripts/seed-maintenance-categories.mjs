import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const defaultCategories = [
  {
    name: "DGUV-V3",
    description: "Prüfung elektrischer Anlagen und Betriebsmittel nach DGUV Vorschrift 3",
    isDefault: true
  },
  {
    name: "Shut<PERSON>",
    description: "Wartung und Reparatur von Ladekabel-Shuttern",
    isDefault: true
  },
  {
    name: "RFID",
    description: "RFID-Leser Wartung und Kalibrierung",
    isDefault: true
  },
  {
    name: "Ethernet",
    description: "Netzwerk- und Kommunikationskomponenten",
    isDefault: true
  },
  {
    name: "Sonstiges",
    description: "Allgemeine Wartungs- und Reparaturarbeiten",
    isDefault: true
  }
];

async function seedMaintenanceCategories() {
  console.log('Seeding maintenance categories...');

  try {
    for (const category of defaultCategories) {
      const existingCategory = await prisma.maintenanceCategory.findUnique({
        where: { name: category.name }
      });

      if (!existingCategory) {
        await prisma.maintenanceCategory.create({
          data: category
        });
        console.log(`✓ Created category: ${category.name}`);
      } else {
        console.log(`- Category already exists: ${category.name}`);
      }
    }

    console.log('✓ Maintenance categories seeding completed');
  } catch (error) {
    console.error('Error seeding maintenance categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seedMaintenanceCategories()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
