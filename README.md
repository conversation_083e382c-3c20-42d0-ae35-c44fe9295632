# Eulektro App  (NICHT MEHR GÜLTIG)























Der deploy der eulektro app kann über den internen Jenkins unter
http://jenkins.zeitgleich.internal:8080/view/Eulektro/job/Deploy%20and%20build%20onsite/
gestartet werden.
Dadurch wird allerdings immer nur die master branch deployed.

Um manuell zu deployen / bauen kann man sich per ssh auf dem zeitgleich .de Server einloggen.

Dort liegt der Sourcecode in

    /var/www/vhosts/eulektro.de/app.eulektro.de2/eulektro_app

.

> ** Bitte alle folgenden Befehle IMMER als user "eulektro" durchführen (su eulektro) **

## checkout

Auschecken kann normal per git Befehl durchgeführt werden, es ist für den user eulektro ein deploy Token mit lesezugriff
hinterlegt (siehe Passbolt)

z.B.

    "git checkout feature/blah"

## build

Ein build kann einfach durch das starten des  "build_app.sh" Skriptes gestartet weren (inklusive migrations).

    "./build_app.sh"

## service

Der laufende service kann per

    "sudo /bin/systemctl restart eulektro.service"

neugestartet werden.

! Für den build und den service werden die env Variablen aus
/var/www/vhosts/eulektro.de/app.eulektro.de2/.env.prod (build)
und
/var/www/vhosts/eulektro.de/app.eulektro.de2/env.prod (service)
geladen.
Sollten sich Variablen ändern dann müssen sie hier angepasst werden.

## Debug

    "journalctl -u eulektro"

Shift G

## Debug TestApp

    "journalctl -f -u eulektro-test.service"
