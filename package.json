{"name": "eulektro_app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build && quirrel ci", "nextDev": "next dev", "nextDev 0.0.0.0": "next dev -H 0.0.0.0", "dev": "concurrently 'next dev' 'quirrel'", "quirrel": "quirrel", "postinstall": "prisma generate", "migrateDev": "prisma migrate dev", "preview-feature": "prisma migrate dev --preview-feature", "lint": "next lint", "start": "next start", "seed": "npx prisma db seed", "mongo": "npx prisma generate --schema=./prismaMongoAdhoc/schema.prisma", "create-mitarbeiter-groups": "node scripts/create-mitarbeiter-usergroups.mjs", "fix-usergroup-tarif-limits": "node scripts/fix-usergroup-tarif-limits.mjs", "seed-maintenance-categories": "node scripts/seed-maintenance-categories.mjs"}, "prisma": {"seed": "env-cmd -f .env prisma/seed.sh"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.20.0", "@stripe/react-stripe-js": "^2.3.0", "@stripe/stripe-js": "^2.1.5", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^4.20.9", "@trpc/client": "^10.19.1", "@trpc/next": "^10.19.1", "@trpc/react-query": "^10.5.0", "@trpc/server": "^10.5.0", "@types/he": "^1.2.3", "adm-zip": "^0.5.10", "ag-grid-community": "27.3.0", "ag-grid-enterprise": "27.3.0", "ag-grid-react": "27.3.0", "apexcharts": "^3.37.1", "axios": "^1.6.8", "coherentpdf": "^2.5.5", "concurrently": "^7.6.0", "csv-generate": "^4.2.2", "csv-parse": "^5.5.6", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "cuid": "^3.0.0", "echarts": "^5.6.0", "echarts-for-react": "3.0.2", "encoding": "^0.1.13", "env-cmd": "^10.1.0", "fs": "^0.0.1-security", "fs-extra": "^11.1.1", "he": "^1.2.0", "jest": "^29.7.0", "jimp": "^0.22.8", "luxon": "^3.3.0", "next": "14.2.25", "next-auth": "^4.24.7", "nodemailer": "^6.9.1", "original-fs": "^1.2.0", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfkit": "^0.15.0", "prisma": "^5.20.0", "qrcode": "^1.5.3", "quirrel": "^1.14.1", "react": "18.2.0", "react-apexcharts": "^1.4.0", "react-datepicker": "^4.10.0", "react-dom": "18.2.0", "react-hook-form": "^7.43.9", "react-select": "^5.8.0", "redis": "^4.6.7", "stripe": "^12.10.0", "superjson": "1.12.2", "swr": "^2.1.5", "tough-cookie": "^4.1.3", "uuid": "^9.0.0", "zod": "^3.23.8", "zod-prisma-types": "^3.1.6", "zustand": "^4.3.9"}, "devDependencies": {"@types/adm-zip": "^0.5.0", "@types/echarts": "^4.9.20", "@types/fs-extra": "^11.0.1", "@types/imap": "^0.8.40", "@types/luxon": "^3.3.0", "@types/mailparser": "^3.4.4", "@types/mysql": "^2.15.21", "@types/node": "^18.11.17", "@types/nodemailer": "^6.4.7", "@types/pdfkit": "0.13.4", "@types/qrcode": "^1.5.1", "@types/react": "^18.2.12", "@types/react-datepicker": "^4.8.0", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.7", "dotenv": "^16.4.5", "eslint": "^8.35.0", "eslint-config-next": "13.4.9", "generate-password": "^1.7.0", "imap": "^0.8.19", "mailparser": "^3.7.1", "mysql": "^2.18.1", "pdfjs-dist": "^4.2.67", "postcss": "^8.4.25", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "react-icons": "^5.0.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.1.6", "zod-prisma-types": "^3.1.6"}}