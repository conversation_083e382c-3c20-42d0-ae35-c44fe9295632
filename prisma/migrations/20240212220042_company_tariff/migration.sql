/*
  Warnings:

  - You are about to drop the column `tariffType` on the `Tarif` table. All the data in the column will be lost.
  - You are about to drop the `TarifOnEMPCard` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `TarifOnEMPCard` DROP FOREIGN KEY `TarifOnEMPCard_empCardId_fkey`;

-- DropForeignKey
ALTER TABLE `TarifOnEMPCard` DROP FOREIGN KEY `TarifOnEMPCard_tarifId_fkey`;

-- AlterTable
ALTER TABLE `Tarif` DROP COLUMN `tariffType`;

-- DropTable
DROP TABLE `TarifOnEMPCard`;

-- CreateTable
CREATE TABLE `CompanyTarifOnContacts` (
    `tarifId` VARCHAR(191) NOT NULL,
    `contactId` VARCHAR(191) NOT NULL,

    INDEX `CompanyTarifOnContacts_contactId_fkey`(`contactId`),
    PRIMARY KEY (`tarifId`, `contactId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompanyTarif` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `energyPrice` FLOAT NULL,
    `sessionPrice` FLOAT NULL,
    `minChargingTime` FLOAT NOT NULL,
    `minChargingEnergy` FLOAT NOT NULL,
    `blockingFee` FLOAT NOT NULL DEFAULT 0,
    `blockingFeeBeginAtMin` SMALLINT UNSIGNED NOT NULL DEFAULT 0,
    `blockingFeeMax` FLOAT NOT NULL DEFAULT 0,
    `currentType` VARCHAR(11) NULL,
    `validFrom` DATE NOT NULL,
    `validTo` DATE NOT NULL,
    `optional` BOOLEAN NOT NULL DEFAULT false,
    `description` VARCHAR(200) NULL,

    UNIQUE INDEX `CompanyTarif_id_key`(`id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompanyTarifOnEMPCard` (
    `tarifId` VARCHAR(191) NOT NULL,
    `empCardId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`tarifId`, `empCardId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnContacts` ADD CONSTRAINT `CompanyTarifOnContacts_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnContacts` ADD CONSTRAINT `CompanyTarifOnContacts_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `CompanyTarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnEMPCard` ADD CONSTRAINT `CompanyTarifOnEMPCard_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `CompanyTarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnEMPCard` ADD CONSTRAINT `CompanyTarifOnEMPCard_empCardId_fkey` FOREIGN KEY (`empCardId`) REFERENCES `EMPCard`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
