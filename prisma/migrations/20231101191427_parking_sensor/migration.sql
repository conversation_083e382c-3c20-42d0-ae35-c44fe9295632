/*
  Warnings:

  - The primary key for the `ParkingSensor` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `ParkingSensor` table. The data in that column could be lost. The data in that column will be cast from `UnsignedInt` to `VarChar(191)`.
  - You are about to drop the `Parking` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[device_id]` on the table `ParkingSensor` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `device_id` to the `ParkingSensor` table without a default value. This is not possible if the table is not empty.
  - Made the column `name` on table `ParkingSensor` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `ParkingSensor` DROP PRIMARY KEY,
    ADD COLUMN `device_id` BIGINT NOT NULL,
    ADD COLUMN `evseId` VARCHAR(191) NULL,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `name` VARCHAR(191) NOT NULL DEFAULT 'auto_created',
    ADD PRIMARY KEY (`id`);

-- DropTable
DROP TABLE `Parking`;

-- CreateTable
CREATE TABLE `ParkingEvent` (
    `device_id` BIGINT NOT NULL,
    `timestamp` DATETIME(0) NOT NULL,
    `state` BOOLEAN NULL,
    `battery` TINYINT UNSIGNED NULL,
    `sensorTime` DATETIME(0) NULL,

    PRIMARY KEY (`device_id`, `timestamp`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `ParkingSensor_device_id_key` ON `ParkingSensor`(`device_id`);

-- AddForeignKey
ALTER TABLE `ParkingSensor` ADD CONSTRAINT `ParkingSensor_evseId_fkey` FOREIGN KEY (`evseId`) REFERENCES `Evse`(`uid`) ON DELETE SET NULL ON UPDATE CASCADE;
