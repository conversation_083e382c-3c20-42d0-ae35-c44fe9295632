-- CreateTable
CREATE TABLE `PayoneSession` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `<PERSON><PERSON>enart` VARCHAR(191) NULL,
    `Zahlref.-Nr.` INTEGER NOT NULL,
    `VU ZE` VARCHAR(191) NULL,
    `Kundennr ZE` VARCHAR(191) NULL,
    `Name ZE` VARCHAR(191) NULL,
    `PLZ ZE` VARCHAR(191) NULL,
    `Ort ZE` VARCHAR(191) NULL,
    `Straße ZE` VARCHAR(191) NULL,
    `VU` VARCHAR(191) NULL,
    `Kundennr VU` VARCHAR(191) NULL,
    `Name VU` VARCHAR(191) NULL,
    `PLZ VU` VARCHAR(191) NULL,
    `Ort VU` VARCHAR(191) NULL,
    `Straße VU` VARCHAR(191) NULL,
    `MCC Code` INTEGER NOT NULL,
    `<PERSON><PERSON><PERSON>` VARCHAR(191) NULL,
    `<PERSON>.Prod Separate Pricing` VARCHAR(191) NULL,
    `Transaktion` VARCHAR(191) NULL,
    `Terminal-ID` INTEGER NOT NULL,
    `<PERSON>ufdatum` DATETIME(3) NOT NULL,
    `Kaufdatum Uhrzeit` VARCHAR(191) NULL,
    `Processing Datum` DATETIME(3) NOT NULL,
    `Kartennr.` VARCHAR(191) NULL,
    `Währung` VARCHAR(191) NULL,
    `Betrag TXN` DOUBLE NOT NULL,
    `CashBack Betrag` VARCHAR(191) NULL,
    `Anzahl` VARCHAR(191) NULL,
    `Text Summe` VARCHAR(191) NULL,
    `Summe Serv.Geb.` VARCHAR(191) NULL,
    `Summe TXN-Geb.` VARCHAR(191) NULL,
    `Händlerentgelt in Zahlwährung` DOUBLE NOT NULL,
    `Einzel-IC in Zahlwährung` DOUBLE NOT NULL,
    `Summe Interchange` VARCHAR(191) NULL,
    `Acq. Serv. Geb` VARCHAR(191) NULL,
    `Scheme Fee` VARCHAR(191) NULL,
    `Netto PLV TXN` VARCHAR(191) NULL,
    `Netto Pre Auth` VARCHAR(191) NULL,
    `Netto PLV non TXN` VARCHAR(191) NULL,
    `Sicherheitseinbehalt Betrag` VARCHAR(191) NULL,
    `SicherheitseinbehaltFälligkeit` VARCHAR(191) NULL,
    `Nettobetrag Geb.` VARCHAR(191) NULL,
    `Ust. Geb.` VARCHAR(191) NULL,
    `Bruttobetrag Geb.` VARCHAR(191) NULL,
    `Netto` VARCHAR(191) NULL,
    `Auszahlung an Vertragsnr.` VARCHAR(191) NULL,
    `Bankname` VARCHAR(191) NULL,
    `Konto (IBAN)` VARCHAR(191) NULL,
    `BLZ (BIC)` VARCHAR(191) NULL,
    `Genehmigungsnr.TXN` INTEGER NOT NULL,
    `DCC TXN Betrag` VARCHAR(191) NULL,
    `DCC CurrencyCode` VARCHAR(191) NULL,
    `MerchantReference` VARCHAR(191) NULL,
    `MessageText` VARCHAR(191) NULL,
    `Merchant Voucher No` INTEGER NOT NULL,
    `BSPCode` VARCHAR(191) NULL,
    `CustomerOrderNo` VARCHAR(191) NULL,
    `OrderInvoiceNumber` VARCHAR(191) NULL,
    `PassangerName` VARCHAR(191) NULL,
    `MerchantPurchaseRef` VARCHAR(191) NULL,
    `TravelAgencyCode` VARCHAR(191) NULL,
    `Agency Nr.` VARCHAR(191) NULL,
    `TravelAgencyName` VARCHAR(191) NULL,
    `Ticket Nr.` VARCHAR(191) NULL,
    `Buyer VAT No.` VARCHAR(191) NULL,
    `Umrechnungskurs` VARCHAR(191) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
