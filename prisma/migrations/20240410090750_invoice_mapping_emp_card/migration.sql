/*
  Warnings:

  - You are about to drop the column `oneTimeFeeDefault` on the `Contact` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `CompanyTarif` MODIFY `oneTimeFeePayer` ENUM('USER', 'ADMIN', 'CA<PERSON>_HOLDER', 'CA<PERSON>_MANAGER') NOT NULL DEFAULT 'CARD_HOLDER';

-- AlterTable
ALTER TABLE `Contact` DROP COLUMN `oneTimeFeeDefault`;

-- AlterTable
ALTER TABLE `EMPCard` ADD COLUMN `invoiceId` VARCHAR(191) NULL;

-- AddForeignKey
ALTER TABLE `EMPCard` ADD CONSTRAINT `EMPCard_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
