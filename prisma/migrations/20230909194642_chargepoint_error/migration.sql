-- CreateTable
CREATE TABLE `ChargePointError` (
    `id` VARCHAR(191) NOT NULL,
    `error` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `chargePointId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ChargePointError` ADD CONSTRAINT `ChargePointError_chargePointId_fkey` FOREIGN KEY (`chargePointId`) REFERENCES `ChargePoint`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
