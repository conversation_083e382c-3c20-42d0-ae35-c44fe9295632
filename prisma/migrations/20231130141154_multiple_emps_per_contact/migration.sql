/*
  Warnings:

  - You are about to drop the column `providerCountryId` on the `Contact` table. All the data in the column will be lost.
  - You are about to drop the column `providerId` on the `Contact` table. All the data in the column will be lost.

*/

-- CreateTable
CREATE TABLE `Provider` (
    `providerId` VARCHAR(3) NOT NULL,
    `providerCountryId` VARCHAR(3) NOT NULL,
    `contactId` VARCHAR(191) NULL,

    PRIMARY <PERSON>Y (`providerId`, `providerCountryId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Provider` ADD CONSTRAINT `Provider_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;


INSERT INTO Provider (providerId, providerCountryId, contactId) SELECT providerId, providerCountryId, id FROM Contact WHERE providerId IS NOT NULL AND providerCountryId IS NOT NULL;

-- DropIndex
DROP INDEX `Contact_providerId_key` ON `Contact`;

-- AlterTable
ALTER TABLE `Contact` DROP COLUMN `providerCountryId`,
DROP COLUMN `providerId`;
