/*
  Warnings:

  - A unique constraint covering the columns `[evse_id]` on the table `Evse` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE `ParkingSensor` DROP FOREIGN KEY `ParkingSensor_evseId_fkey`;

-- CreateIndex
CREATE UNIQUE INDEX `Evse_evse_id_key` ON `Evse`(`evse_id`);

-- AddForeignKey
ALTER TABLE `ParkingSensor` ADD CONSTRAINT `ParkingSensor_evseId_fkey` FOREIGN KEY (`evseId`) REFERENCES `Evse`(`evse_id`) ON DELETE SET NULL ON UPDATE CASCADE;
