-- CreateTable
CREATE TABLE `_BillingOUs` (
    `A` VARCHAR(191) NOT NULL,
    `B` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `_BillingOUs_AB_unique`(`A`, `B`),
    INDEX `_BillingOUs_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `_BillingOUs` ADD CONSTRAINT `_BillingOUs_A_fkey` FOREIGN KEY (`A`) REFERENCES `Ou`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_BillingOUs` ADD CONSTRAINT `_BillingOUs_B_fkey` FOREIGN KEY (`B`) REFERENCES `TokenGroup`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
