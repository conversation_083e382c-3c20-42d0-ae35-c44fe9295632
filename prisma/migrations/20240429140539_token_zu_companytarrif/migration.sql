-- CreateTable
CREATE TABLE `CompanyTarifOnToken` (
    `tarifId` VARCHAR(191) NOT NULL,
    `tokenId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`tarifId`, `tokenId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnToken` ADD CONSTRAINT `CompanyTarifOnToken_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `CompanyTarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnToken` ADD CONSTRAINT `CompanyTarifOnToken_tokenId_fkey` FOREIGN KEY (`tokenId`) REFERENCES `Token`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
