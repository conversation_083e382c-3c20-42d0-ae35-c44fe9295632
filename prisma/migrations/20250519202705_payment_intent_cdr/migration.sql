/*
  Warnings:

  - A unique constraint covering the columns `[cdrId]` on the table `PaymentIntent` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE `PaymentIntent` DROP FOREIGN KEY `PaymentIntent_invoiceId_fkey`;

-- AlterTable
ALTER TABLE `PaymentIntent` ADD COLUMN `cdrId` VARCHAR(191) NULL,
    MODIFY `invoiceId` VARCHAR(191) NULL;

-- CreateIndex
CREATE UNIQUE INDEX `PaymentIntent_cdrId_key` ON `PaymentIntent`(`cdrId`);

-- AddForeignKey
ALTER TABLE `PaymentIntent` ADD CONSTRAINT `PaymentIntent_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PaymentIntent` ADD CONSTRAINT `PaymentIntent_cdrId_fkey` FOREIGN KEY (`cdrId`) REFERENCES `Cdr`(`CDR_ID`) ON DELETE SET NULL ON UPDATE CASCADE;
