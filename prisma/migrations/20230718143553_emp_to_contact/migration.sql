

-- Rename Table
RENAME TABLE `Emp` TO `Contact`;

-- Rename Table
RENAME TABLE `EmpAddress` TO `ContactAddress`;

-- Rename Table
RENAME TABLE `EmpEnergyReseller` TO `ContactEnergyReseller`;

-- Rename Table
RENAME TABLE `RoamingOfferOnEmps` TO `RoamingOfferOnContacts`;

-- DropForeignKey
ALTER TABLE `ContactAddress` DROP FOREIGN KEY `EmpAddress_empId_fkey`;

-- DropForeignKey
ALTER TABLE `ContactEnergyReseller` DROP FOREIGN KEY `EmpEnergyReseller_empId_fkey`;

-- DropForeignKey
ALTER TABLE `Invoice` DROP FOREIGN KEY `Invoice_empId_fkey`;

-- DropForeignKey
ALTER TABLE `RoamingOfferOnContacts` DROP FOREIGN KEY `RoamingOfferOnEmps_empId_fkey`;

-- DropForeignKey
ALTER TABLE `RoamingOfferOnContacts` DROP FOREIGN KEY `RoamingOfferOnEmps_roamingOfferId_fkey`;

-- DropForeignKey
ALTER TABLE `TokenGroup` DROP FOREIGN KEY `TokenGroup_empId_fkey`;

-- AlterTable
ALTER TABLE `Invoice`
    CHANGE `empId` `contactId` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `TokenGroup`
    CHANGE `empId` `contactId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `ContactAddress`
    CHANGE `empId` `contactId` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `ContactEnergyReseller`
    CHANGE `empId` `contactId` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `RoamingOfferOnContacts`
    CHANGE `empId` `contactId` VARCHAR(191) NOT NULL;

-- AddForeignKey
ALTER TABLE `ContactAddress` ADD CONSTRAINT `ContactAddress_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ContactEnergyReseller` ADD CONSTRAINT `ContactEnergyReseller_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Invoice` ADD CONSTRAINT `Invoice_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RoamingOfferOnContacts` ADD CONSTRAINT `RoamingOfferOnContacts_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RoamingOfferOnContacts` ADD CONSTRAINT `RoamingOfferOnContacts_roamingOfferId_fkey` FOREIGN KEY (`roamingOfferId`) REFERENCES `RoamingOffer`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `TokenGroup` ADD CONSTRAINT `TokenGroup_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Update the index on the `ContactEnergyReseller` table

DROP INDEX `EmpEnergyReseller_id_key` ON `ContactEnergyReseller`;
CREATE UNIQUE INDEX `EmpEnergyReseller_id_key` ON `ContactEnergyReseller`(`id`);


