/*
  Warnings:

  - The primary key for the `AgGridView` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `AgGridView` table. The data in that column could be lost. The data in that column will be cast from `VarChar(255)` to `VarChar(191)`.
  - A unique constraint covering the columns `[id]` on the table `AgGridView` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `gridId` to the `AgGridView` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `AgGridView` DROP PRIMARY KEY,
    ADD COLUMN `gridId` VARCHAR(255) NOT NULL,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- CreateIndex
CREATE UNIQUE INDEX `AgGridView_id_key` ON `AgGridView`(`id`);
