/*
  Warnings:

  - You are about to drop the `CompanyTarifOnContacts` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `ouId` to the `CompanyTarif` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `CompanyTarifOnContacts` DROP FOREIGN KEY `CompanyTarifOnContacts_contactId_fkey`;

-- DropForeignKey
ALTER TABLE `CompanyTarifOnContacts` DROP FOREIGN KEY `CompanyTarifOnContacts_tarifId_fkey`;

-- AlterTable
ALTER TABLE `CompanyTarif` ADD COLUMN `ouId` VARCHAR(191) NOT NULL;

-- DropTable
DROP TABLE `CompanyTarifOnContacts`;

-- AddForeignKey
ALTER TABLE `CompanyTarif` ADD CONSTRAINT `CompanyTarif_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
