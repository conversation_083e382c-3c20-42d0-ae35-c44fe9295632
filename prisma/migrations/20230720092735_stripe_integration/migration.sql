/*
  Warnings:

  - You are about to drop the `Attachment` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Label` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Tenant` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Transaction` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `Attachment` DROP FOREIGN KEY `Attachment_transactionId_fkey`;

-- DropForeignKey
ALTER TABLE `FileRef` DROP FOREIGN KEY `FileRef_invoiceId_fkey`;

-- DropForeignKey
ALTER TABLE `Label` DROP FOREIGN KEY `Label_transactionId_fkey`;

-- DropForeignKey
ALTER TABLE `Tenant` DROP FOREIGN KEY `Tenant_parentId_fkey`;

-- AlterTable
ALTER TABLE `FileRef` ADD COLUMN `stripePayoutId` VARCHAR(191) NULL,
    MODIFY `invoiceId` VARCHAR(191) NULL;

-- DropTable
DROP TABLE `Attachment`;

-- DropTable
DROP TABLE `Label`;

-- DropTable
DROP TABLE `Tenant`;

-- DropTable
DROP TABLE `Transaction`;

-- CreateTable
CREATE TABLE `StripePayout` (
    `id` VARCHAR(191) NOT NULL,
    `amount` INTEGER NOT NULL,
    `arrival_date` DATETIME(3) NOT NULL,
    `created` DATETIME(3) NOT NULL,
    `automatic` BOOLEAN NOT NULL,
    `status` ENUM('CREATED', 'STRIPE_REPORT_TRIGGERED', 'STRIPE_REPORT_RECEIVED', 'STRIPE_FILE_DOWNLOADED', 'TAX_FILE_GENERATED', 'TAX_FILE_UPLOADED') NOT NULL DEFAULT 'CREATED',

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `StripePayoutItem` (
    `automaticPayoutId` VARCHAR(191) NOT NULL,
    `paymentIntentId` VARCHAR(191) NOT NULL,
    `gross` DOUBLE NOT NULL,
    `net` DOUBLE NOT NULL,
    `fee` DOUBLE NOT NULL,
    `customerFacingAmount` DOUBLE NOT NULL,
    `paymentMetadataEvse` VARCHAR(191) NULL,
    `reportingCategory` VARCHAR(191) NOT NULL,
    `paymentMetadataCdrId` VARCHAR(191) NULL,
    `adhocInvoiceNumber` VARCHAR(191) NULL,

    PRIMARY KEY (`automaticPayoutId`, `paymentIntentId`, `reportingCategory`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QontoTransaction` (
    `transaction_id` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `amount_cents` INTEGER NOT NULL,
    `settled_balance` DOUBLE NOT NULL,
    `settled_balance_cents` INTEGER NOT NULL,
    `local_amount` DOUBLE NOT NULL,
    `local_amount_cents` INTEGER NOT NULL,
    `side` VARCHAR(191) NOT NULL,
    `operation_type` VARCHAR(191) NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `local_currency` VARCHAR(191) NOT NULL,
    `label` VARCHAR(191) NOT NULL,
    `settled_at` DATETIME(3) NOT NULL,
    `emitted_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `note` VARCHAR(191) NULL,
    `reference` VARCHAR(191) NULL,
    `vat_amount` DOUBLE NULL,
    `vat_amount_cents` INTEGER NULL,
    `vat_rate` DOUBLE NULL,
    `initiator_id` VARCHAR(191) NULL,
    `attachment_lost` BOOLEAN NOT NULL,
    `attachment_required` BOOLEAN NOT NULL,
    `card_last_digits` VARCHAR(191) NULL,
    `category` VARCHAR(191) NOT NULL,
    `ignore_for_mapping` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`transaction_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `StripePayoutToQontoTransaction` (
    `stripePayoutId` VARCHAR(191) NOT NULL,
    `qontoTransaction_id` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `StripePayoutToQontoTransaction_stripePayoutId_qontoTransacti_key`(`stripePayoutId`, `qontoTransaction_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `InvoiceToQontoTransaction` (
    `invoiceId` VARCHAR(191) NOT NULL,
    `qontoTransaction_id` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `InvoiceToQontoTransaction_invoiceId_qontoTransaction_id_key`(`invoiceId`, `qontoTransaction_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QontoAttachment` (
    `id` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `file_name` VARCHAR(191) NOT NULL,
    `file_size` VARCHAR(191) NOT NULL,
    `file_content_type` VARCHAR(191) NOT NULL,
    `url` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QontoTransactionAttachment` (
    `transactionId` VARCHAR(191) NOT NULL,
    `attachmentId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`transactionId`, `attachmentId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QontoLabel` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `parent_id` VARCHAR(191) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QontoTransactionLabel` (
    `transactionId` VARCHAR(191) NOT NULL,
    `labelId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`transactionId`, `labelId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QontoProbativeAttachment` (
    `local_id` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `file_name` VARCHAR(191) NOT NULL,
    `file_size` VARCHAR(191) NOT NULL,
    `file_content_type` VARCHAR(191) NOT NULL,
    `url` VARCHAR(191) NOT NULL,
    `attachmentId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `QontoProbativeAttachment_attachmentId_key`(`attachmentId`),
    PRIMARY KEY (`local_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `FileRef` ADD CONSTRAINT `FileRef_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `FileRef` ADD CONSTRAINT `FileRef_stripePayoutId_fkey` FOREIGN KEY (`stripePayoutId`) REFERENCES `StripePayout`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StripePayoutToQontoTransaction` ADD CONSTRAINT `StripePayoutToQontoTransaction_stripePayoutId_fkey` FOREIGN KEY (`stripePayoutId`) REFERENCES `StripePayout`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StripePayoutToQontoTransaction` ADD CONSTRAINT `StripePayoutToQontoTransaction_qontoTransaction_id_fkey` FOREIGN KEY (`qontoTransaction_id`) REFERENCES `QontoTransaction`(`transaction_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `InvoiceToQontoTransaction` ADD CONSTRAINT `InvoiceToQontoTransaction_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `InvoiceToQontoTransaction` ADD CONSTRAINT `InvoiceToQontoTransaction_qontoTransaction_id_fkey` FOREIGN KEY (`qontoTransaction_id`) REFERENCES `QontoTransaction`(`transaction_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `QontoTransactionAttachment` ADD CONSTRAINT `QontoTransactionAttachment_transactionId_fkey` FOREIGN KEY (`transactionId`) REFERENCES `QontoTransaction`(`transaction_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `QontoTransactionAttachment` ADD CONSTRAINT `QontoTransactionAttachment_attachmentId_fkey` FOREIGN KEY (`attachmentId`) REFERENCES `QontoAttachment`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `QontoTransactionLabel` ADD CONSTRAINT `QontoTransactionLabel_transactionId_fkey` FOREIGN KEY (`transactionId`) REFERENCES `QontoTransaction`(`transaction_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `QontoTransactionLabel` ADD CONSTRAINT `QontoTransactionLabel_labelId_fkey` FOREIGN KEY (`labelId`) REFERENCES `QontoLabel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `QontoProbativeAttachment` ADD CONSTRAINT `QontoProbativeAttachment_attachmentId_fkey` FOREIGN KEY (`attachmentId`) REFERENCES `QontoAttachment`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
