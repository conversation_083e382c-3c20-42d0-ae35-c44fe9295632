CREATE TABLE `Tarif` (
                         `id` VARCHAR(191) NOT NULL,
                         `name` VARCHAR(191) NULL,
                         `sessionFee` FLOAT NULL,
                         `kwh` FLOAT NOT NULL,
                         `minChargingTime` FLOAT NOT NULL,
                         `minChargingEnergy` FLOAT NOT NULL,
                         `currentType` VARCHAR(11) NULL,
                         `validFrom` DATE NOT NULL,
                         `validTo` DATE NOT NULL,

                         UNIQUE INDEX `Tarif_id_key`(`id`),
                         UNIQUE INDEX `Tarif_name_key`(`name`),
                         PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE `TarifOnContacts` (
                                   `tarifId` VARCHAR(191) NOT NULL,
                                   `contactId` VARCHAR(191) NOT NULL,

                                   INDEX `TarifOnContacts_contactId_fkey`(`contactId`),
                                   PRIMARY KEY (`tarifId`, `contactId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- rename roamingOfferId
-- ALTER TABLE `Cdr` RENAME COLUMN `roamingOfferId` to `tarifId`;
ALTER TABLE `Cdr` CHANGE COLUMN `roamingOfferId` `tarifId` varchar(191);
-- remove key and index because their names are not matching anymore
ALTER TABLE `Cdr` DROP FOREIGN KEY `Cdr_roamingOfferId_fkey`;
ALTER TABLE `Cdr` DROP INDEX `Cdr_roamingOfferId_fkey`;

-- transfer all data to new table
INSERT INTO `Tarif` SELECT * FROM `RoamingOffer`;
-- set new key and index
ALTER TABLE `Cdr` ADD CONSTRAINT `Cdr_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `Tarif`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;


-- same for TarifsOnContacts
INSERT INTO `TarifOnContacts` SELECT * FROM `RoamingOfferOnContacts`;
ALTER TABLE `TarifOnContacts` ADD CONSTRAINT `TarifOnContacts_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `Tarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `TarifOnContacts` ADD CONSTRAINT `TarifOnContacts_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;



-- Drop keys, update col name and set new keys
ALTER TABLE `InvoicePosition` DROP FOREIGN KEY `InvoicePosition_roamingOfferId_fkey`;
ALTER TABLE `InvoicePosition` DROP INDEX       `InvoicePosition_roamingOfferId_fkey`;

ALTER TABLE `InvoicePosition` CHANGE COLUMN `roamingOfferId` `tarifId` varchar(191);
ALTER TABLE `InvoicePosition` ADD CONSTRAINT `InvoicePosition_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `Tarif`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- finally drop old tables which were replaced

DROP TABLE `RoamingOfferOnContacts`;
DROP TABLE `RoamingOffer`;





