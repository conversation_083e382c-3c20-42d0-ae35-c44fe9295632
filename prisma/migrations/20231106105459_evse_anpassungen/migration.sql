-- DropForeignKey
ALTER TABLE `Evse` DROP FOREIGN KEY `Evse_coordinatesId_fkey`;

-- AlterTable
ALTER TABLE `Evse` MODIFY `capabilities` VARCHAR(191) NULL,
    MODIFY `physical_reference` VARCHAR(191) NULL,
    MODIFY `coordinatesId` VARCHAR(191) NULL;

-- AddForeignKey
ALTER TABLE `Evse` ADD CONSTRAINT `Evse_coordinatesId_fkey` FOREIGN KEY (`coordinatesId`) REFERENCES `Coordinates`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
