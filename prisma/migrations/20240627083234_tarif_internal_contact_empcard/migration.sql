-- DropFore<PERSON><PERSON>ey
ALTER TABLE `EMPCard` DROP FOREIGN KEY `EMPCard_userId_fkey`;

-- AlterTable
ALTER TABLE `CompanyTarif` ADD COLUMN `internal` BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE `EMPCard` ADD COLUMN `contactId` VARCHAR(191) NULL,
    MODIFY `userId` VARCHAR(191) NULL;

-- AddForeignKey
ALTER TABLE `EMPCard` ADD CONSTRAINT `EMPCard_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EMPCard` ADD CONSTRAINT `EMPCard_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
