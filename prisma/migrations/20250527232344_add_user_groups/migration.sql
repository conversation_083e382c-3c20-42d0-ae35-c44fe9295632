-- AlterTable
ALTER TABLE `PhysicalCard` ADD COLUMN `userGroupId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `userGroupId` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `UserGroup` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `ouId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `UserGroup_name_ouId_key`(`name`, `ouId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompanyTarifOnUserGroup` (
    `tarifId` VARCHAR(191) NOT NULL,
    `userGroupId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`tarifId`, `userGroupId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `User` ADD CONSTRAINT `User_userGroupId_fkey` FOREIGN KEY (`userGroupId`) REFERENCES `UserGroup`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PhysicalCard` ADD CONSTRAINT `PhysicalCard_userGroupId_fkey` FOREIGN KEY (`userGroupId`) REFERENCES `UserGroup`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserGroup` ADD CONSTRAINT `UserGroup_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnUserGroup` ADD CONSTRAINT `CompanyTarifOnUserGroup_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `CompanyTarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CompanyTarifOnUserGroup` ADD CONSTRAINT `CompanyTarifOnUserGroup_userGroupId_fkey` FOREIGN KEY (`userGroupId`) REFERENCES `UserGroup`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
