-- AlterTable
ALTER TABLE `Cdr` ADD COLUMN `transactionId` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `EnerchargeSession` (
    `ID` INTEGER NOT NULL,
    `SessionID` VARCHAR(191) NOT NULL,
    `<PERSON><PERSON>ID` INTEGER NOT NULL,
    `KundenID2` INTEGER NOT NULL,
    `ChargePointNR` INTEGER NOT NULL,
    `EVSEID` VARCHAR(191) NOT NULL,
    `Ladetype` INTEGER NOT NULL,
    `Start_Session` DATETIME(3) NOT NULL,
    `End_Session` DATETIME(3) NOT NULL,
    `StartEnergieinhalt` DECIMAL(65, 30) NOT NULL,
    `EndeEnergieinhalt` DECIMAL(65, 30) NOT NULL,
    `Energy_content` DECIMAL(65, 30) NOT NULL,
    `Parktime` INTEGER NOT NULL,
    `Chargetime` INTEGER NOT NULL,
    `Preis` DECIMAL(65, 30) NOT NULL,
    `payment_method_AuswahltId` INTEGER NOT NULL,
    `payment_method_Auswahlt` VARCHAR(191) NOT NULL,
    `Billnr_Payment` INTEGER NOT NULL,
    `Billnr_Gesamt` INTEGER NOT NULL,
    `tariff_model` INTEGER NOT NULL,
    `TA_NR` INTEGER NOT NULL,
    `Payment_Kartennummer` VARCHAR(191) NOT NULL,
    `Payment_Zahlungstyp` VARCHAR(191) NOT NULL,
    `OCPP_TransactionID` BIGINT NOT NULL,
    `Selbstabrechnung` BOOLEAN NOT NULL,
    `Teilstorno_Versuche` INTEGER NOT NULL,
    `PDF_Update_Finish` BOOLEAN NOT NULL,
    `Webcode` VARCHAR(191) NOT NULL,
    `Session_StatusCode` INTEGER NOT NULL,
    `Session_Status` VARCHAR(191) NOT NULL,
    `SessionIDMes` VARCHAR(191) NULL,

    UNIQUE INDEX `EnerchargeSession_SessionID_key`(`SessionID`),
    PRIMARY KEY (`ID`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
