/*
  Warnings:

  - Made the column `name` on table `CompanyTarif` required. This step will fail if there are existing NULL values in that column.
  - Made the column `energyPrice` on table `CompanyTarif` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sessionPrice` on table `CompanyTarif` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `CompanyTarif` MODIFY `name` VARCHAR(191) NOT NULL,
    MODIFY `energyPrice` FLOAT NOT NULL DEFAULT 0,
    MODIFY `sessionPrice` FLOAT NOT NULL DEFAULT 0,
    MODIFY `minChargingTime` FLOAT NOT NULL DEFAULT 0,
    MODIFY `minChargingEnergy` FLOAT NOT NULL DEFAULT 0;
