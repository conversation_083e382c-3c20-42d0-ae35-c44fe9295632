-- CreateTable
CREATE TABLE `EMPCard` (
    `id` VARCHAR(191) NOT NULL,
    `active` BOOLEAN NOT NULL DEFAULT false,
    `deactivated_at` DATETIME(3) NULL,
    `ordered_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `userId` VARCHAR(191) NOT NULL,
    `physicalCardId` VARCHAR(191) NULL,

    UNIQUE INDEX `EMPCard_physicalCardId_key`(`physicalCardId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PhysicalCard` (
    `uid` VARCHAR(191) NOT NULL,
    `number` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `PhysicalCard_number_key`(`number`),
    PRIMARY KEY (`uid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `EMPCard` ADD CONSTRAINT `EMPCard_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EMPCard` ADD CONSTRAINT `EMPCard_physicalCardId_fkey` FOREIGN KEY (`physicalCardId`) REFERENCES `PhysicalCard`(`uid`) ON DELETE SET NULL ON UPDATE CASCADE;
