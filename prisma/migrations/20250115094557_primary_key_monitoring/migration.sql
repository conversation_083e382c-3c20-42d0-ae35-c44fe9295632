/*
  Warnings:

  - The primary key for the `MonitoringEvent` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `MonitoringEvent` table. All the data in the column will be lost.
  - Added the required column `lastevent` to the `MonitoringEvent` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `MonitoringEvent` DROP PRIMARY KEY,
    DROP COLUMN `id`,
    ADD COLUMN `lastevent` INTEGER NOT NULL,
    ADD PRIMARY KEY (`lastevent`, `evseId`);
