/*
  Warnings:

  - A unique constraint covering the columns `[ouId]` on the table `Contact` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `Contact` ADD COLUMN `iban` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `PhysicalCard` ADD COLUMN `cpoId` VARCHAR(191) NULL;

-- <PERSON>ug gerade ziehen
ALTER TABLE Contact DROP FOREIGN KEY Contact_ouId_fkey;
DROP INDEX Contact_ouId_fkey on Contact;
ALTER TABLE `Contact` ADD CONSTRAINT `Contact_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
create unique index Contact_ouId_key
    on Contact (ouId);


-- AddForeignKey
ALTER TABLE `PhysicalCard` ADD CONSTRAINT `PhysicalCard_cpoId_fkey` FOREIGN KEY (`cpoId`) REFERENCES `Contact`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
