/*
  Warnings:

  - You are about to drop the column `number` on the `PhysicalCard` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[visualNumber]` on the table `PhysicalCard` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `visualNumber` to the `PhysicalCard` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX `PhysicalCard_number_key` ON `PhysicalCard`;

-- AlterTable
ALTER TABLE `PhysicalCard` DROP COLUMN `number`,
    ADD COLUMN `visualNumber` VARCHAR(191) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `PhysicalCard_visualNumber_key` ON `PhysicalCard`(`visualNumber`);
