-- CreateTable
CREATE TABLE `CPOContract` (
    `id` VARCHAR(191) NOT NULL,
    `numACCharger` INTEGER NOT NULL DEFAULT 0,
    `numDCCharger` INTEGER NOT NULL DEFAULT 0,
    `numACHotline` INTEGER NOT NULL DEFAULT 0,
    `numDCHotline` INTEGER NOT NULL DEFAULT 0,
    `priceACCharger` DOUBLE NOT NULL DEFAULT 5,
    `priceDCCharger` DOUBLE NOT NULL DEFAULT 10,
    `start` DATETIME(3) NOT NULL,
    `end` DATETIME(3) NOT NULL,
    `contactId` VARCHAR(191) NULL,
    `kwhFeeCent` DOUBLE NOT NULL DEFAULT 3,
    `sessionFeeCent` DOUBLE NOT NULL DEFAULT 20,
    `directPaymentFeePercent` DOUBLE NOT NULL DEFAULT 3.5,
    `adhocPaymentFeePercent` DOUBLE NOT NULL DEFAULT 3.5,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `CPOContract` ADD CONSTRAINT `CPOContract_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
