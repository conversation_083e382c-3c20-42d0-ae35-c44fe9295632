/*
  Warnings:

  - You are about to drop the column `PaidOnDate` on the `Invoice` table. All the data in the column will be lost.
  - You are about to drop the column `ServicePeriodFrom` on the `Invoice` table. All the data in the column will be lost.
  - You are about to drop the column `ServicePeriodTo` on the `Invoice` table. All the data in the column will be lost.
  - You are about to alter the column `financialDetails` on the `Ou` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Json`.
  - Added the required column `selectedOuId` to the `User` table without a default value. This is not possible if the table is not empty.
  - Made the column `email` on table `User` required. This step will fail if there are existing NULL values in that column.
  - Made the column `password` on table `User` required. This step will fail if there are existing NULL values in that column.
  - Made the column `role` on table `User` required. This step will fail if there are existing NULL values in that column.
  - Made the column `ouId` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `User` DROP FOREIGN KEY `User_ouId_fkey`;

-- DropIndex
DROP INDEX `sha1` ON `FileRef`;

-- AlterTable
ALTER TABLE `Emp` ADD COLUMN `cdrMapping` ENUM('Standard', 'EnBW') NOT NULL DEFAULT 'Standard';

-- AlterTable
ALTER TABLE `Invoice` DROP COLUMN `PaidOnDate`,
    DROP COLUMN `ServicePeriodFrom`,
    DROP COLUMN `ServicePeriodTo`,
    ADD COLUMN `paidAmount` DOUBLE NULL,
    ADD COLUMN `paidOnDate` DATE NULL,
    ADD COLUMN `servicePeriodFrom` DATE NULL,
    ADD COLUMN `servicePeriodTo` DATE NULL;

-- AlterTable
ALTER TABLE `Ou` MODIFY `financialDetails` JSON NULL;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `selectedOuId` VARCHAR(191) NOT NULL,
    MODIFY `email` VARCHAR(191) NOT NULL,
    MODIFY `password` VARCHAR(191) NOT NULL,
    MODIFY `role` ENUM('USER', 'ADMIN') NOT NULL DEFAULT 'USER',
    MODIFY `ouId` VARCHAR(191) NOT NULL;

-- CreateTable
CREATE TABLE `Transaction` (
    `transaction_id` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `amount_cents` INTEGER NOT NULL,
    `settled_balance` DOUBLE NOT NULL,
    `settled_balance_cents` INTEGER NOT NULL,
    `local_amount` DOUBLE NOT NULL,
    `local_amount_cents` INTEGER NOT NULL,
    `side` VARCHAR(191) NOT NULL,
    `operation_type` VARCHAR(191) NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `local_currency` VARCHAR(191) NOT NULL,
    `label` VARCHAR(191) NOT NULL,
    `settled_at` DATETIME(3) NOT NULL,
    `emitted_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `note` VARCHAR(191) NULL,
    `reference` VARCHAR(191) NULL,
    `vat_amount` DOUBLE NULL,
    `vat_amount_cents` INTEGER NULL,
    `vat_rate` DOUBLE NULL,
    `initiator_id` VARCHAR(191) NULL,
    `attachment_lost` BOOLEAN NOT NULL,
    `attachment_required` BOOLEAN NOT NULL,
    `card_last_digits` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `attached_file` VARCHAR(191) NULL,

    PRIMARY KEY (`transaction_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Attachment` (
    `id` VARCHAR(191) NOT NULL,
    `transactionId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Label` (
    `id` VARCHAR(191) NOT NULL,
    `transactionId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Tenant` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `parentId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `User` ADD CONSTRAINT `User_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `User` ADD CONSTRAINT `User_selectedOuId_fkey` FOREIGN KEY (`selectedOuId`) REFERENCES `Ou`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Attachment` ADD CONSTRAINT `Attachment_transactionId_fkey` FOREIGN KEY (`transactionId`) REFERENCES `Transaction`(`transaction_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Label` ADD CONSTRAINT `Label_transactionId_fkey` FOREIGN KEY (`transactionId`) REFERENCES `Transaction`(`transaction_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Tenant` ADD CONSTRAINT `Tenant_parentId_fkey` FOREIGN KEY (`parentId`) REFERENCES `Tenant`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
