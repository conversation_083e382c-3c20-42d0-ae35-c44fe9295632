-- CreateTable
CREATE TABLE `Account` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `provider` VARCHAR(191) NOT NULL,
    `providerAccountId` VARCHAR(191) NOT NULL,
    `refresh_token` VARCHAR(191) NULL,
    `access_token` VARCHAR(191) NULL,
    `expires_at` INTEGER NULL,
    `token_type` VARCHAR(191) NULL,
    `scope` VARCHAR(191) NULL,
    `id_token` VARCHAR(191) NULL,
    `session_state` VARCHAR(191) NULL,

    INDEX `Account_userId_fkey`(`userId`),
    UNIQUE INDEX `Account_provider_providerAccountId_key`(`provider`, `providerAccountId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Session` (
    `id` VARCHAR(191) NOT NULL,
    `sessionToken` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `expires` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Session_sessionToken_key`(`sessionToken`),
    INDEX `Session_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `User` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `password` VARCHAR(191) NULL,
    `emailVerified` DATETIME(3) NULL,
    `image` VARCHAR(191) NULL,
    `role` VARCHAR(191) NULL,
    `ouId` VARCHAR(191) NULL,

    UNIQUE INDEX `User_email_key`(`email`),
    INDEX `User_ouId_fkey`(`ouId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Ou` (
    `id` VARCHAR(191) NOT NULL,
    `parentId` VARCHAR(191) NULL,
    `name` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `externalReference` VARCHAR(191) NULL,
    `address` VARCHAR(191) NULL,
    `state` VARCHAR(191) NULL,
    `country` VARCHAR(191) NULL,
    `city` VARCHAR(191) NULL,
    `houseNumber` VARCHAR(191) NULL,
    `postalCode` VARCHAR(191) NULL,
    `hotlinePhoneNumber` VARCHAR(191) NULL,
    `companyEmail` VARCHAR(191) NULL,
    `primaryContactperson` VARCHAR(191) NULL,
    `primaryContactpersonEmail` VARCHAR(191) NULL,
    `directPaymentProfileId` VARCHAR(191) NULL,
    `mspOuId` VARCHAR(191) NULL,
    `mspOuName` VARCHAR(191) NULL,
    `mspOuCode` VARCHAR(191) NULL,
    `mspExternalId` VARCHAR(191) NULL,
    `financialDetails` VARCHAR(191) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `VerificationToken` (
    `identifier` VARCHAR(191) NOT NULL,
    `token` VARCHAR(191) NOT NULL,
    `expires` DATETIME(3) NOT NULL,

    UNIQUE INDEX `VerificationToken_token_key`(`token`),
    UNIQUE INDEX `VerificationToken_identifier_token_key`(`identifier`, `token`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Emp` (
    `id` VARCHAR(191) NOT NULL,
    `customerNumber` VARCHAR(191) NULL,
    `name` VARCHAR(255) NULL,
    `providerCountryId` VARCHAR(3) NULL,
    `providerId` VARCHAR(3) NULL,
    `companyName` VARCHAR(200) NULL,
    `invoiceMail` VARCHAR(200) NOT NULL DEFAULT '<EMAIL>',
    `cdrMail` VARCHAR(200) NULL,
    `note` LONGTEXT NULL,
    `invoiceLanguageCode` ENUM('DE', 'EN') NOT NULL DEFAULT 'DE',

    UNIQUE INDEX `Emp_id_key`(`id`),
    UNIQUE INDEX `Emp_customerNumber_key`(`customerNumber`),
    UNIQUE INDEX `Emp_providerId_key`(`providerId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EmpEnergyReseller` (
    `id` VARCHAR(191) NOT NULL,
    `start` DATETIME(0) NULL,
    `end` DATETIME(0) NULL,
    `file` INTEGER NULL,
    `valid` BOOLEAN NULL,
    `empId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `EmpEnergyReseller_id_key`(`id`),
    INDEX `EmpEnergyReseller_empId_fkey`(`empId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Invoice` (
    `id` VARCHAR(191) NOT NULL,
    `invoiceId` INTEGER NULL,
    `invoiceNumber` VARCHAR(191) NULL,
    `invoiceIdSufix` VARCHAR(191) NULL,
    `invoiceRelationId` VARCHAR(191) NULL,
    `kindOfInvoice` ENUM('INVOICE', 'STORNO', 'CREDIT') NOT NULL,
    `stateOfInvoice` ENUM('PREVIEW', 'CREATED', 'PAID', 'CANCEL') NOT NULL,
    `createDate` DATE NULL,
    `invoiceDate` DATE NULL,
    `ServicePeriodFrom` DATE NULL,
    `ServicePeriodTo` DATE NULL,
    `PaidOnDate` DATE NULL,
    `sendAsMail` BOOLEAN NOT NULL DEFAULT false,
    `sumKwh` DOUBLE NOT NULL,
    `sumSession` INTEGER NOT NULL,
    `sumNet` DOUBLE NOT NULL,
    `sumGross` DOUBLE NOT NULL,
    `sumTax` DOUBLE NOT NULL,
    `subject` VARCHAR(191) NULL,
    `empId` VARCHAR(191) NOT NULL,
    `history` LONGTEXT NULL,

    UNIQUE INDEX `Invoice_id_key`(`id`),
    UNIQUE INDEX `Invoice_invoiceRelationId_key`(`invoiceRelationId`),
    UNIQUE INDEX `Invoice_invoiceId_invoiceIdSufix_key`(`invoiceId`, `invoiceIdSufix`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `InvoicePosition` (
    `id` VARCHAR(191) NOT NULL,
    `pos` INTEGER NULL,
    `title` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `unit` VARCHAR(191) NULL,
    `unitPrice` DOUBLE NOT NULL,
    `description` LONGTEXT NULL,
    `sumNet` DOUBLE NOT NULL,
    `sumGross` DOUBLE NOT NULL,
    `sumTax` DOUBLE NOT NULL,
    `invoiceId` VARCHAR(191) NULL,
    `roamingOfferId` VARCHAR(191) NULL,
    `taxRate` DOUBLE NOT NULL,

    UNIQUE INDEX `InvoicePosition_id_key`(`id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RoamingOfferOnEmps` (
    `roamingOfferId` VARCHAR(191) NOT NULL,
    `empId` VARCHAR(191) NOT NULL,

    INDEX `RoamingOfferOnEmps_empId_fkey`(`empId`),
    PRIMARY KEY (`roamingOfferId`, `empId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RoamingOffer` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `sessionFee` FLOAT NULL,
    `kwh` FLOAT NOT NULL,
    `minChargingTime` FLOAT NOT NULL,
    `minChargingEnergy` FLOAT NOT NULL,
    `currentType` VARCHAR(11) NULL,
    `validFrom` DATE NOT NULL,
    `validTo` DATE NOT NULL,

    UNIQUE INDEX `RoamingOffer_id_key`(`id`),
    UNIQUE INDEX `RoamingOffer_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `FileRef` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `path` VARCHAR(191) NOT NULL,
    `sha1` VARCHAR(191) NULL,
    `contentType` VARCHAR(191) NULL,
    `invoiceId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `FileRef_id_key`(`id`),
    UNIQUE INDEX `sha1`(`sha1`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `FileStorage` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `path` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Cdr` (
    `CDR_ID` VARCHAR(191) NOT NULL DEFAULT '',
    `Start_datetime` DATETIME(0) NOT NULL,
    `End_datetime` DATETIME(0) NOT NULL,
    `Duration` VARCHAR(200) NULL,
    `DurationInSec` INTEGER NULL,
    `Volume` FLOAT NULL,
    `Charge_Point_Address` VARCHAR(200) NULL,
    `Charge_Point_ZIP` VARCHAR(11) NULL,
    `Charge_Point_City` VARCHAR(60) NULL,
    `Charge_Point_Country` VARCHAR(6) NULL,
    `Charge_Point_Type` TINYINT NULL,
    `Product_Type` TINYINT NULL,
    `Tariff_Type` CHAR(11) NULL,
    `Authentication_ID` VARCHAR(60) NULL,
    `Contract_ID` VARCHAR(60) NULL,
    `Meter_ID` VARCHAR(60) NULL,
    `OBIS_Code` VARCHAR(60) NULL,
    `Charge_Point_ID` CHAR(36) NULL,
    `Service_Provider_ID` CHAR(60) NULL,
    `Infra_Provider_ID` CHAR(10) NULL,
    `Calculated_Cost` FLOAT NULL,
    `Timezone` CHAR(60) NULL,
    `LocalStart_datetime` DATETIME(0) NULL,
    `LocalEnd_datetime` DATETIME(0) NULL,
    `Location_ID` CHAR(60) NULL,
    `OU_Code` CHAR(60) NULL,
    `Tariff_Name` CHAR(60) NULL,
    `Start_Tariff` FLOAT NULL,
    `Tariff_kWh` FLOAT NULL,
    `Token_OU_Code` VARCHAR(60) NULL,
    `Token_OU_Name` VARCHAR(100) NULL,
    `OU_Name` CHAR(100) NULL,
    `Owner` CHAR(100) NULL,
    `Operator` CHAR(100) NULL,
    `Sub_Operator` CHAR(100) NULL,
    `MeterStart` INTEGER NULL,
    `MeterStop` INTEGER NULL,
    `ExternalReference` VARCHAR(100) NULL,
    `Charging_Time_Cost` FLOAT NULL,
    `Parking_Time_Cost` FLOAT NULL,
    `ConnectorId` INTEGER NULL,
    `invoiceId` VARCHAR(191) NULL,
    `roamingOfferId` VARCHAR(191) NULL,
    `billable` BOOLEAN NOT NULL DEFAULT true,

    PRIMARY KEY (`CDR_ID`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EmpAddress` (
    `id` VARCHAR(191) NOT NULL,
    `validFrom` DATE NOT NULL,
    `validTo` DATE NOT NULL,
    `street` VARCHAR(200) NULL,
    `streetNr` VARCHAR(20) NULL,
    `city` VARCHAR(200) NULL,
    `zip` VARCHAR(200) NULL,
    `country` VARCHAR(200) NULL,
    `ustId` VARCHAR(200) NULL,
    `isNetInvoice` BOOLEAN NULL,
    `invoiceTaxRate` DOUBLE NULL DEFAULT 19,
    `ustIdValid` BOOLEAN NULL,
    `empId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `EmpAddress_id_key`(`id`),
    INDEX `EmpAddress_empId_fkey`(`empId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Parking` (
    `device_id` BIGINT NOT NULL,
    `timestamp` DATETIME(0) NOT NULL,
    `state` BOOLEAN NULL,
    `battery` TINYINT UNSIGNED NULL,
    `sensorTime` DATETIME(0) NULL,

    PRIMARY KEY (`device_id`, `timestamp`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ParkingSensor` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(11) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `StopLog` (
    `id` VARCHAR(36) NOT NULL DEFAULT '',
    `createAt` DATETIME(0) NULL,
    `transactionId` INTEGER NULL,
    `idTag` VARCHAR(255) NULL,
    `chargePointId` VARCHAR(255) NULL,
    `emp` VARCHAR(6) NULL,
    `kwh` FLOAT NULL,
    `duration` INTEGER NULL,
    `durationHr` VARCHAR(30) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ChargePoint` (
    `id` VARCHAR(191) NOT NULL,
    `chargePointId` VARCHAR(191) NOT NULL,
    `dateDeleted` DATETIME(3) NULL,
    `displayName` VARCHAR(191) NOT NULL,
    `roamingName` VARCHAR(191) NOT NULL,
    `chargeBoxSerialNumber` VARCHAR(191) NULL,
    `chargePointModel` VARCHAR(191) NOT NULL,
    `chargePointSerialNumber` VARCHAR(191) NULL,
    `chargePointVendor` VARCHAR(191) NOT NULL,
    `firmwareVersion` VARCHAR(191) NOT NULL,
    `connectivityStatus` VARCHAR(191) NOT NULL,
    `iccid` VARCHAR(191) NULL,
    `imsi` VARCHAR(191) NULL,
    `meterSerialNumber` VARCHAR(191) NULL,
    `meterType` VARCHAR(191) NULL,
    `tenantId` VARCHAR(191) NOT NULL,
    `isRoaming` BOOLEAN NOT NULL,
    `hasGuestUsage` BOOLEAN NULL,
    `locationId` VARCHAR(191) NULL,
    `allowAnyToken` BOOLEAN NOT NULL,
    `dateCreated` DATETIME(3) NOT NULL,
    `updated` DATETIME(3) NULL,
    `ou` VARCHAR(191) NOT NULL,
    `ouId` VARCHAR(191) NOT NULL,
    `ouName` VARCHAR(191) NOT NULL,
    `tariffId` VARCHAR(191) NULL,
    `tariffName` VARCHAR(191) NULL,
    `startTariff` INTEGER NULL,
    `tariffPrice` DOUBLE NULL,
    `simCardNumber` VARCHAR(191) NULL,
    `isNew` BOOLEAN NOT NULL,
    `hasReimbursement` BOOLEAN NULL,
    `reimburseTariffId` VARCHAR(191) NULL,
    `reimburseTariffName` VARCHAR(191) NULL,
    `reimburseTariffPrice` DOUBLE NULL,
    `reimburseUID` VARCHAR(191) NULL,
    `reimburseTokenId` VARCHAR(191) NULL,
    `reimburseOU` VARCHAR(191) NULL,
    `useTenantFee` BOOLEAN NOT NULL,
    `maxCapacityInKw` INTEGER NOT NULL,

    UNIQUE INDEX `ChargePoint_chargePointId_key`(`chargePointId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RealtimeData` (
    `id` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `kw` DOUBLE NOT NULL,
    `kwh` DOUBLE NOT NULL,
    `sessionStart` DATETIME(3) NOT NULL,
    `lastUpdate` DATETIME(3) NOT NULL,
    `emp` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Location` (
    `id` VARCHAR(191) NOT NULL,
    `country_code` VARCHAR(191) NOT NULL,
    `party_id` VARCHAR(191) NOT NULL,
    `publish` BOOLEAN NOT NULL,
    `publish_allowed_to` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `houseNumber` VARCHAR(191) NOT NULL,
    `street` VARCHAR(191) NOT NULL,
    `city` VARCHAR(191) NOT NULL,
    `postal_code` VARCHAR(191) NOT NULL,
    `country` VARCHAR(191) NOT NULL,
    `hotline_phonenumber` VARCHAR(191) NOT NULL,
    `parking_type` VARCHAR(191) NOT NULL,
    `coordinatesId` VARCHAR(191) NULL,

    UNIQUE INDEX `Location_id_key`(`id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PowerContract` (
    `id` VARCHAR(191) NOT NULL,
    `typeOfContract` VARCHAR(191) NOT NULL,
    `start` DATETIME(3) NOT NULL,
    `end` DATETIME(3) NOT NULL,
    `basePrice` DOUBLE NULL,
    `kwhPrice` DOUBLE NULL,
    `contractWith` VARCHAR(191) NULL,
    `contractNumber` VARCHAR(191) NULL,
    `locationId` VARCHAR(191) NOT NULL,
    `customerNumber` VARCHAR(191) NOT NULL,
    `supplyNetworkOperator` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `PowerContract_id_key`(`id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ThgPrice` (
    `start` DATETIME(3) NOT NULL,
    `end` DATETIME(3) NOT NULL,
    `price` DOUBLE NOT NULL,

    UNIQUE INDEX `ThgPrice_start_key`(`start`),
    UNIQUE INDEX `ThgPrice_end_key`(`end`),
    PRIMARY KEY (`start`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CdrCost` (
    `cost` DOUBLE NOT NULL,
    `cdrId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `CdrCost_cdrId_key`(`cdrId`),
    PRIMARY KEY (`cdrId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `HistoryPowerByCharger` (
    `power` INTEGER NOT NULL,
    `timestamp` DATETIME(3) NOT NULL,
    `chargePointId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `HistoryPowerByCharger_timestamp_chargePointId_key`(`timestamp`, `chargePointId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `HistoryPowerByOu` (
    `power` INTEGER NOT NULL,
    `timestamp` DATETIME(3) NOT NULL,
    `ouId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `HistoryPowerByOu_timestamp_ouId_key`(`timestamp`, `ouId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Coordinates` (
    `id` VARCHAR(191) NOT NULL,
    `latitude` DOUBLE NOT NULL,
    `longitude` DOUBLE NOT NULL,

    UNIQUE INDEX `Coordinates_id_key`(`id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Evse` (
    `uid` VARCHAR(191) NOT NULL,
    `evse_id` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `status_schedule` VARCHAR(191) NULL,
    `capabilities` VARCHAR(191) NOT NULL,
    `floor_level` VARCHAR(191) NULL,
    `physical_reference` VARCHAR(191) NOT NULL,
    `directions` VARCHAR(191) NULL,
    `images` VARCHAR(191) NULL,
    `last_updated` DATETIME(3) NOT NULL,
    `locationId` VARCHAR(191) NOT NULL,
    `coordinatesId` VARCHAR(191) NOT NULL,
    `chargePointId` VARCHAR(191) NULL,

    PRIMARY KEY (`uid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Connector` (
    `uid` VARCHAR(191) NOT NULL,
    `standard` VARCHAR(191) NOT NULL,
    `format` VARCHAR(191) NOT NULL,
    `power_type` VARCHAR(191) NOT NULL,
    `max_voltage` INTEGER NOT NULL,
    `max_amperage` INTEGER NOT NULL,
    `max_electric_power` INTEGER NOT NULL,
    `calc_max_electric_power` BOOLEAN NOT NULL,
    `last_updated` DATETIME(3) NOT NULL,
    `evseId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`uid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EnergyStockPrice` (
    `timestamp` DATETIME(3) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `hour` INTEGER NOT NULL,
    `month` INTEGER NOT NULL,
    `year` INTEGER NOT NULL,

    UNIQUE INDEX `EnergyStockPrice_timestamp_key`(`timestamp`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Log` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `timestamp` DATETIME(3) NOT NULL,
    `type` ENUM('WARN', 'ERROR', 'INFO', 'DEBUG') NOT NULL,
    `topic` VARCHAR(191) NOT NULL,
    `headline` VARCHAR(191) NOT NULL,
    `message` LONGTEXT NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Token` (
    `id` VARCHAR(191) NOT NULL,
    `createDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `authenticationId` VARCHAR(191) NULL,
    `plateNumber` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `description` TEXT NULL,
    `tokenGroupId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `TokenGroup` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `empId` VARCHAR(191) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Account` ADD CONSTRAINT `Account_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Session` ADD CONSTRAINT `Session_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `User` ADD CONSTRAINT `User_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EmpEnergyReseller` ADD CONSTRAINT `EmpEnergyReseller_empId_fkey` FOREIGN KEY (`empId`) REFERENCES `Emp`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Invoice` ADD CONSTRAINT `Invoice_invoiceRelationId_fkey` FOREIGN KEY (`invoiceRelationId`) REFERENCES `Invoice`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Invoice` ADD CONSTRAINT `Invoice_empId_fkey` FOREIGN KEY (`empId`) REFERENCES `Emp`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `InvoicePosition` ADD CONSTRAINT `InvoicePosition_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `InvoicePosition` ADD CONSTRAINT `InvoicePosition_roamingOfferId_fkey` FOREIGN KEY (`roamingOfferId`) REFERENCES `RoamingOffer`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RoamingOfferOnEmps` ADD CONSTRAINT `RoamingOfferOnEmps_empId_fkey` FOREIGN KEY (`empId`) REFERENCES `Emp`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RoamingOfferOnEmps` ADD CONSTRAINT `RoamingOfferOnEmps_roamingOfferId_fkey` FOREIGN KEY (`roamingOfferId`) REFERENCES `RoamingOffer`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `FileRef` ADD CONSTRAINT `FileRef_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Cdr` ADD CONSTRAINT `Cdr_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Cdr` ADD CONSTRAINT `Cdr_roamingOfferId_fkey` FOREIGN KEY (`roamingOfferId`) REFERENCES `RoamingOffer`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EmpAddress` ADD CONSTRAINT `EmpAddress_empId_fkey` FOREIGN KEY (`empId`) REFERENCES `Emp`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Location` ADD CONSTRAINT `Location_coordinatesId_fkey` FOREIGN KEY (`coordinatesId`) REFERENCES `Coordinates`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PowerContract` ADD CONSTRAINT `PowerContract_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CdrCost` ADD CONSTRAINT `CdrCost_cdrId_fkey` FOREIGN KEY (`cdrId`) REFERENCES `Cdr`(`CDR_ID`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `HistoryPowerByCharger` ADD CONSTRAINT `HistoryPowerByCharger_chargePointId_fkey` FOREIGN KEY (`chargePointId`) REFERENCES `ChargePoint`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `HistoryPowerByOu` ADD CONSTRAINT `HistoryPowerByOu_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Evse` ADD CONSTRAINT `Evse_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Evse` ADD CONSTRAINT `Evse_coordinatesId_fkey` FOREIGN KEY (`coordinatesId`) REFERENCES `Coordinates`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Evse` ADD CONSTRAINT `Evse_chargePointId_fkey` FOREIGN KEY (`chargePointId`) REFERENCES `ChargePoint`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Connector` ADD CONSTRAINT `Connector_evseId_fkey` FOREIGN KEY (`evseId`) REFERENCES `Evse`(`uid`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Token` ADD CONSTRAINT `Token_tokenGroupId_fkey` FOREIGN KEY (`tokenGroupId`) REFERENCES `TokenGroup`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `TokenGroup` ADD CONSTRAINT `TokenGroup_empId_fkey` FOREIGN KEY (`empId`) REFERENCES `Emp`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
