-- AlterTable
ALTER TABLE `Contact` ADD COLUMN `ouId` VARCHAR(191) NULL,
    ADD COLUMN `supplierNumber` VARCHAR(191) NULL;

-- AddForeignKey
ALTER TABLE `Contact` ADD CONSTRAINT `Contact_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- RedefineIndex
CREATE UNIQUE INDEX `Contact_id_key` ON `Contact`(`id`);
DROP INDEX `Emp_id_key` ON `Contact`;

-- RedefineIndex
CREATE UNIQUE INDEX `Contact_providerId_key` ON `Contact`(`providerId`);
DROP INDEX `Emp_providerId_key` ON `Contact`;

-- RedefineIndex
CREATE INDEX `ContactAddress_contactId_fkey` ON `ContactAddress`(`contactId`);
DROP INDEX `EmpAddress_empId_fkey` ON `ContactAddress`;

-- RedefineIndex
CREATE UNIQUE INDEX `ContactAddress_id_key` ON `ContactAddress`(`id`);
DROP INDEX `EmpAddress_id_key` ON `ContactAddress`;

-- RedefineIndex
CREATE INDEX `ContactEnergyReseller_contactId_fkey` ON `ContactEnergyReseller`(`contactId`);
DROP INDEX `EmpEnergyReseller_empId_fkey` ON `ContactEnergyReseller`;

-- RedefineIndex
CREATE UNIQUE INDEX `ContactEnergyReseller_id_key` ON `ContactEnergyReseller`(`id`);
DROP INDEX `EmpEnergyReseller_id_key` ON `ContactEnergyReseller`;

-- RedefineIndex
CREATE INDEX `RoamingOfferOnContacts_contactId_fkey` ON `RoamingOfferOnContacts`(`contactId`);
DROP INDEX `RoamingOfferOnEmps_empId_fkey` ON `RoamingOfferOnContacts`;
