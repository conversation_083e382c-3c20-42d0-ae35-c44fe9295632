/*
  Warnings:

  - You are about to alter the column `state` on the `ParkingSensor` table. The data in that column could be lost. The data in that column will be cast from `TinyInt` to `UnsignedTinyInt`.
  - Made the column `state` on table `ParkingEvent` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `ParkingEvent` ADD COLUMN `heartbeat` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `init` BOOLEAN NOT NULL DEFAULT false,
    MODIFY `state` TINYINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `ParkingSensor` MODIFY `state` TINYINT UNSIGNED NOT NULL;
