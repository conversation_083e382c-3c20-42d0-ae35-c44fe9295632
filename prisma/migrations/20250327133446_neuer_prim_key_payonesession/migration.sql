/*
  Warnings:

  - The primary key for the `PayoneSession` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `PayoneSession` table. All the data in the column will be lost.
  - Made the column `<PERSON><PERSON>enart` on table `PayoneSession` required. This step will fail if there are existing NULL values in that column.
  - Made the column `Kartennr.` on table `PayoneSession` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `PayoneSession` DROP PRIMARY KEY,
    DROP COLUMN `id`,
    MODIFY `Zeilenart` VARCHAR(191) NOT NULL,
    MODIFY `Kartennr.` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`Kartennr.`, `Zeilenart`, `Zahlref.-Nr.`);
