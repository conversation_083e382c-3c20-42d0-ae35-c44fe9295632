/*
  Warnings:

  - Made the column `name` on table `User` required. This step will fail if there are existing NULL values in that column.
  - Made the column `lastName` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `ContactAddress` ADD COLUMN `userId` VARCHAR(191) NULL;

UPDATE User SET lastName = '' WHERE lastName IS NULL;

-- AlterTable
ALTER TABLE `User` MODIFY `name` VARCHAR(191) NOT NULL,
    MODIFY `lastName` VARCHAR(191) NOT NULL DEFAULT '';

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE `ContactAddress` ADD CONSTRAINT `ContactAddress_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
