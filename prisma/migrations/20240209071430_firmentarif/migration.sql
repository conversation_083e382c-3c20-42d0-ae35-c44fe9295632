-- CreateTable
CREATE TABLE `TarifOnEMPCard` (
    `tarifId` VARCHAR(191) NOT NULL,
    `empCardId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`tarifId`, `empCardId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON>oreignKey
ALTER TABLE `TarifOnEMPCard` ADD CONSTRAINT `TarifOnEMPCard_tarifId_fkey` FOREIGN KEY (`tarifId`) REFERENCES `Tarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE `TarifOnEMPCard` ADD CONSTRAINT `TarifOnEMPCard_empCardId_fkey` FOREIGN KEY (`empCardId`) REFERENCES `EMPCard`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
