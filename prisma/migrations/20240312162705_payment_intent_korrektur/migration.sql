/*
  Warnings:

  - A unique constraint covering the columns `[invoiceId]` on the table `PaymentIntent` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE `PaymentIntent` DROP FOREIGN KEY `PaymentIntent_id_fkey`;

-- CreateIndex
CREATE UNIQUE INDEX `PaymentIntent_invoiceId_key` ON `PaymentIntent`(`invoiceId`);

-- AddForeignKey
ALTER TABLE `PaymentIntent` ADD CONSTRAINT `PaymentIntent_invoiceId_fkey` FOREIGN KEY (`invoiceId`) REFERENCES `Invoice`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
