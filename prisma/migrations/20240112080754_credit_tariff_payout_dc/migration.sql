-- AlterTable
ALTER TABLE `Contact` MODIFY `cdrMapping` ENUM('Standard', 'EnBW', 'Standard_Credit') NOT NULL DEFAULT 'Standard';

-- CreateTable
CREATE TABLE `CdrPayout` (
    `energyPayout` DOUBLE NOT NULL,
    `sessionPayout` DOUBLE NOT NULL,
    `blockingPayout` DOUBLE NOT NULL,
    `cdrId` VARCHAR(191) NOT NULL,
    `payoutSum` DOUBLE NOT NULL,

    UNIQUE INDEX `CdrPayout_cdrId_key`(`cdrId`),
    PRIMARY KEY (`cdrId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CreditTarif` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `tarifType` ENUM('FIX', 'FLEX') NOT NULL,
    `powerType` ENUM('AC', 'DC') NOT NULL,
    `validFrom` DATETIME(3) NOT NULL,
    `validTo` DATETIME(3) NULL,
    `sessionCredit` DOUBLE NOT NULL DEFAULT 0,
    `energyCredit` DOUBLE NOT NULL DEFAULT 0,
    `blockingCredit` DOUBLE NOT NULL DEFAULT 0,
    `maxBlockingCredit` DOUBLE NOT NULL DEFAULT 0,
    `blockingFeeMinStart` INTEGER NOT NULL DEFAULT 0,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ContactCreditTarif` (
    `contactId` VARCHAR(191) NOT NULL,
    `creditTarifId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`contactId`, `creditTarifId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `CdrPayout` ADD CONSTRAINT `CdrPayout_cdrId_fkey` FOREIGN KEY (`cdrId`) REFERENCES `Cdr`(`CDR_ID`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ContactCreditTarif` ADD CONSTRAINT `ContactCreditTarif_contactId_fkey` FOREIGN KEY (`contactId`) REFERENCES `Contact`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ContactCreditTarif` ADD CONSTRAINT `ContactCreditTarif_creditTarifId_fkey` FOREIGN KEY (`creditTarifId`) REFERENCES `CreditTarif`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
