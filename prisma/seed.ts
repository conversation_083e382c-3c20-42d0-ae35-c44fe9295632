import type { Tarif } from "@prisma/client";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
import cuid from "cuid";

const prices: Tarif[] = [
  {
    id: cuid(),
    name: "AC Q2",
    validFrom: new Date(),
    validTo: new Date(),
    sessionFee: 1,
    currentType: "AC",
    kwh: 0.41,
    minChargingEnergy: 0.2,
    minChargingTime: 2,
  },
];

async function main() {
  const user_jan = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      lastName: "runkel",
      id: "clbdl2nvr00006ds3hmda2tzf",
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "USER",
      ouId: "clbdl2nvr00006ds3hmda2tzf",
      selectedOuId: "clbdl2nvr00006ds3hmda2tzf",
      password:
        "dd1d3e4bd29e037a4ad5b2f2189649c5475649225b86f6f3a2f96acb6c517002596cc11850f38741c436e78c84418791dfe4054ab06a5893084ec09d8a5ab9f8",
    },
  });

  const contactTarifList = [
    {
      name: "Eulektro / Hubject offerToAll 2022",
      kwh: 0.41,
      minChargingEnergy: 0.2,
      minChargingTime: 2,
      sessionFee: 1,
      validFrom: new Date("08.31.2022"),
      validTo: new Date("01.31.2023"),
      currentType: "AC",
    },
    {
      name: "Eulektro / Hubject offerToAll 2023",
      kwh: 0.5,
      minChargingEnergy: 0.2,
      minChargingTime: 2,
      sessionFee: 1,
      validFrom: new Date("02.01.2023"),
      validTo: new Date("11.01.2024"),
      currentType: "AC",
    },
    {
      name: "Bonnet 2022 / 1",
      kwh: 0.47,
      minChargingEnergy: 0.2,
      minChargingTime: 2,
      sessionFee: 1,
      validFrom: new Date("02.01.2023"),
      validTo: new Date("11.01.2024"),
      currentType: "AC",
    },
    {
      name: "Elvah 2022 / 1",
      kwh: 0.3277,
      minChargingEnergy: 0.2,
      minChargingTime: 2,
      sessionFee: 0.84,
      validFrom: new Date("01.01.2022"),
      validTo: new Date("10.31.2024"),
      currentType: "AC",
    },
    {
      name: "Elvah 2022 / 2",
      kwh: 0.4118,
      minChargingEnergy: 0.2,
      minChargingTime: 2,
      sessionFee: 0.84,
      validFrom: new Date("11.01.2022"),
      validTo: new Date("10.31.2024"),
      currentType: "AC",
    },
  ];

  const contactTarifs = await prisma.tarif.createMany({
    data: contactTarifList,
    skipDuplicates: true,
  });

  const tarifs = await prisma.tarif.findMany();

  const contact_nbw = await prisma.contact.upsert({
    where: { providerId: "NBW" },
    update: {},
    create: {
      name: "EnBW",
      companyName: "EnBW mobility+ Ag & Co.KG",
      providerCountryId: "DE",
      providerId: "NBW",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Karlsruhe",
            street: "Durlacher Allee",
            streetNr: "93",
            country: "Deutschland",
            zip: "76133",
            ustId: "DE334538933",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2100-10-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [
          {
            start: new Date("2022-01-01T00:00:00+0200"),
            end: new Date(),
            valid: true,
          },
        ],
      },
    },
  });

  const contact_ewe = await prisma.contact.upsert({
    where: { providerId: "EWE" },
    update: {},
    create: {
      name: "Ewe Go",
      companyName: "EWE Go GmbH ",
      providerCountryId: "DE",
      providerId: "EWE",
      invoiceMail: "<EMAIL>",
      cdrMail: "<EMAIL>",
      note:
        "Für die Rechnungszustellung bitten wir Sie:\n" +
        "\n" +
        "Die Rechnungen als pdf im Anhang via <NAME_EMAIL> zu schicken und in der Benennung der pdf die Wörter “Rechnung” oder “invoice” anzuführen.\n" +
        "Die CDR separat als CSV-File oder Excel-<NAME_EMAIL> zu schicken.\n" +
        " \n" +
        "Bitte senden Sie stets Rechnungen, bei denen die Worte „Rechnung“ oder „invoice“ in der Beschriftung der pdf enthalten ist, an <EMAIL>. Da <NAME_EMAIL> automatisiert die Rechnungseingänge weiterverarbeitet, kommt es ansonsten zu Störmeldungen und die Rechnungen können nicht verarbeitet werden.",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Oldenburg",
            street: "Alter Stadthafen ",
            streetNr: "3b",
            country: "Deutschland",
            zip: "26122",
            ustId: "DE315640644",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-11-30T00:00:00+0200"),
            validTo: new Date("2022-01-01T00:00:00+0200"),
          },
          {
            city: "Oldenburg",
            street: "Donnerschweer Straße",
            streetNr: "22-26",
            country: "Deutschland",
            zip: "26123",
            ustId: "DE28155713",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-12-01T00:00:00+0200"),
            validTo: new Date("2100-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [
          {
            start: new Date("2022-11-09T00:00:00+0200"),
            end: new Date("2024-12-31T00:00:00+0200"),
            valid: true,
          },
        ],
      },
    },
  });

  const contact_eweGo = await prisma.contact.upsert({
    where: { providerId: "EWE" },
    update: {},
    create: {
      name: "Ewe Go",
      companyName: "EWE Go GmbH ",
      providerCountryId: "DE",
      providerId: "2GO",
      invoiceMail: "<EMAIL>",
      cdrMail: "<EMAIL>",
      note:
        "Für die Rechnungszustellung bitten wir Sie:\n" +
        "\n" +
        "Die Rechnungen als pdf im Anhang via <NAME_EMAIL> zu schicken und in der Benennung der pdf die Wörter “Rechnung” oder “invoice” anzuführen.\n" +
        "Die CDR separat als CSV-File oder Excel-<NAME_EMAIL> zu schicken.\n" +
        " \n" +
        "Bitte senden Sie stets Rechnungen, bei denen die Worte „Rechnung“ oder „invoice“ in der Beschriftung der pdf enthalten ist, an <EMAIL>. Da <NAME_EMAIL> automatisiert die Rechnungseingänge weiterverarbeitet, kommt es ansonsten zu Störmeldungen und die Rechnungen können nicht verarbeitet werden.",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Oldenburg",
            street: "Alter Stadthafen ",
            streetNr: "3b",
            country: "Deutschland",
            zip: "26122",
            ustId: "DE315640644",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-11-30T00:00:00+0200"),
            validTo: new Date("2022-01-01T00:00:00+0200"),
          },
          {
            city: "Oldenburg",
            street: "Donnerschweer Straße",
            streetNr: "22-26",
            country: "Deutschland",
            zip: "26123",
            ustId: "DE28155713",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-12-01T00:00:00+0200"),
            validTo: new Date("2100-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [
          {
            start: new Date("2022-11-09T00:00:00+0200"),
            end: new Date("2024-12-31T00:00:00+0200"),
            valid: true,
          },
        ],
      },
    },
  });

  const contact_shell = await prisma.contact.upsert({
    where: { providerId: "TNM" },
    update: {},
    create: {
      name: "Shell Recharge",
      companyName: "Shell EV Charging Solutions Germany GmbH",
      providerCountryId: "NL",
      providerId: "TNM",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note:
        "Für die Rechnungszustellung bitten wir Sie:\n" +
        "\n" +
        "Die Rechnungen als pdf im Anhang via <NAME_EMAIL> zu schicken und in der Benennung der pdf die Wörter “Rechnung” oder “invoice” anzuführen.\n" +
        "Die CDR separat als CSV-File oder Excel-<NAME_EMAIL> zu schicken.\n" +
        " \n" +
        "Bitte senden Sie stets Rechnungen, bei denen die Worte „Rechnung“ oder „invoice“ in der Beschriftung der pdf enthalten ist, an <EMAIL>. Da <NAME_EMAIL> automatisiert die Rechnungseingänge weiterverarbeitet, kommt es ansonsten zu Störmeldungen und die Rechnungen können nicht verarbeitet werden.",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Berlin",
            street: "Kopernikusstraße",
            streetNr: "35",
            country: "Deutschland",
            zip: "10243",
            ustId: "DE815388933",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [
          {
            start: new Date("2022-11-09T00:00:00+0200"),
            end: new Date("2025-04-18T00:00:00+0200"),
            valid: true,
          },
        ],
      },
    },
  });

  const contact_dcs = await prisma.contact.upsert({
    where: { providerId: "DCS" },
    update: {},
    create: {
      name: "Digital Charging Solutions",
      companyName: "Digital Charging Solutions GmbH ",
      providerCountryId: "DE",
      providerId: "DCS",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "München",
            street: "Mies-van-der-Rohe-Straße",
            streetNr: "6",
            country: "Deutschland",
            zip: "80807",
            ustId: "DE312237805",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [
          {
            start: new Date("2022-01-01T00:00:00+0200"),
            end: new Date("2025-07-19T00:00:00+0200"),
            valid: true,
          },
        ],
      },
    },
  });

  const contact_elvah = await prisma.contact.upsert({
    where: { providerId: "KRT" },
    update: {},
    create: {
      name: "elvah GmbH",
      companyName: "elvah GmbH ",
      providerCountryId: "DE",
      providerId: "KRT",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Grafschaft",
            street: "Oberescher Weg",
            streetNr: "7",
            country: "Deutschland",
            zip: "80807",
            ustId: "DE281555713",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [
          {
            start: new Date("2022-01-01T00:00:00+0200"),
            end: new Date("2023-12-31T00:00:00+0200"),
            valid: true,
          },
        ],
      },
    },
  });

  const contact_chargepoint = await prisma.contact.upsert({
    where: { providerId: "HTB" },
    update: {},
    create: {
      name: "ChargePoint Austria GmbH",
      companyName: "ChargePoint Austria GmbH",
      providerCountryId: "AT",
      providerId: "HTB",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Radstadt",
            street: "Salzburger Straße",
            streetNr: "26",
            country: "Österreich",
            zip: "5550",
            ustId: "ATU68066335",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_greeflux = await prisma.contact.upsert({
    where: { providerId: "GFX" },
    update: {},
    create: {
      name: "GreenFlux Assets B.V. ",
      companyName: "GreenFlux Assets B.V. ",
      providerCountryId: "NL",
      providerId: "GFX",
      invoiceMail: "<EMAIL> ",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Amsterdam",
            street: "Mauritskade",
            streetNr: "63",
            country: "Netherlands",
            zip: "1092 AD",
            ustId: "NL851745052B01",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2022-10-31T00:00:00+0200"),
          },
          {
            city: "Amsterdam",
            street: "Joan Muyskenweg",
            streetNr: "22",
            country: "Netherlands",
            zip: "1096 CJ",
            ustId: "NL851745052B01",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-11-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_maingau = await prisma.contact.upsert({
    where: { providerId: "MEG" },
    update: {},
    create: {
      name: "MAINGAU Energie GmbH",
      companyName: "MAINGAU Energie GmbH",
      providerCountryId: "DE",
      providerId: "MEG",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Obertshausen",
            street: "Ringstraße",
            streetNr: "4-6",
            country: "Deutschland",
            zip: "63179",
            ustId: "DE113525007",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_trafineo = await prisma.contact.upsert({
    where: { providerId: "TRA" },
    update: {},
    create: {
      name: "TRAFINEO GmbH & Co. KG",
      companyName: "TRAFINEO GmbH & Co. KG",
      providerCountryId: "DE",
      providerId: "TRA",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Bochum",
            street: "Wittener Straße",
            streetNr: "56",
            country: "Deutschland",
            zip: "44789",
            ustId: "DE266451114",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_mer = await prisma.contact.upsert({
    where: { providerId: "EEM" },
    update: {},
    create: {
      name: "Mer Solutions GmbH",
      companyName: "Mer Solutions GmbH",
      providerCountryId: "DE",
      providerId: "EEM",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "München",
            street: "Taunusstraße",
            streetNr: "23",
            country: "Deutschland",
            zip: "80807",
            ustId: "DE298490066",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_chargemap = await prisma.contact.upsert({
    where: { providerId: "CMP" },
    update: {},
    create: {
      name: "CHARGEMAP SAS",
      companyName: "CHARGEMAP SAS",
      providerCountryId: "FR",
      providerId: "CMP",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "STRASBOURG",
            street: "allée Cérès",
            streetNr: "7",
            country: "Frankreich",
            zip: "F-67200",
            ustId: "FR37809844731",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_bonnet = await prisma.contact.upsert({
    where: { providerId: "BNT" },
    update: {},
    create: {
      name: "Bonnet Ltd",
      companyName: "Bonnet Ltd",
      providerCountryId: "GB",
      providerId: "BNT",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "London",
            street: "Tavistock St",
            streetNr: "34",
            country: "Großbritannien",
            zip: "WC2E 7PB",
            ustId: "GB361451710",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });

  const contact_chargePoint = await prisma.contact.upsert({
    where: { providerId: "CPI" },
    update: {},
    create: {
      name: "ChargePoint",
      companyName: "ChargePoint",
      providerCountryId: "DE",
      providerId: "CPI",
      invoiceMail: "<EMAIL>",
      cdrMail: "",
      note: "",
      tarifs: {
        create: tarifs.map((price) => ({ tarifId: price.id })),
      },
      contactAddress: {
        create: [
          {
            city: "Amsterdam",
            street: "Hoogoorddreef",
            streetNr: "56E",
            country: "Niederlande",
            zip: "1101 BE",
            ustId: "NL856714392B01",
            isNetInvoice: true,
            ustIdValid: true,
            validFrom: new Date("2022-01-01T00:00:00+0200"),
            validTo: new Date("2099-01-01T00:00:00+0200"),
          },
        ],
      },
      contactEnergyResellers: {
        create: [{}],
      },
    },
  });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
