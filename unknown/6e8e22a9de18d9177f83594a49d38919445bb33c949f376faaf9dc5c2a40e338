import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupInternalTarifsFromUserGroups() {
  console.log('🚀 Starting cleanup of internal tarifs from UserGroups...');

  try {
    // Find all UserGroup assignments with internal tarifs
    const internalTarifAssignments = await prisma.companyTarifOnUserGroup.findMany({
      include: {
        tarif: {
          select: {
            id: true,
            name: true,
            internal: true,
          },
        },
        userGroup: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      where: {
        tarif: {
          internal: true,
        },
      },
    });

    console.log(`📊 Found ${internalTarifAssignments.length} internal tarif assignments to remove`);

    if (internalTarifAssignments.length === 0) {
      console.log('🎯 No internal tarif assignments found. All UserGroups are already clean!');
      return;
    }

    // Log what will be removed
    console.log('\n📋 Internal tarif assignments to be removed:');
    internalTarifAssignments.forEach(assignment => {
      console.log(`  - UserGroup: "${assignment.userGroup.name}" -> Tarif: "${assignment.tarif.name}"`);
    });

    // Remove all internal tarif assignments from UserGroups
    const result = await prisma.companyTarifOnUserGroup.deleteMany({
      where: {
        tarif: {
          internal: true,
        },
      },
    });

    console.log(`\n✅ Successfully removed ${result.count} internal tarif assignments from UserGroups`);
    console.log('🎉 Cleanup completed! Internal tarifs can no longer be assigned to UserGroups.');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
cleanupInternalTarifsFromUserGroups()
  .then(() => {
    console.log('✅ Cleanup script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Cleanup script failed:', error);
    process.exit(1);
  });
