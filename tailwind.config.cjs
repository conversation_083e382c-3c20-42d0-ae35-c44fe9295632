const plugin = require("tailwindcss/plugin");
/** @type {import('tailwindcss').Config} */
module.exports = {
  mode:'jit',
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}"],
  presets: [],
  darkMode: "class",
    safelist: [
        {
            pattern: /^__variable_/,
        },
    ],
  theme: {
      extend: {
          backgroundColor: {
              'primary': 'var(--color-primary)',
              'secondary': 'var(--color-secondary)'
          },
          textColor: {
              'primary': 'var(--color-primary-text)',
              'secondary': 'var(--color-secondary-text)'
          },
          fontFamily: {
              sansnew: ['var(--font-sansnew)'],
          },
          // ... andere Utilities
      },
    screens: {
      sm: "576px",
      "sm-max": { max: "576px" },
      md: "768px",
      "md-max": { max: "768px" },
      lg: "992px",
      "lg-max": { max: "992px" },
      xl: "1200px",
      "xl-max": { max: "1200px" },
      "2xl": "1400px",
      "2xl-max": { max: "1320px" },
    },
    colors: ({ colors }) => ({
      inherit: colors.inherit,
      current: colors.current,
      transparent: colors.transparent,
      black: colors.black,
      white: colors.white,
        deactivated: {100: "#9D9A9B"},
        eul : {
            "gray": "#9D9A9B",
            "light-gray": "#bbbbbd",
            "main":"#FDFDFD"},
        elm: {
            "50": "#effbfc",
            "100": "#d6f4f7",
            "200": "#b2e9ef",
            "300": "#7dd8e3",
            "400": "#40bed0",
            "500": "#24a1b6",
            "600": "#218399",
            "700": "#21697c",
            "800": "#235767",
            "900": "#214958",
            "950": "#112f3b",
        },
      slate: {
        DEFAULT: colors.slate,
        50: "#f8fafc",
        100: "#dee2e6",
        150: "#cbd3da",
        200: "#e4e8ed",
        250: "#c7d3de",
        300: "#a8b8d8",
        400: "#8392ab",
        500: "#67748e",
        600: "#627594",
        650: "#5974a2",
        700: "#344767",
        750: "#1e2e4a",
        800: "#3a416f",
        850: "#323a54",
        900: "#0f172a",
        950: "#141728",
      },
      gray: {
        DEFAULT: colors.gray,
        50: "#f8f9fa",
        100: "#ebeff4",
        200: "#e9ecef",
        300: "#d2d6da",
        400: "#ced4da",
        500: "#adb5bd",
        600: "#6c757d",
        700: "#495057",
        800: "#252f40",
        850: "#1a2035",
        900: "#141727",
        950: "#111322",
      },

      zinc: {
        DEFAULT: colors.zinc,
        50: "#fafafa",
        100: "#f4f4f5",
        200: "#e4e4e7",
        300: "#d4d4d8",
        400: "#a1a1aa",
        500: "#71717a",
        600: "#52525b",
        700: "#3f3f46",
        800: "#27272a",
        900: "#18181b",
      },

      neutral: {
        DEFAULT: colors.neutral,
        50: "#fafafa",
        100: "#f5f5f5",
        200: "#e5e5e5",
        300: "#d4d4d4",
        400: "#a3a3a3",
        500: "#757575",
        600: "#525252",
        700: "#404040",
        800: "#262626",
        900: "#111111",
      },

      stone: {
        DEFAULT: colors.stone,
        50: "#fafaf9",
        100: "#f5f5f4",
        200: "#e7e5e4",
        300: "#d6d3d1",
        400: "#a8a29e",
        500: "#78716c",
        600: "#57534e",
        700: "#44403c",
        800: "#292524",
        900: "#1c1917",
      },

      red: {
        DEFAULT: colors.red,
        50: "#fef2f2",
        100: "#fee2e2",
        200: "#fc9797",
        300: "#fca5a5",
        400: "#f87171",
        500: "#f53939",
        600: "#ea0606",
        650: "#d41f1a",
        700: "#bd0000",
        800: "#991b1b",
        900: "#7f1d1d",
      },

      orange: {
        DEFAULT: colors.orange,
        50: "#fef5d3",
        100: "#ffedd5",
        200: "#fed7aa",
        300: "#fdba74",
        400: "#fb923c",
        500: "#f97316",
        600: "#ea580c",
        650: "#e03d00",
        700: "#c2410c",
        800: "#9a3412",
        900: "#7c2d12",
      },

      amber: {
        DEFAULT: colors.amber,
        50: "#fffbeb",
        100: "#fef3c7",
        200: "#fde68a",
        300: "#fcd34d",
        400: "#fbc400",
        500: "#f59e0b",
        600: "#d97706",
        700: "#b45309",
        800: "#92400e",
        900: "#78350f",
      },

      yellow: {
        DEFAULT: colors.yellow,
        50: "#fefce8",
        100: "#fef9c3",
        200: "#fef08a",
        300: "#fde047",
        400: "#fbcf33",
        500: "#eab308",
        600: "#ca8a04",
        700: "#a16207",
        800: "#854d0e",
        900: "#713f12",
      },

      lime: {
        DEFAULT: colors.lime,
        50: "#f7fee7",
        100: "#ecfccb",
        200: "#cdf59b",
        300: "#bef264",
        400: "#98ec2d",
        500: "#82d616",
        600: "#67b108",
        700: "#4d7c0f",
        800: "#3f6212",
        900: "#365314",
      },

      green: {
        DEFAULT: colors.green,
        50: "#f0fdf4",
        100: "#dcfce7",
        200: "#bbf7d0",
        300: "#86efac",
        400: "#4ade80",
        500: "#22c55e",
        600: "#17ad37",
        700: "#15803d",
        800: "#166534",
        900: "#14532d",
      },

      emerald: {
        DEFAULT: colors.emerald,
        50: "#ecfdf5",
        100: "#d1fae5",
        200: "#a7f3d0",
        300: "#6ee7b7",
        400: "#34d399",
        500: "#10b981",
        600: "#059669",
        700: "#047857",
        800: "#065f46",
        900: "#064e3b",
      },

      teal: {
        DEFAULT: colors.teal,
        50: "#f0fdfa",
        100: "#ccfbf1",
        200: "#99f6e4",
        300: "#5eead4",
        400: "#2dd4bf",
        500: "#14b8a6",
        550: "#329874",
        600: "#0d9488",
        700: "#0f766e",
        800: "#115e59",
        900: "#134e4a",
      },

      cyan: {
        DEFAULT: colors.cyan,
        50: "#ecfeff",
        100: "#cffafe",
        200: "#a5f3fc",
        300: "#67e8f9",
        400: "#21d4fd",
        500: "#17c1e8",
        600: "#08a1c4",
        700: "#0e7490",
        800: "#155e75",
        900: "#164e63",
      },

      sky: {
        DEFAULT: colors.sky,
        50: "#f0f9ff",
        100: "#e0f2fe",
        200: "#abe9f7",
        300: "#7dd3fc",
        400: "#38bdf8",
        500: "#0ea5e9",
        600: "#3ea1ec",
        700: "#0369a1",
        800: "#075985",
        900: "#0e456d",
      },

      blue: {
        DEFAULT: colors.blue,
        50: "#eff6ff",
        100: "#dbeafe",
        200: "#bfdbfe",
        300: "#93c5fd",
        400: "#60a5fa",
        500: "#3b82f6",
        600: "#2152ff",
        700: "#1d4ed8",
        800: "#344e86",
        900: "#00007d",
      },

      indigo: {
        DEFAULT: colors.indigo,
        50: "#eef2ff",
        100: "#e0e7ff",
        200: "#c7d2fe",
        300: "#a5b4fc",
        400: "#818cf8",
        500: "#6366f1",
        600: "#4f46e5",
        700: "#4338ca",
        800: "#3730a3",
        900: "#312e81",
      },

      violet: {
        DEFAULT: colors.violet,
        50: "#f5f3ff",
        100: "#ede9fe",
        200: "#ddd6fe",
        300: "#c4b5fd",
        400: "#a78bfa",
        500: "#8b5cf6",
        600: "#7c3aed",
        700: "#6d28d9",
        800: "#5b21b6",
        900: "#4c1d95",
      },

      purple: {
        DEFAULT: colors.purple,
        50: "#faf5ff",
        100: "#f3e8ff",
        200: "#e9d5ff",
        300: "#d8b4fe",
        400: "#c084fc",
        500: "#a855f7",
        600: "#9333ea",
        700: "#7928ca",
        800: "#6b21a8",
        900: "#581c87",
      },

      fuchsia: {
        DEFAULT: colors.fuchsia,
        50: "#fdf4ff",
        100: "#fae8ff",
        200: "#f5d0fe",
        300: "#e293d3",
        400: "#e879f9",
        500: "#cb0c9f",
        600: "#c026d3",
        700: "#a3017e",
        800: "#830866",
        900: "#701a75",
      },

      pink: {
        DEFAULT: colors.pink,
        50: "#fdf2f8",
        100: "#fce7f3",
        200: "#fbcfe8",
        300: "#f883dd",
        400: "#f472b6",
        500: "#ff0080",
        600: "#db2777",
        700: "#be185d",
        800: "#9d174d",
        900: "#831843",
      },

      rose: {
        DEFAULT: colors.rose,
        50: "#fff1f2",
        100: "#ffe4e6",
        200: "#fecdd3",
        300: "#fda4af",
        400: "#ff667c",
        500: "#f43f5e",
        600: "#e11d48",
        700: "#be123c",
        800: "#9f1239",
        900: "#881337",
      },
    }),
    columns: {
      auto: "auto",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
      8: "8",
      9: "9",
      10: "10",
      11: "11",
      12: "12",
      "3xs": "16rem",
      "2xs": "18rem",
      xs: "20rem",
      sm: "24rem",
      md: "28rem",
      lg: "32rem",
      xl: "36rem",
      "2xl": "42rem",
      "3xl": "48rem",
      "4xl": "56rem",
      "5xl": "64rem",
      "6xl": "72rem",
      "7xl": "80rem",
    },
    spacing: {
      unset: "unset",
      px: "1px",
      0: "0px",
      0.4: "0.1rem",
      0.5: "0.125rem",
      0.6: "0.15rem",
      0.75: "0.2rem",
      1: "0.25rem",
      1.2: "0.3rem",
      1.25: "0.3125rem",
      1.4: "0.35rem",
      1.5: "0.375rem",
      1.6: "0.4rem",
      1.75: "0.4375rem",
      1.8: "0.45rem",
      2: "0.5rem",
      2.2: "0.55rem",
      2.5: "0.625rem",
      2.6: "0.65rem",
      2.7: "0.675rem",
      2.8: "0.7rem",
      3: "0.75rem",
      3.4: "0.85rem",
      3.5: "0.875rem",
      3.6: "0.9rem",
      4: "1rem",
      4.5: "1.125rem",
      4.8: "1.2rem",
      5: "1.25rem",
      5.3: "1.3rem",
      5.4: "1.35rem",
      5.5: "1.375rem",
      5.6: "1.4rem",
      6: "1.5rem",
      6.5: "1.625rem",
      7: "1.75rem",
      8: "2rem",
      9: "2.25rem",
      10: "2.5rem",
      11: "2.75rem",
      12: "3rem",
      13: "3.25rem",
      14: "3.5rem",
      16: "4rem",
      19: "4.75rem",
      20: "5rem",
      24: "6rem",
      25: "6.25rem",
      28: "7rem",
      30: "7.5rem",
      32: "8rem",
      34: "8.5rem",
      36: "9rem",
      40: "10rem",
      42: "10.5rem",
      44: "11rem",
      46: "11.5rem",
      48: "12rem",
      50: "12.5rem",
      52: "13rem",
      54: "13.5rem",
      56: "14rem",
      60: "15rem",
      64: "16rem",
      68: "17rem",
      72: "18rem",
      75: "18.75rem",
      80: "20rem",
      85: "22rem",
      90: "22.5rem",
      92: "23rem",
      96: "24rem",
      100: "25rem",
      116: "29rem",
      120: "30rem",
      125: "31.25rem",
      135: "33.75rem",
      160: "40rem",
      180: "45rem",
      240: "60rem",
      285: "71.25rem",
      330: "82.5rem",
      "70/100": "70%",
      "31/100": "31%",
      "15/100": "15%",
      "1/100": "1%",
      "1/10": "10%",
      "1/2": "50%",
    },
    animation: {
      none: "none",
      spin: "spin 1s linear infinite",
      ping: "ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",
      pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      bounce: "bounce 1s infinite",
      "fade-up": "fade-up 1.5s both",
      drive: 'drive 2s ease-in-out infinite',
    },
    aspectRatio: {
      auto: "auto",
      square: "1 / 1",
      video: "16 / 9",
    },
    backdropBlur: ({ theme }) => theme("blur"),
    backdropBrightness: ({ theme }) => theme("brightness"),
    backdropContrast: ({ theme }) => theme("contrast"),
    backdropGrayscale: ({ theme }) => theme("grayscale"),
    backdropHueRotate: ({ theme }) => theme("hueRotate"),
    backdropInvert: ({ theme }) => theme("invert"),
    backdropOpacity: ({ theme }) => theme("opacity"),
    backdropSaturate: ({ theme }) => theme("saturate"),
    backdropSepia: ({ theme }) => theme("sepia"),
    backgroundColor: ({ theme }) => theme("colors"),
    backgroundImage: ({ theme }) => ({
      none: "none",
      "gradient-to-t": "linear-gradient(to top, var(--tw-gradient-stops))",
      "gradient-to-tr": "linear-gradient(to top right, var(--tw-gradient-stops))",
      "gradient-to-r": "linear-gradient(to right, var(--tw-gradient-stops))",
      "gradient-to-br": "linear-gradient(to bottom right, var(--tw-gradient-stops))",
      "gradient-to-b": "linear-gradient(to bottom, var(--tw-gradient-stops))",
      "gradient-to-bl": "linear-gradient(to bottom left, var(--tw-gradient-stops))",
      "gradient-to-l": "linear-gradient(to left, var(--tw-gradient-stops))",
      "gradient-to-tl": "linear-gradient(to top left, var(--tw-gradient-stops))",
    }),
    backgroundOpacity: ({ theme }) => theme("opacity"),
    backgroundPosition: {
      bottom: "bottom",
      center: "center",
      "x-25": "25% 0",
      left: "left",
      "left-bottom": "left bottom",
      "left-top": "left top",
      right: "right",
      "right-bottom": "right bottom",
      "right-top": "right top",
      top: "top",
    },
    backgroundSize: {
      auto: "auto",
      cover: "cover",
      contain: "contain",
      150: "150%",
    },
    blur: {
      0: "0",
      none: "0",
      sm: "4px",
      DEFAULT: "8px",
      md: "12px",
      lg: "16px",
      xl: "24px",
      "2xl": "30px",
      "3xl": "64px",
    },
    brightness: {
      0: "0",
      50: ".5",
      75: ".75",
      90: ".9",
      95: ".95",
      100: "1",
      105: "1.05",
      110: "1.1",
      125: "1.25",
      150: "1.5",
      200: "2",
    },
    borderColor: ({ theme }) => ({
      ...theme("colors"),
      DEFAULT: theme("colors.gray.200", "currentColor"),
    }),
    borderOpacity: ({ theme }) => theme("opacity"),
    borderRadius: ({ theme }) => ({
      ...theme("spacing"),
      none: "0px",
      inherit: "inherit",
      xs: "0.0625rem",
      sm: "0.125rem",
      DEFAULT: "0.25rem",
      md: "0.375rem",
      lg: "0.5rem",
      xl: "0.75rem",
      "2xl": "1rem",
      "3xl": "1.5rem",
      "3.5xl": "1.875rem",
      full: "9999px",
      circle: "50%",
      blur: "40px",
    }),
    borderWidth: ({ theme }) => ({
      ...theme("spacing"),
      DEFAULT: "1px",
      0: "0px",
      2: "2px",
      3: "3px",
      4: "4px",
      8: "8px",
    }),
    boxShadow: {
      "md-all":"0 0 10px 0 rgb(0 0 0 / 0.1), 1px 1px 2px -1px rgb(0 0 0 / 0.1)",
      "soft-xxs": "0 1px 5px 1px #ddd",
      "soft-xs": "0 3px 5px -1px rgba(0,0,0,.09),0 2px 3px -1px rgba(0,0,0,.07)",
      "soft-sm": "0 .25rem .375rem -.0625rem hsla(0,0%,8%,.12),0 .125rem .25rem -.0625rem hsla(0,0%,8%,.07)",
      "soft-md": "0 4px 7px -1px rgba(0,0,0,.11),0 2px 4px -1px rgba(0,0,0,.07)",
      "soft-lg": "0 2px 12px 0 rgba(0,0,0,.16)",
      "soft-xl": "0 20px 27px 0 rgba(0,0,0,.05)",
      "soft-dark-xl": "0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.2),0 1px 5px 0 rgba(0,0,0,.12)",
      "soft-2xl": "0 .3125rem .625rem 0 rgba(0,0,0,.12)",
      "soft-3xl": "0 8px 26px -4px hsla(0,0%,8%,.15),0 8px 9px -5px hsla(0,0%,8%,.06)",
      "soft-primary-outline": "0 0 4px 1px var(--color-primary)",
      blur: "inset 0 0 1px 1px hsla(0,0%,100%,.9),0 20px 27px 0 rgba(0,0,0,.05)",
      "dark-blur": "inset 0 0 1px 1px hsla(0,0%,100%,.4),0 20px 27px 0 rgba(0,0,0,.05)",
      DEFAULT: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
      inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
      sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
      md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
      lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
      xl: "0 23px 45px -11px hsla(0,0%,8%,.25)",
      "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)",
      none: "none",
    },
    boxShadowColor: ({ theme }) => theme("colors"),
    caretColor: ({ theme }) => theme("colors"),
    accentColor: ({ theme }) => ({
      ...theme("colors"),
      auto: "auto",
    }),
    contrast: {
      0: "0",
      50: ".5",
      75: ".75",
      100: "1",
      125: "1.25",
      150: "1.5",
      200: "2",
    },
    container: {
      center: true,
      padding: "1.5rem",
    },
    content: {
      none: "none",
    },
    cursor: {
      auto: "auto",
      default: "default",
      pointer: "pointer",
      wait: "wait",
      text: "text",
      move: "move",
      help: "help",
      "not-allowed": "not-allowed",
      none: "none",
      "context-menu": "context-menu",
      progress: "progress",
      cell: "cell",
      crosshair: "crosshair",
      "vertical-text": "vertical-text",
      alias: "alias",
      copy: "copy",
      "no-drop": "no-drop",
      grab: "grab",
      grabbing: "grabbing",
      "all-scroll": "all-scroll",
      "col-resize": "col-resize",
      "row-resize": "row-resize",
      "n-resize": "n-resize",
      "e-resize": "e-resize",
      "s-resize": "s-resize",
      "w-resize": "w-resize",
      "ne-resize": "ne-resize",
      "nw-resize": "nw-resize",
      "se-resize": "se-resize",
      "sw-resize": "sw-resize",
      "ew-resize": "ew-resize",
      "ns-resize": "ns-resize",
      "nesw-resize": "nesw-resize",
      "nwse-resize": "nwse-resize",
      "zoom-in": "zoom-in",
      "zoom-out": "zoom-out",
    },
    divideColor: ({ theme }) => theme("borderColor"),
    divideOpacity: ({ theme }) => theme("borderOpacity"),
    divideWidth: ({ theme }) => theme("borderWidth"),
    dropShadow: {
      sm: "0 1px 1px rgb(0 0 0 / 0.05)",
      DEFAULT: ["0 1px 2px rgb(0 0 0 / 0.1)", "0 1px 1px rgb(0 0 0 / 0.06)"],
      md: ["0 4px 3px rgb(0 0 0 / 0.07)", "0 2px 2px rgb(0 0 0 / 0.06)"],
      lg: ["0 10px 8px rgb(0 0 0 / 0.04)", "0 4px 3px rgb(0 0 0 / 0.1)"],
      xl: ["0 20px 13px rgb(0 0 0 / 0.03)", "0 8px 5px rgb(0 0 0 / 0.08)"],
      "2xl": "0 25px 25px rgb(0 0 0 / 0.15)",
      none: "0 0 #0000",
    },
    fill: ({ theme }) => theme("colors"),
    grayscale: {
      0: "0",
      DEFAULT: "100%",
    },
    hueRotate: {
      0: "0deg",
      15: "15deg",
      30: "30deg",
      60: "60deg",
      90: "90deg",
      180: "180deg",
    },
    invert: {
      0: "0",
      DEFAULT: "100%",
    },
    flex: {
      '1-0': "1 0 0%",
      1: "1 1 0%",
      auto: "1 1 auto",
      initial: "0 1 auto",
      none: "none",
      0: "0 0 auto",
    },
    flexBasis: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
      "1/2": "50%",
      "1/3": "33.333333%",
      "2/3": "66.666667%",
      "1/4": "25%",
      "2/4": "50%",
      "3/4": "75%",
      "1/5": "20%",
      "2/5": "40%",
      "3/5": "60%",
      "4/5": "80%",
      "1/6": "16.666667%",
      "2/6": "33.333333%",
      "3/6": "50%",
      "4/6": "66.666667%",
      "5/6": "83.333333%",
      "1/12": "8.333333%",
      "2/12": "16.666667%",
      "3/12": "25%",
      "4/12": "33.333333%",
      "5/12": "41.666667%",
      "6/12": "50%",
      "7/12": "58.333333%",
      "8/12": "66.666667%",
      "9/12": "75%",
      "10/12": "83.333333%",
      "11/12": "91.666667%",
      full: "100%",
    }),
    flexGrow: {
      0: "0",
      DEFAULT: "1",
    },
    flexShrink: {
      0: "0",
      DEFAULT: "1",
    },

    fontSize: ({ theme }) => ({
      ...theme("spacing"),
      inherit: "inherit",
      "3xs": ["0.5rem", { lineHeight: "1rem" }],
      xxs: ["0.65rem", { lineHeight: "1rem" }],
      xs: ["0.75rem", { lineHeight: "1rem" }],
      sm: ["0.875rem", { lineHeight: "1.5rem" }],
      base: ["1rem", { lineHeight: "1.5rem" }],
      lg: ["1.125rem", { lineHeight: "1.75rem" }],
      xl: ["1.25rem", { lineHeight: "1.75rem" }],
      "2xl": ["1.5rem", { lineHeight: "2rem" }],
      "3xl": ["1.875rem", { lineHeight: "2.25rem" }],
      "4xl": ["2.25rem", { lineHeight: "2.5rem" }],
      "5xl": ["3rem", { lineHeight: "1" }],
      "6xl": ["3.75rem", { lineHeight: "1" }],
      "7xl": ["4.5rem", { lineHeight: "1" }],
      "8xl": ["5rem", { lineHeight: "1" }],
      "9xl": ["6rem", { lineHeight: "1" }],
      "10xl": ["8rem", { lineHeight: "1" }],
      "size-banner-calculate": ["calc(1.625rem+4.5vw)"],
    }),
    fontWeight: {
      thin: "100",
      extralight: "200",
      light: "300",
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
      extrabold: "800",
      black: "900",
    },
    gap: ({ theme }) => theme("spacing"),
    gradientColorStops: ({ theme }) => theme("colors"),
    gridAutoColumns: {
      auto: "auto",
      min: "min-content",
      max: "max-content",
      fr: "minmax(0, 1fr)",
    },
    gridAutoRows: {
      auto: "auto",
      min: "min-content",
      max: "max-content",
      fr: "minmax(0, 1fr)",
    },
    gridColumn: {
      auto: "auto",
      "span-1": "span 1 / span 1",
      "span-2": "span 2 / span 2",
      "span-3": "span 3 / span 3",
      "span-4": "span 4 / span 4",
      "span-5": "span 5 / span 5",
      "span-6": "span 6 / span 6",
      "span-7": "span 7 / span 7",
      "span-8": "span 8 / span 8",
      "span-9": "span 9 / span 9",
      "span-10": "span 10 / span 10",
      "span-11": "span 11 / span 11",
      "span-12": "span 12 / span 12",
      "span-full": "1 / -1",
    },
    gridColumnEnd: {
      auto: "auto",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
      8: "8",
      9: "9",
      10: "10",
      11: "11",
      12: "12",
      13: "13",
    },
    gridColumnStart: {
      auto: "auto",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
      8: "8",
      9: "9",
      10: "10",
      11: "11",
      12: "12",
      13: "13",
    },
    gridRow: {
      auto: "auto",
      "span-1": "span 1 / span 1",
      "span-2": "span 2 / span 2",
      "span-3": "span 3 / span 3",
      "span-4": "span 4 / span 4",
      "span-5": "span 5 / span 5",
      "span-6": "span 6 / span 6",
      "span-full": "1 / -1",
    },
    gridRowStart: {
      auto: "auto",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
    },
    gridRowEnd: {
      auto: "auto",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
    },
    gridTemplateColumns: {
      none: "none",
      1: "repeat(1, minmax(0, 1fr))",
      2: "repeat(2, minmax(0, 1fr))",
      3: "repeat(3, minmax(0, 1fr))",
      4: "repeat(4, minmax(0, 1fr))",
      5: "repeat(5, minmax(0, 1fr))",
      6: "repeat(6, minmax(0, 1fr))",
      7: "repeat(7, minmax(0, 1fr))",
      8: "repeat(8, minmax(0, 1fr))",
      9: "repeat(9, minmax(0, 1fr))",
      10: "repeat(10, minmax(0, 1fr))",
      11: "repeat(11, minmax(0, 1fr))",
      12: "repeat(12, minmax(0, 1fr))",
    },
    gridTemplateRows: {
      none: "none",
      1: "repeat(1, minmax(0, 1fr))",
      2: "repeat(2, minmax(0, 1fr))",
      3: "repeat(3, minmax(0, 1fr))",
      4: "repeat(4, minmax(0, 1fr))",
      5: "repeat(5, minmax(0, 1fr))",
      6: "repeat(6, minmax(0, 1fr))",
    },
    height: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
      "1/2": "50%",
      "1/3": "33.333333%",
      "2/3": "66.666667%",
      "1/4": "25%",
      "2/4": "50%",
      "3/4": "75%",
      "1/5": "20%",
      "2/5": "40%",
      "3/5": "60%",
      "4/5": "80%",
      "1/6": "16.666667%",
      "2/6": "33.333333%",
      "3/6": "50%",
      "4/6": "66.666667%",
      "5/6": "83.333333%",
      full: "100%",
      sidenav: "calc(100vh - 305px)",
      // sidenav: "calc(100vh - 360px)", // for pro btn
      screen: "100vh",
      min: "min-content",
      max: "max-content",
      fit: "fit-content",
    }),
    inset: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
      "1/2": "50%",
      "1/3": "33.333333%",
      "2/3": "66.666667%",
      "1/4": "25%",
      "2/4": "50%",
      "3/4": "75%",
      full: "100%",
    }),
    keyframes: {
        drive: {
            '0%': { transform: 'translateX(100%)' }, // Startet außerhalb des rechten Bildschirmrandes
            '100%': { transform: 'translateX(-100%)' }, // Endet außerhalb des linken Bildschirmrandes
        },
      "fade-up": {
        from: {
          opacity: "0",
          transform: "translateY(100%)",
        },
        to: {
          opacity: "1",
        },
      },
      spin: {
        to: {
          transform: "rotate(360deg)",
        },
      },
      ping: {
        "75%, 100%": {
          transform: "scale(2)",
          opacity: "0",
        },
      },
      pulse: {
        "50%": {
          opacity: ".5",
        },
      },
      bounce: {
        "0%, 100%": {
          transform: "translateY(-25%)",
          animationTimingFunction: "cubic-bezier(0.8,0,1,1)",
        },
        "50%": {
          transform: "none",
          animationTimingFunction: "cubic-bezier(0,0,0.2,1)",
        },
      },
    },
    letterSpacing: {
      tighter: "-0.05em",
      tight: "-0.025em",
      "tight-soft": "-0.025rem",
      normal: "0em",
      none: "0",
      wide: "0.025em",
      wider: "0.05em",
      widest: "0.1em",
    },
    lineHeight: ({ theme }) => ({
      ...theme("spacing"),
      none: "1",
      tighter: "1.2",
      tight: "1.25",
      snug: "1.375",
      button: "1.3",
      pro: "1.4",
      normal: "1.5",
      default: "1.6",
      relaxed: "1.625",
      loose: "2",
    }),
    listStyleType: {
      none: "none",
      disc: "disc",
      decimal: "decimal",
    },
    margin: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
    }),
    maxHeight: ({ theme }) => ({
      ...theme("spacing"),
      full: "100%",
      screen: "100vh",
      min: "min-content",
      max: "max-content",
      fit: "fit-content",
      "85-screen": "85vh",
      "75-screen": "75vh",
      "70-screen": "70vh",
      "50-screen": "50vh",
    }),
    maxWidth: ({ theme, breakpoints }) => ({
      ...theme("spacing"),
      sidebar: "15.625rem",
      none: "none",
      0: "0rem",
      xs: "20rem",
      sm: "24rem",
      md: "28rem",
      lg: "32rem",
      xl: "36rem",
      "2xl": "42rem",
      "3xl": "48rem",
      "4xl": "56rem",
      "5xl": "64rem",
      "6xl": "72rem",
      "7xl": "80rem",
      full: "100%",
      min: "min-content",
      max: "max-content",
      fit: "fit-content",
      prose: "65ch",
      ...breakpoints(theme("screens")),
    }),
    minHeight: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
      0: "0px",
      6: "1.5rem",
      full: "100%",
      screen: "100vh",
      "85-screen": "85vh",
      "75-screen": "75vh",
      "70-screen": "70vh",
      "50-screen": "50vh",
      min: "min-content",
      max: "max-content",
      fit: "fit-content",
    }),
    minWidth: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
      0: "0px",
      full: "100%",
      min: "min-content",
      max: "max-content",
      fit: "fit-content",
    }),

    objectPosition: {
      bottom: "bottom",
      center: "center",
      left: "left",
      "left-bottom": "left bottom",
      "left-top": "left top",
      right: "right",
      "right-bottom": "right bottom",
      "right-top": "right top",
      top: "top",
    },
    opacity: {
      0: "0",
      3: "0.03",
      5: "0.05",
      10: "0.1",
      12.5: "0.125",
      20: "0.2",
      25: "0.25",
      30: "0.3",
      40: "0.4",
      50: "0.5",
      60: "0.6",
      65: "0.65",
      70: "0.7",
      75: "0.75",
      80: "0.8",
      85: "0.85",
      90: "0.9",
      95: "0.95",
      100: "1",
    },
    order: {
      first: "-9999",
      last: "9999",
      none: "0",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
      8: "8",
      9: "9",
      10: "10",
      11: "11",
      12: "12",
    },
    padding: ({ theme }) => theme("spacing"),
    placeholderColor: ({ theme }) => theme("colors"),
    placeholderOpacity: ({ theme }) => theme("opacity"),
    outlineColor: ({ theme }) => theme("colors"),
    outlineOffset: {
      0: "0px",
      1: "1px",
      2: "2px",
      4: "4px",
      8: "8px",
    },
    outlineWidth: {
      0: "0px",
      1: "1px",
      2: "2px",
      4: "4px",
      8: "8px",
    },
    ringColor: ({ theme }) => ({
      DEFAULT: theme("colors.blue.500", "#3b82f6"),
      ...theme("colors"),
    }),
    ringOffsetColor: ({ theme }) => theme("colors"),
    ringOffsetWidth: {
      0: "0px",
      1: "1px",
      2: "2px",
      4: "4px",
      8: "8px",
    },
    ringOpacity: ({ theme }) => ({
      DEFAULT: "0.5",
      ...theme("opacity"),
    }),
    ringWidth: {
      DEFAULT: "3px",
      0: "0px",
      1: "1px",
      2: "2px",
      4: "4px",
      8: "8px",
    },
    rotate: {
      0: "0deg",
      1: "1deg",
      2: "2deg",
      3: "3deg",
      6: "6deg",
      12: "12deg",
      45: "45deg",
      90: "90deg",
      180: "180deg",
    },
    saturate: {
      0: "0",
      50: ".5",
      100: "1",
      150: "1.5",
      200: "2",
    },
    scale: {
      0: "0",
      50: ".5",
      60: ".6",
      70: ".7",
      75: ".75",
      90: ".9",
      95: ".95",
      100: "1",
      102: "1.02",
      105: "1.05",
      110: "1.1",
      120: "1.2",
      125: "1.25",
      150: "1.5",
    },
    scrollMargin: ({ theme }) => ({
      ...theme("spacing"),
    }),
    scrollPadding: ({ theme }) => theme("spacing"),
    sepia: {
      0: "0",
      DEFAULT: "100%",
    },
    skew: {
      0: "0deg",
      1: "1deg",
      2: "2deg",
      3: "3deg",
      6: "6deg",
      10: "10deg",
      12: "12deg",
    },
    space: ({ theme }) => ({
      ...theme("spacing"),
    }),
    stroke: ({ theme }) => ({
      ...theme("colors"),
      none: "none",
    }),
    strokeWidth: {
      0: "0",
      1: "1",
      2: "2",
    },
    textColor: ({ theme }) => theme("colors"),
    textDecorationColor: ({ theme }) => theme("colors"),
    textDecorationThickness: {
      auto: "auto",
      "from-font": "from-font",
      0: "0px",
      1: "1px",
      2: "2px",
      4: "4px",
      8: "8px",
    },
    textUnderlineOffset: {
      auto: "auto",
      0: "0px",
      1: "1px",
      2: "2px",
      4: "4px",
      8: "8px",
    },
    textIndent: ({ theme }) => ({
      ...theme("spacing"),
    }),
    textOpacity: ({ theme }) => theme("opacity"),
    transformOrigin: {
      center: "center",
      top: "top",
      "top-right": "top right",
      right: "right",
      "bottom-right": "bottom right",
      bottom: "bottom",
      "bottom-left": "bottom left",
      left: "left",
      "top-left": "top left",
      "10-10": "10% 10%",
      "10-90": "10% 90%",
    },
    transitionDelay: {
      0: "0ms",
      75: "75ms",
      100: "100ms",
      150: "150ms",
      200: "200ms",
      300: "300ms",
      500: "500ms",
      700: "700ms",
      1000: "1000ms",
    },
    transitionDuration: {
      DEFAULT: "150ms",
      75: "75ms",
      100: "100ms",
      150: "150ms",
      200: "200ms",
      250: "250ms",
      300: "300ms",
      350: "350ms",
      500: "500ms",
      600: "600ms",
      700: "700ms",
      1000: "1000ms",
    },
    transitionProperty: {
      none: "none",
      all: "all",
      DEFAULT: "color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",
      colors: "color, background-color, border-color, text-decoration-color, fill, stroke",
      color: "color",
      height: "height",
      "max-height": "max-height",
      opacity: "opacity",
      shadow: "box-shadow",
      background: "background-color",
      "border-color": "border-color",
      transform: "transform",
      width: "width",
    },
    transitionTimingFunction: {
      DEFAULT: "cubic-bezier(0.4, 0, 0.2, 1)",
      bounce: "cubic-bezier(0.34, 1.61, 0.7, 1.3)",
      linear: "linear",
      in: "cubic-bezier(0.4, 0, 1, 1)",
      "in-out": "cubic-bezier(0.4, 0, 0.2, 1)",
      out: "cubic-bezier(0, 0, 0.2, 1)",
      soft: "cubic-bezier(0.25, 0.1, 0.25, 1)",
      "soft-in": "cubic-bezier(0.42, 0, 1, 1)",
      "soft-in-out": "cubic-bezier(0.42, 0, 0.58, 1)",
      "soft-out": "cubic-bezier(0, 0, 0.58, 1)",
    },
    translate: ({ theme }) => ({
      ...theme("spacing"),
      "1/2": "50%",
      "1/3": "33.333333%",
      "2/3": "66.666667%",
      "1/4": "25%",
      "2/4": "50%",
      "3/4": "75%",
      full: "100%",
    }),
    width: ({ theme }) => ({
      auto: "auto",
      ...theme("spacing"),
      "1/100": "1%",
      "3/100": "3%",
      "5/100": "5%",
      "17/100": "17%",
      "80/100": "80%",
      "1/2": "50%",
      "1/3": "33.333333%",
      "2/3": "66.666667%",
      "1/4": "25%",
      "2/4": "50%",
      "3/4": "75%",
      "1/5": "20%",
      "2/5": "40%",
      "3/5": "60%",
      "4/5": "80%",
      "1/6": "16.666667%",
      "2/6": "33.333333%",
      "3/6": "50%",
      "4/6": "66.666667%",
      "5/6": "83.333333%",
      "1/10": "10%",
      "3/10": "30%",
      "9/10": "90%",
      "1/12": "8.333333%",
      "2/12": "16.666667%",
      "3/12": "25%",
      "4/12": "33.333333%",
      "5/12": "41.666667%",
      "6/12": "50%",
      "7/12": "58.333333%",
      "8/12": "66.666667%",
      "9/12": "75%",
      "10/12": "83.333333%",
      "11/12": "91.666667%",
      full: "100%",
      screen: "100vw",
      min: "min-content",
      max: "max-content",
      fit: "fit-content",
    }),
    willChange: {
      auto: "auto",
      scroll: "scroll-position",
      contents: "contents",
      transform: "transform",
    },
    zIndex: {
      auto: "auto",
      0: "0",
      1: "1",
      2: "2",
      10: "10",
      20: "20",
      30: "30",
      40: "40",
      50: "50",
      100: "100",
      110: "110",
      990: "990",
      sticky: "1020",
    },
  },
  variants: {
    display: ["responsive", "dropdown"],
  },
  variantOrder: ["first", "last", "odd", "even", "visited", "checked", "empty", "read-only", "group-hover", "group-focus", "focus-within", "hover", "focus", "focus-visible", "active", "disabled"],

  plugins: [
    plugin(function ({ addComponents, addUtilities }) {
      addUtilities({
        ".transform3d": {
          transform: "perspective(999px) rotateX(0deg) translateZ(0)",
        },
        ".transform3d-hover": {
          transform: "perspective(999px) rotateX(7deg) translate3d(0,-4px,5px)",
        },
        ".transform-dropdown": {
          transform: "perspective(999px) rotateX(-10deg) translateZ(0) translate3d(0,37px,0)",
        },
        ".transform-dropdown-show": {
          transform: "perspective(999px) rotateX(0deg) translateZ(0) translate3d(0,37px,5px)",
        },
        ".transform-dropdown-nested": {
          transform: "perspective(999px) rotateX(0deg) translateZ(0) translateZ(5px)",
        },
        ".flex-wrap-inherit": {
          "flex-wrap": "inherit",
        },
        ".text-align-inherit": {
          "text-align": "inherit",
        },
        ".clip-rect-0": {
          "clip": "rect(0,0,0,0)",
        },
      });
      const typography = {
        a: {
          "letter-spacing": "-0.025rem",
          "text-decoration": "none",
        },

        hr: {
          margin: "1rem 0",
          color: "inherit",
          border: "0",
          opacity: ".25",
        },

        img: {
          maxWidth: "none",
          display: "inline",
        },

        label: {
          display: "inline-block",
        },

        p: {
          "font-size": "1rem",
          "line-height": "1.625",
          "font-weight": "400",
          "margin-bottom": "1rem",
        },

        small: {
          "font-size": ".875em",
        },

        svg: {
          display: "inline",
        },

        table: {
          "border-collapse": "inherit",
        },

        "h1, h2, h3, h4, h5, h6, a": {
          color: "#000000",
        },
        "h1, h2, h3, h4, h5, h6": {
          "margin-bottom": ".5rem",
        },

        "h1, h2, h3, h4": {
          "letter-spacing": "-0.05rem",
        },

        "h1, h2, h3": {
          "font-weight": "700",
        },
        "h4, h5, h6": {
          "font-weight": "600",
        },

        h1: {
          "font-size": "3rem",
          "line-height": "1.25",
        },
        h2: {
          "font-size": "2.25rem",
          "line-height": "1.3",
        },
        h3: {
          "font-size": "1.875rem",
          "line-height": "1.375",
        },
        h4: {
          "font-size": "1.5rem",
          "line-height": "1.375",
        },
        h5: {
          "font-size": "1.25rem",
          "line-height": "1.375",
        },
        h6: {
          "font-size": "1rem",
          "line-height": "1.625",
        },
      };
      addComponents(typography);
    }),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities({
        "bg-gradient": (angle) => ({
          "background-image": `linear-gradient(${angle}, var(--tw-gradient-stops))`,
        }),
      });
    }),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          "translate-z": (value) => ({
            transform: `translateZ(${value})`,
          }),
        },
        { values: theme("spacing") }
      );
    }),
  ],
};
