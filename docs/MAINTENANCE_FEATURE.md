# Maintenance Feature

Das Maintenance-Feature ermöglicht die Verwaltung von Wartungs- und Reparaturarbeiten an Standorten, Ladesäulen und Connectoren.

## Features

### 1. Kategorien-Management
- **Standard-Kategorien**: DGUV-V3, Shutter, RFID, Ethernet, Sonstiges
- **Benutzerdefinierte Kategorien**: Admins können neue Kategorien hinzufügen
- **Kategorie-Schutz**: Standard-Kategorien können nicht gelöscht werden

### 2. Wartungseinträge
- **Flexible Ziele**: Standort, ein<PERSON>ne EVSE, Connector oder alle Ladesäulen
- **OU-abhängige Filterung**: Einträge werden basierend auf selectedOu.id gefiltert
- **Mobile Optimierung**: Schnelle Eingabe über mobile-optimierte Formulare
- **Shortcuts**: "Alle Ladesäulen" Toggle für schnelle Auswahl

### 3. DGUV-V3 Erinnerungen
- **Automatische Berechnung**: Nächste Prüfung wird automatisch auf 50 Wochen gesetzt
- **Cronjob**: Wöchentliche Prüfung auf fällige/überfällige Prüfungen
- **Benachrichtigungen**: E-Mail und Admin-Benachrichtigungen
- **Farbkodierung**: Überfällige (rot) und bald fällige (gelb) Prüfungen

## Datenbank-Schema

### MaintenanceCategory
```sql
- id: String (Primary Key)
- name: String (Unique)
- description: String (Optional)
- isDefault: Boolean
- createdAt: DateTime
- updatedAt: DateTime
```

### MaintenanceRecord
```sql
- id: String (Primary Key)
- date: DateTime
- description: String
- notes: String (Optional)
- targetType: MaintenanceTargetType (LOCATION, EVSE, CONNECTOR, ALL_EVSES)
- locationId: String (Optional)
- evseId: String (Optional)
- connectorId: String (Optional)
- categoryId: String (Foreign Key)
- ouId: String (Foreign Key)
- userId: String (Foreign Key)
- nextDueDate: DateTime (Optional, für DGUV-V3)
- createdAt: DateTime
- updatedAt: DateTime
```

## API-Endpunkte

### Kategorien
- `GET /api/maintenance/categories` - Alle Kategorien abrufen
- `POST /api/maintenance/categories` - Neue Kategorie erstellen
- `PUT /api/maintenance/categories/[id]` - Kategorie bearbeiten
- `DELETE /api/maintenance/categories/[id]` - Kategorie löschen

### Wartungseinträge
- `GET /api/maintenance/records` - Wartungseinträge abrufen (OU-gefiltert)
- `POST /api/maintenance/records` - Neuen Wartungseintrag erstellen
- `PUT /api/maintenance/records/[id]` - Wartungseintrag bearbeiten
- `DELETE /api/maintenance/records/[id]` - Wartungseintrag löschen

### Hilfsdaten
- `GET /api/maintenance/locations` - Standorte und EVSEs abrufen (OU-gefiltert)

## Cronjob

### DGUV-V3 Erinnerungen
- **Zeitplan**: Jeden Montag um 8:00 Uhr
- **Datei**: `src/app/api/cronjobs/checkDGUVReminders/route.ts`
- **Funktionen**:
  - Prüfung auf überfällige DGUV-V3 Prüfungen
  - Warnung 30 Tage vor Fälligkeit
  - E-Mail-Benachrichtigungen an `<EMAIL>`
  - Admin-Benachrichtigungen im System

## Installation & Setup

### 1. Migration ausführen
```bash
npx prisma migrate dev --name add_maintenance_models
```

### 2. Standard-Kategorien erstellen
```bash
npm run seed-maintenance-categories
```

### 3. Cronjob aktivieren
Der Cronjob wird automatisch mit Quirrel aktiviert.

## Verwendung

### 1. Zugriff
- **URL**: `/maintenance`
- **Berechtigung**: Nur für ADMIN-Rolle

### 2. Schnelle Wartungseintragung
- Mobile-optimiertes Formular auf der Hauptseite
- Standort auswählen → optional EVSE auswählen
- "Alle Ladesäulen" Toggle für standortweite Wartung
- Kategorie und Beschreibung eingeben

### 3. Erweiterte Eingabe
- Vollständiges Modal-Formular
- Connector-spezifische Wartung
- Detaillierte Notizen

### 4. Kategorien verwalten
- Neue Kategorien hinzufügen
- Bestehende Kategorien bearbeiten
- Kategorien löschen (nur wenn keine Einträge vorhanden)

## Mobile Optimierung

### Responsive Design
- Mobile-first Ansatz
- Touch-optimierte Buttons
- Kompakte Formulare
- Horizontales Scrollen für Tabellen

### Quick Actions
- Ein-Klick EVSE-Auswahl
- Schnelle Kategorie-Auswahl
- "Alle Ladesäulen" Toggle
- Datum standardmäßig auf heute gesetzt

## Benachrichtigungen

### DGUV-V3 Erinnerungen
- **Überfällig**: Rote Warnung + E-Mail
- **Fällig in 30 Tagen**: Gelbe Warnung + E-Mail
- **E-Mail-Inhalt**: Liste aller fälligen Prüfungen mit Details
- **Admin-Benachrichtigungen**: Im System für alle Admins

### E-Mail-Template
- HTML-formatiert
- Überfällige Prüfungen (rot markiert)
- Anstehende Prüfungen (gelb markiert)
- Link zur Maintenance-Verwaltung
- Automatisch generiert

## Sicherheit

### Zugriffskontrolle
- Nur ADMIN-Rolle hat Zugriff
- OU-basierte Filterung aller Daten
- Benutzer sehen nur Daten ihrer OU und Child-OUs

### Datenvalidierung
- Server-seitige Validierung aller Eingaben
- Enum-Validierung für targetType
- Existenz-Prüfung für Kategorien und Standorte

## Erweiterungsmöglichkeiten

### Geplante Features
- PDF-Export von Wartungsberichten
- Wartungsintervalle für andere Kategorien
- Foto-Upload für Wartungsarbeiten
- QR-Code-Scanner für EVSE-Auswahl
- Wartungskalender-Ansicht
- Techniker-Zuordnung
