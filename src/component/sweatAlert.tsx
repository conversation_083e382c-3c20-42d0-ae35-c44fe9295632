const SweatAlert = ({
  children,
  text,
  className,
}: {
  text: string;
  children?: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className="w-full max-w-full shrink-0 px-3">
      <div className="relative flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl">
        <div className="flex-auto p-6 text-center">
          <p>{text}</p>
          {children}
        </div>
      </div>
    </div>
  );
};
export default SweatAlert;
