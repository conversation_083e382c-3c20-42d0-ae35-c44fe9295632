"use client";
import { useSoftUI<PERSON>ontroller, setMiniSidenav } from "../context";
import { useEffect, useRef } from "react";

interface SidebarWrapperProps {
  align?: "left" | "right";
  children: React.ReactNode;
}

const SidebarWrapper = ({ align = "left", children }: SidebarWrapperProps) => {
  const [controller, dispatch] = useSoftUIController();
  const sidebarRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setMiniSidenav(dispatch, false);
      }
    };
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (
        target.id !== "id-menubutton" &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        setMiniSidenav(dispatch, false);
      }
    };

    // Event-Listener hinzufügen
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKey);

    return () => {
      // Event-Listener entfernen, wenn die Komponente unmountet wird
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKey);
    };
  }, [sidebarRef, dispatch, controller.miniSidenav]);
  const getTranslateClass = () => {
    if (align == "left") {
      return "";
    } else {
      return "translate-x-full";
    }
  };
  return (
    <aside
      ref={sidebarRef}
      className={`fixed inset-y-0 ${align}-0 z-990 block w-full max-w-64 -translate-x-full flex-wrap items-center justify-between overflow-y-auto 
            rounded-2xl border-0  bg-white p-0 shadow-blur 
            shadow-md transition-all duration-200 ease-soft-in-out xl:ml-4 xl:translate-x-0 xl:bg-transparent
            ${controller.miniSidenav ? "translate-x-0" : getTranslateClass()}
            `}
      id="sidenav-main"
    >
      {children}
    </aside>
  );
};

export default SidebarWrapper;
