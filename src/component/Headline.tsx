import React from "react";

interface Props {
  title: string;
}

export const Headline = ({ title }: Props) => {
  // Das aktuelle Datum und Uhrzeit im gewünschten Format abrufen
  const now = new Date();
  const formattedDate = `${now.getDate()}.${String(now.getMonth() + 1).padStart(
    2,
    "0",
  )}.${now.getFullYear()}, ${String(now.getHours()).padStart(2, "0")}:${String(
    now.getMinutes(),
  ).padStart(2, "0")} Uhr`;

  return (
    <div className="mb-7 flex flex-col p-4">
      <span className="text-xl font-bold">{title}</span>
      <span className="text-eul-gray">{formattedDate}</span>
    </div>
  );
};

export default Headline;
