"use server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Session } from "next-auth";
import { Role } from "@prisma/client";
import { getOusBelowUserOu } from "~/server/model/ou/func";

export const selectOuForUser = async (ouId: string) => {
  const session = (await getServerSession(authOptions)) as Session;
  if (!session || !session?.user) {
    //|| session?.user?.role !== Role.ADMIN) {
    return false;
  }
  // if not admin check whether user is allowed to change ou
  if (ouId !== session.user.ou.id && session?.user?.role !== Role.ADMIN) {
    const allOus = await getOusBelowUserOu(session.user.email);
    const valid = allOus.find((ouItem) => ouItem.id == ouId);
    if (!valid) {
      return false;
    }
  }

  void (await prisma.user.update({
    where: {
      email: session.user.email,
    },
    data: {
      selectedOuId: ouId,
    },
  }));
  return true;
};
