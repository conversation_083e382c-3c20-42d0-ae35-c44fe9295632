"use client";

import React, { useContext, useState } from "react";
import DateContext from "./dateContext";
import Button from "~/component/button";
import Dropdown from "~/app/(app)/util/Dropdown";
import { LuCalendarClock } from "react-icons/lu";

const DatePickerPresets = () => {
  const { setStartDate, setEndDate } = useContext(DateContext);
  const [selectedYear, setSelectedYear] = useState(0);

  const setYear = (year: number) => {
    setSelectedYear(year);
    if (!year) return;

    const firstDayOfYear = new Date(year, 0, 1); // 1. Januar des ausgewählten Jahres
    const lastDayOfYear = new Date(year, 11, 31); // 31. Dezember des ausgewählten Jahres
    setStartDate(firstDayOfYear);
    setEndDate(lastDayOfYear);
  };

  function getMonthDates(offset: number): [Date, Date] {
    const today = new Date();
    const targetMonth = new Date(today.getFullYear(), today.getMonth() + offset, 1);
    const firstDay = new Date(targetMonth.getFullYear(), targetMonth.getMonth(), 1);
    const lastDay = new Date(targetMonth.getFullYear(), targetMonth.getMonth() + 1, 0);
    return [firstDay, lastDay];
  }

  const setDatePicker = (offset: number) => {
    const [fistDayFromLastMoth, lastDayFromLastMonth] = getMonthDates(offset);
    setStartDate(fistDayFromLastMoth);
    setEndDate(lastDayFromLastMonth);
    setSelectedYear(-1);
  };

  const handleYearChange = (year: string) => {
    setYear(+year);
  };

  const years = Array.from({ length: new Date().getFullYear() - 2021 }, (_, i) => 2022 + i);

  return (
    <div className={"mb-3 mt-3 flex flex-col justify-center gap-1 sm:flex-row sm:justify-end"}>
      <Dropdown
        options={years.map((year, index) => {
          return { id: year.toString(), label: year.toString(), selected: year == selectedYear };
        })}
        canDelete={false}
        onChange={handleYearChange}
        onDelete={() => console.log("delete")}
        icon={<LuCalendarClock size={18} className={"mr-1"} />}
      />

      <Button onClick={() => setDatePicker(-2)}>vorletzter Monat</Button>
      <Button onClick={() => setDatePicker(-1)}>letzter Monat</Button>
      <Button onClick={() => setDatePicker(0)}>aktueller Monat</Button>
    </div>
  );
};

export default DatePickerPresets;
