"use client";

import React, { useContext, useEffect } from "react";
import DateContext from "./dateContext";

const DateRangePicker: React.FC = () => {
  const { startDate, endDate, setStartDate, setEndDate } = useContext(DateContext);

  return (
    <div className={"flex justify-end gap-x-2"}>
      <div className={"flex-none items-center"}>Zeitraum:</div>
      <div className={"flex-none items-center"}>
        <input
          type={"date"}
          className={""}
          value={startDate.toLocaleString("sv-SE").slice(0, 10)}
          onChange={(date: React.ChangeEvent<HTMLInputElement>) => {
            setStartDate(date.target.valueAsDate || new Date());
          }}
        />
      </div>
      <div className={"flex-nonev items-center"}>bis</div>
      <div className={"flex-none items-center"}>
        <input
          type={"date"}
          className={""}
          value={endDate.toLocaleString("sv-SE").slice(0, 10)}
          onChange={(date) => {
            return setEndDate(date.target.valueAsDate || new Date());
          }}
        />
      </div>
    </div>
  );
};

export default DateRangePicker;
