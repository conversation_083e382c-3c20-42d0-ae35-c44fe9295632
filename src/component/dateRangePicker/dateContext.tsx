"use client";
import { createContext } from "react";

const date = new Date();
const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

const DateContext = createContext<{
  startDate: Date;
  endDate: Date;
  setStartDate: (value: Date) => void;
  setEndDate: (value: Date) => void;
}>({
  startDate: firstDay,
  endDate: lastDay,
  setStartDate: () => {
    return {};
  },
  setEndDate: () => {
    return {};
  },
});

export default DateContext;
