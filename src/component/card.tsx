const Card = ({
  children,
  header_left,
  header_right,
  className,
}: {
  children: React.ReactNode;
  header_left?: React.ReactElement | string;
  header_right?: React.ReactElement;
  className?: string;
}) => {
  return (
    <div
      className={`relative flex h-full min-w-0 flex-col break-words rounded-md border-0 border-solid border-slate-100 bg-white bg-clip-border shadow-soft-xl dark:border-slate-700 dark:bg-gray-950 dark:shadow-soft-dark-xl ${className}`}
    >
      <div className="rounded-t-md p-4 pb-0">
        <div className="-mx-3 flex flex-wrap">
          <div className="w-full max-w-full shrink-0 px-3 md:w-6/12 md:flex-0">
            <h6 className="mb-0 text-primary dark:text-white">{header_left && header_left}</h6>
          </div>
          <div className="flex w-full max-w-full shrink-0 items-center justify-end px-3 md:w-6/12 md:flex-0">
            <small>{header_right && header_right}</small>
          </div>
        </div>
      </div>
      <div className="flex-auto p-4">{children}</div>
    </div>
  );
};

export default Card;
