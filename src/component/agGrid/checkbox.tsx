import React from "react";
import { ICellRendererParams } from "ag-grid-community";

const CheckboxCellRenderer = (props: ICellRendererParams) => {
  const onChange = (event: any) => {
    if (props.node && props.colDef) {
      props.node.setDataValue(String(props.colDef), event.target.checked);
    }
  };

  const { value } = props;
  const disabled = props.colDef && props.colDef.editable === false;

  return (
    <input type="checkbox" checked={value ? true : false} onChange={onChange} disabled={disabled} />
  );
};

export default CheckboxCellRenderer;
