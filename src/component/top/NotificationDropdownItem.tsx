"use client";
import React from "react";
import {
  <PERSON>a<PERSON><PERSON>,
  FaInfoCircle,
  FaExclamationTriangle,
  FaTimesCircle,
  FaCheckCircle,
} from "react-icons/fa";
import { NotificationType } from "@prisma/client";
import { SystemNotification } from "./NotificationDropdown";

interface NotificationDropdownItemProps {
  notification: SystemNotification;
  onMarkAsRead: (id: string) => void;
}

const getTypeIcon = (type: NotificationType) => {
  switch (type) {
    case NotificationType.INFO:
      return <FaInfoCircle className="text-blue-500" />;
    case NotificationType.WARNING:
      return <FaExclamationTriangle className="text-yellow-500" />;
    case NotificationType.ERROR:
      return <FaTimesCircle className="text-red-500" />;
    case NotificationType.SUCCESS:
      return <FaCheckCircle className="text-green-500" />;
    default:
      return <FaInfoCircle className="text-blue-500" />;
  }
};

const getTypeColor = (type: NotificationType) => {
  switch (type) {
    case NotificationType.INFO:
      return "border-l-blue-500";
    case NotificationType.WARNING:
      return "border-l-yellow-500";
    case NotificationType.ERROR:
      return "border-l-red-500";
    case NotificationType.SUCCESS:
      return "border-l-green-500";
    default:
      return "border-l-blue-500";
  }
};

const formatTime = (date: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return "Gerade eben";
  if (diffInMinutes < 60) return `vor ${diffInMinutes} Min`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `vor ${diffInHours} Std`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `vor ${diffInDays} Tag${diffInDays > 1 ? "en" : ""}`;

  return new Date(date).toLocaleDateString("de-DE");
};

const NotificationDropdownItem: React.FC<NotificationDropdownItemProps> = ({
  notification,
  onMarkAsRead,
}) => {
  const handleClick = () => {
    if (!notification.gelesen) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <div
      className={`relative border-l-4 border-t border-gray-200 ${getTypeColor(notification.type)} ${
        !notification.gelesen ? "bg-blue-50 dark:bg-blue-900/20" : "bg-white dark:bg-gray-800"
      } cursor-pointer transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-700`}
      onClick={handleClick}
    >
      <div className="flex items-start  p-2">
        {/* Type Icon */}
        <div className="mr-3 mt-1 flex-shrink-0">{getTypeIcon(notification.type)}</div>

        {/* Content */}
        <div className="min-w-0 flex-1">
          <div className="flex items-start justify-between">
            <div
              className={`text-sm  ${
                !notification.gelesen ? "font-semibold" : "font-normal"
              } text-gray-900 dark:text-white`}
            >
              {notification.nachricht}
            </div>
            {!notification.gelesen && (
              <div className="ml-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
            )}
          </div>

          <div className="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400">
            <FaClock className="mr-1" />
            {formatTime(notification.createdAt)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationDropdownItem;
