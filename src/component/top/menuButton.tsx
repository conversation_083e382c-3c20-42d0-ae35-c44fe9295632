"use client";
import React from "react";
import { setMiniSidenav, useSoftUIController } from "../../context";
import Image from "next/image";

const MenuButton = () => {
  const [controller, dispatch] = useSoftUIController();

  return (
    <a
      className="ease-nav-brand block p-0 text-sm text-slate-500 transition-all dark:text-white"
      aria-expanded="true"
      onClick={() => setMiniSidenav(dispatch, !controller.miniSidenav)}
    >
      {controller.miniSidenav ? (
        <Image
          id={"id-menubutton"}
          src={"/images/burger_menu_active.svg"}
          width={25}
          height={28.58}
          alt={`Menu auf/zuklappen`}
          className="scale-x-[-1] hover:cursor-pointer"
        />
      ) : (
        <Image
          id={"id-menubutton"}
          src={"/images/burger_menu.svg"}
          width={25}
          height={28.58}
          alt={`Menu auf/zuklappen`}
          className={"hover:cursor-pointer"}
        />
      )}
    </a>
  );
};

export default MenuButton;
