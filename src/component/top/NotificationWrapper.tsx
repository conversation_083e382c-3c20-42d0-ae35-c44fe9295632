"use client";
import React from "react";
import NotificationDropdown from "./NotificationDropdown";
import { useNotifications } from "~/hooks/useNotifications";

const NotificationWrapper: React.FC = () => {
  const {
    notifications,
    unreadCount,
    loading,
    error,
    refreshNotifications,
    markAsRead,
    markAllAsRead,
  } = useNotifications(10);

  // Don't render if there's an error or still loading initially
  if (error && notifications.length === 0) {
    console.error("Notification error:", error);
    return null;
  }

  return (
    <NotificationDropdown
      notifications={notifications}
      unreadCount={unreadCount}
      onMarkAsRead={markAsRead}
      onMarkAllAsRead={markAllAsRead}
      onRefresh={refreshNotifications}
    />
  );
};

export default NotificationWrapper;
