"use client";
import { usePathname } from "next/navigation";
import Link from "next/link";
import type { FC } from "react";
import React from "react";
import { AiTwotoneHome } from "react-icons/ai";

const Breadcrumb: FC = () => {
  const pathname = usePathname();
  if (!pathname) {
    return <>No pathname found</>;
  }
  const path = pathname === "/" ? ["realtime"] : pathname.split("/").filter((x) => x);

  return (
    <>
      <ol className="mr-6 flex flex-wrap rounded-lg bg-transparent pt-1">
        <li className="breadcrumb-item text-sm leading-normal">
          <a className="text-slate-700 opacity-30 dark:text-white" href="/">
            <AiTwotoneHome />
          </a>
        </li>
        {path.map((segment, index) => (
          <li
            key={index}
            className="pl-2 text-sm leading-normal before:float-left before:pr-2 before:text-gray-600 before:content-['/']"
          >
            <Link
              href={`/${path.slice(0, index + 1).join("/")}`}
              className="text-slate-700 opacity-50 dark:text-white"
            >
              {segment}
            </Link>
          </li>
        ))}
      </ol>
    </>
  );
};

export default Breadcrumb;
