import React from "react";
import Login from "../../app/(app)/Login";
import MenuButton from "./menuButton";
import NotificationWrapper from "./NotificationWrapper";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import type { Session } from "next-auth";
import MobileCaption from "~/component/top/MobileCaption";
import { menuDef } from "~/utils/menu/menuDef";

export const revalidate = 0;

const MobileBar = async () => {
  const session = (await getServerSession(authOptions)) as Session;

  if (!session?.user?.email || !session?.user?.role) {
    return <>No User</>;
  }

  if (!session?.user?.ou?.id) {
    return (
      <>
        User without Ou <Login />
      </>
    );
  }
  return (
    <>
      <nav
        className={
          "relative sticky top-[1%] z-110 mx-6 mt-6 flex justify-between bg-eul-main sm:hidden"
        }
      >
        <div className="flex flex-grow items-center justify-center">
          <div className="flex items-center">
            <h4 className="mb-0 ml-1 font-bold capitalize text-black text-primary">
              <MobileCaption
                visibleMenuItems={menuDef
                  .filter(
                    (entry) => session?.user?.role && entry?.role.includes(session?.user?.role),
                  )
                  .map((entry) => {
                    return { ...entry, role: [] };
                  })}
              />
            </h4>
          </div>
        </div>

        <div className="flex items-center pl-4">
          <li className="flex items-center pr-4">
            <NotificationWrapper />
          </li>

          <li className="flex items-center xl:hidden">
            <MenuButton />
          </li>
        </div>
      </nav>
    </>
  );
};

export default MobileBar;
