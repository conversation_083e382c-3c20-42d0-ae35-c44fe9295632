"use client";
import { usePathname } from "next/navigation";
import React, { useCallback } from "react";

import { findMenuItemByPathname } from "~/utils/menu/menu";
import { MenuItem } from "~/utils/menu/menuDef";

const MobileCaption = ({ visibleMenuItems }: { visibleMenuItems: MenuItem[] }) => {
  const pathname = usePathname();

  const getNameAndIcon = useCallback(() => {
    if (pathname) {
      const entry = findMenuItemByPathname(pathname, visibleMenuItems);
      let icon = <></>;
      if (entry && "icon" in entry) {
        icon = entry.icon;
        return [entry.name, icon];
      }
      return [entry?.name, <></>];
    }
    return [<></>, <></>];
  }, [pathname]);

  if (!pathname) {
    return <>No pathname found</>;
  }
  const [name, icon] = getNameAndIcon();

  return (
    <div className={"flex items-center gap-1"}>
      {icon} {name}
    </div>
  );
};

export default MobileCaption;
