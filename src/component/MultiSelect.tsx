import Multiselect, { GroupBase, OptionsOrGroups, MultiValue } from "react-select";

const MultiSelect = ({
  options,
  value,
  onChange,
  isDisabled = false,
}: {
  value: MultiValue<any>;
  options: OptionsOrGroups<any, GroupBase<any>> | undefined;
  onChange: any;
  isDisabled?: boolean;
}) => {
  return (
    <Multiselect
      isDisabled={isDisabled}
      onChange={onChange}
      isMulti
      name="colors"
      value={value}
      options={options}
      classNamePrefix="select"
    />
  );
};
export default MultiSelect;
