/*!

=========================================================
* Soft UI Dashboard Pro Tailwind - v1.0.1
=========================================================

* Product Page: https://www.creative-tim.com/product/soft-ui-dashboard-pro-tailwind
* Copyright 2022 Creative Tim (https://www.creative-tim.com)


* Coded by www.creative-tim.com

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

*/

/*! tailwindcss v3.1.7 | MIT License | https://tailwindcss.com

*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e9ecef;
  /* 2 */
}

::before,
::after {
  --tw-content: "";
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
*/

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
  tab-size: 4;
  /* 3 */
  font-family: "Source Sans Pro";
  /* 4 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #ced4da;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #ced4da;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

*,
::before,
::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

::-webkit-backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 576px) {
  .container {
    max-width: 576px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 992px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
}

a {
  letter-spacing: -0.025rem;
  text-decoration: none;
}

hr {
  margin: 1rem 0;
  color: inherit;
  border: 0;
  opacity: 0.25;
}

img {
  max-width: none;
  display: inline;
}

label {
  display: inline-block;
}

p {
  font-size: 1rem;
  line-height: 1.625;
  font-weight: 400;
  margin-bottom: 1rem;
}

small {
  font-size: 0.875em;
}

svg {
  display: inline;
}

table {
  border-collapse: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6,
a {
  color: #000000;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0.5rem;
}

h1,
h2,
h3,
h4 {
  letter-spacing: -0.05rem;
}

h1,
h2,
h3 {
  font-weight: 700;
}

h4,
h5,
h6 {
  font-weight: 600;
}

h1 {
  font-size: 3rem;
  line-height: 1.25;
}

h2 {
  font-size: 2.25rem;
  line-height: 1.3;
}

h3 {
  font-size: 1.875rem;
  line-height: 1.375;
}

h4 {
  font-size: 1.5rem;
  line-height: 1.375;
}

h5 {
  font-size: 1.25rem;
  line-height: 1.375;
}

h6 {
  font-size: 1rem;
  line-height: 1.625;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

.inset-0 {
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.left-0 {
  left: 0px;
}

.top-0 {
  top: 0px;
}

.right-0 {
  right: 0px;
}

.top-3\.5 {
  top: 0.875rem;
}

.top-3 {
  top: 0.75rem;
}

.top-1\/10 {
  top: 10%;
}

.bottom-7 {
  bottom: 1.75rem;
}

.right-7 {
  right: 1.75rem;
}

.-right-90 {
  right: -22.5rem;
}

.left-auto {
  left: auto;
}

.bottom-0 {
  bottom: 0px;
}

.top-auto {
  top: auto;
}

.-left-90 {
  left: -22.5rem;
}

.top-\[1\%\] {
  top: 1%;
}

.top-1 {
  top: 0.25rem;
}

.top-6 {
  top: 1.5rem;
}

.right-16 {
  right: 4rem;
}

.bottom-auto {
  bottom: auto;
}

.bottom-1\/100 {
  bottom: 1%;
}

.right-1\/100 {
  right: 1%;
}

.-right-50 {
  right: -12.5rem;
}

.-top-1 {
  top: -0.25rem;
}

.top-31\/100 {
  top: 31%;
}

.right-4 {
  right: 1rem;
}

.-top-1\.5 {
  top: -0.375rem;
}

.left-7 {
  left: 1.75rem;
}

.right-auto {
  right: auto;
}

.left-4 {
  left: 1rem;
}

.-right-40 {
  right: -10rem;
}

.-right-2 {
  right: -0.5rem;
}

.top-1\.5 {
  top: 0.375rem;
}

.right-2 {
  right: 0.5rem;
}

.top-1\/100 {
  top: 1%;
}

.right-5\.5 {
  right: 1.375rem;
}

.-top-12 {
  top: -3rem;
}

.right-5 {
  right: 1.25rem;
}

.isolate {
  isolation: isolate;
}

.z-990 {
  z-index: 990;
}

.z-20 {
  z-index: 20;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.z-sticky {
  z-index: 1020;
}

.z-110 {
  z-index: 110;
}

.z-100 {
  z-index: 100;
}

.z-1 {
  z-index: 1;
}

.z-40 {
  z-index: 40;
}

.z-30 {
  z-index: 30;
}

.z-2 {
  z-index: 2;
}

.z-0 {
  z-index: 0;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.float-none {
  float: none;
}

.clear-both {
  clear: both;
}

.m-0 {
  margin: 0px;
}

.m-4 {
  margin: 1rem;
}

.-m-2 {
  margin: -0.5rem;
}

.m-1 {
  margin: 0.25rem;
}

.m-auto {
  margin: auto;
}

.m-2 {
  margin: 0.5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.-mx-3 {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-56 {
  margin-top: 14rem;
  margin-bottom: 14rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.my-7 {
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-16 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}

.my-48 {
  margin-top: 12rem;
  margin-bottom: 12rem;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.mt-0 {
  margin-top: 0px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-5\.4 {
  margin-left: 1.35rem;
}

.mr-4 {
  margin-right: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.mt-4 {
  margin-top: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-7 {
  margin-bottom: 1.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mr-12 {
  margin-right: 3rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-0\.75 {
  margin-bottom: 0.2rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.-ml-px {
  margin-left: -1px;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.-ml-3 {
  margin-left: -0.75rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.-mt-0\.4 {
  margin-top: -0.1rem;
}

.-mt-0 {
  margin-top: -0px;
}

.mt-24 {
  margin-top: 6rem;
}

.-mr-48 {
  margin-right: -12rem;
}

.mr-1\.25 {
  margin-right: 0.3125rem;
}

.mb-0\.5 {
  margin-bottom: 0.125rem;
}

.ml-auto {
  margin-left: auto;
}

.ml-0 {
  margin-left: 0px;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-0\.75 {
  margin-left: 0.2rem;
}

.mr-0 {
  margin-right: 0px;
}

.mr-6 {
  margin-right: 1.5rem;
}

.mr-5\.4 {
  margin-right: 1.35rem;
}

.mr-5 {
  margin-right: 1.25rem;
}

.mr-0\.6 {
  margin-right: 0.15rem;
}

.-ml-2 {
  margin-left: -0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-0\.75 {
  margin-top: 0.2rem;
}

.mt-1\.75 {
  margin-top: 0.4375rem;
}

.-mr-4 {
  margin-right: -1rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mt-12 {
  margin-top: 3rem;
}

.-ml-4 {
  margin-left: -1rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-32 {
  margin-bottom: 8rem;
}

.-mb-2 {
  margin-bottom: -0.5rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.mr-auto {
  margin-right: auto;
}

.-mt-24 {
  margin-top: -6rem;
}

.-ml-1 {
  margin-left: -0.25rem;
}

.-mt-32 {
  margin-top: -8rem;
}

.-ml-12 {
  margin-left: -3rem;
}

.mt-auto {
  margin-top: auto;
}

.-mt-16 {
  margin-top: -4rem;
}

.mt-32 {
  margin-top: 8rem;
}

.ml-12 {
  margin-left: 3rem;
}

.-mr-px {
  margin-right: -1px;
}

.ml-1\.25 {
  margin-left: 0.3125rem;
}

.-mt-48 {
  margin-top: -12rem;
}

.-mr-32 {
  margin-right: -8rem;
}

.-ml-16 {
  margin-left: -4rem;
}

.mt-40 {
  margin-top: 10rem;
}

.-ml-7 {
  margin-left: -1.75rem;
}

.-ml-36 {
  margin-left: -9rem;
}

.-mt-6 {
  margin-top: -1.5rem;
}

.-mt-2 {
  margin-top: -0.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.box-border {
  box-sizing: border-box;
}

.box-content {
  box-sizing: content-box;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.list-item {
  display: list-item;
}

.hidden {
  display: none;
}

.h-20 {
  height: 5rem;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-auto {
  height: auto;
}

.h-8 {
  height: 2rem;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-0 {
  height: 0px;
}

.h-9 {
  height: 2.25rem;
}

.h-12 {
  height: 3rem;
}

.h-5 {
  height: 1.25rem;
}

.h-0\.75 {
  height: 0.2rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-1 {
  height: 0.25rem;
}

.h-3\/4 {
  height: 75%;
}

.h-5\.6 {
  height: 1.4rem;
}

.h-\[80vh\] {
  height: 80vh;
}

.h-6 {
  height: 1.5rem;
}

.h-screen {
  height: 100vh;
}

.h-2 {
  height: 0.5rem;
}

.h-4 {
  height: 1rem;
}

.h-28 {
  height: 7rem;
}

.h-14 {
  height: 3.5rem;
}

.h-13 {
  height: 3.25rem;
}

.h-100 {
  height: 25rem;
}

.h-19 {
  height: 4.75rem;
}

.h-16 {
  height: 4rem;
}

.h-6\.5 {
  height: 1.625rem;
}

.h-25 {
  height: 6.25rem;
}

.\!h-1\/2 {
  height: 50% !important;
}

.max-h-8 {
  max-height: 2rem;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-screen {
  max-height: 100vh;
}

.max-h-70-screen {
  max-height: 70vh;
}

.max-h-25 {
  max-height: 6.25rem;
}

.min-h-6 {
  min-height: 1.5rem;
}

.min-h-unset {
  min-height: unset;
}

.min-h-screen {
  min-height: 100vh;
}

.min-h-75 {
  min-height: 18.75rem;
}

.min-h-75-screen {
  min-height: 75vh;
}

.min-h-50-screen {
  min-height: 50vh;
}

.min-h-85-screen {
  min-height: 85vh;
}

.min-h-25 {
  min-height: 6.25rem;
}

.w-full {
  width: 100%;
}

.w-8 {
  width: 2rem;
}

.w-0 {
  width: 0px;
}

.w-4\.5 {
  width: 1.125rem;
}

.w-4 {
  width: 1rem;
}

.w-1\/100 {
  width: 1%;
}

.w-9 {
  width: 2.25rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-4\/12 {
  width: 33.333333%;
}

.w-12 {
  width: 3rem;
}

.w-3\/10 {
  width: 30%;
}

.w-1\/4 {
  width: 25%;
}

.w-5 {
  width: 1.25rem;
}

.w-3\/4 {
  width: 75%;
}

.w-3\/5 {
  width: 60%;
}

.w-9\/10 {
  width: 90%;
}

.w-1\/2 {
  width: 50%;
}

.w-90 {
  width: 22.5rem;
}

.w-5\.6 {
  width: 1.4rem;
}

.w-10 {
  width: 2.5rem;
}

.w-6 {
  width: 1.5rem;
}

.w-screen {
  width: 100vw;
}

.w-2 {
  width: 0.5rem;
}

.w-4\/5 {
  width: 80%;
}

.w-2\/5 {
  width: 40%;
}

.w-15\/100 {
  width: 15%;
}

.w-px {
  width: 1px;
}

.w-7\/12 {
  width: 58.333333%;
}

.w-5\/12 {
  width: 41.666667%;
}

.w-auto {
  width: auto;
}

.w-10\/12 {
  width: 83.333333%;
}

.w-28 {
  width: 7rem;
}

.w-8\/12 {
  width: 66.666667%;
}

.w-14 {
  width: 3.5rem;
}

.w-13 {
  width: 3.25rem;
}

.w-6\/12 {
  width: 50%;
}

.w-70\/100 {
  width: 70%;
}

.w-19 {
  width: 4.75rem;
}

.w-11\/12 {
  width: 91.666667%;
}

.w-1\/12 {
  width: 8.333333%;
}

.w-85 {
  width: 22rem;
}

.w-5\.5 {
  width: 1.375rem;
}

.w-16 {
  width: 4rem;
}

.w-1\/5 {
  width: 20%;
}

.w-6\.5 {
  width: 1.625rem;
}

.w-1 {
  width: 0.25rem;
}

.w-3\/12 {
  width: 25%;
}

.w-25 {
  width: 6.25rem;
}

.w-1\/10 {
  width: 10%;
}

.w-30 {
  width: 7.5rem;
}

.w-5\/100 {
  width: 5%;
}

.w-9\/12 {
  width: 75%;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-80\/100 {
  width: 80%;
}

.w-17\/100 {
  width: 17%;
}

.w-3\/100 {
  width: 3%;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-44 {
  min-width: 11rem;
}

.min-w-7 {
  min-width: 1.75rem;
}

.max-w-64 {
  max-width: 16rem;
}

.max-w-full {
  max-width: 100%;
}

.max-w-none {
  max-width: none;
}

.max-w-screen-2xl {
  max-width: 1400px;
}

.max-w-24 {
  max-width: 6rem;
}

.max-w-0 {
  max-width: 0rem;
}

.max-w-46 {
  max-width: 11.5rem;
}

.max-w-50 {
  max-width: 12.5rem;
}

.max-w-125 {
  max-width: 31.25rem;
}

.max-w-120 {
  max-width: 30rem;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

.flex-0 {
  flex: 0 0 auto;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-1-0 {
  flex: 1 0 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.grow {
  flex-grow: 1;
}

.basis-full {
  flex-basis: 100%;
}

.origin-top {
  transform-origin: top;
}

.origin-10-10 {
  transform-origin: 10% 10%;
}

.origin-10-90 {
  transform-origin: 10% 90%;
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.translate-x-\[5px\] {
  --tw-translate-x: 5px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.-translate-x-\[5px\] {
  --tw-translate-x: -5px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.-translate-y-13 {
  --tw-translate-y: -3.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.-skew-x-10 {
  --tw-skew-x: -10deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.skew-x-10 {
  --tw-skew-x: 10deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.transform-none {
  transform: none;
}

@-webkit-keyframes fade-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }

  to {
    opacity: 1;
  }
}

@keyframes fade-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }

  to {
    opacity: 1;
  }
}

.animate-fade-up {
  -webkit-animation: fade-up 1.5s both;
  animation: fade-up 1.5s both;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.select-auto {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  user-select: auto;
}

.resize {
  resize: both;
}

.list-none {
  list-style-type: none;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.self-center {
  align-self: center;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overscroll-x-auto {
  overscroll-behavior-x: auto;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.overflow-ellipsis {
  text-overflow: ellipsis;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-circle {
  border-radius: 50%;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-10 {
  border-radius: 2.5rem;
}

.rounded-1 {
  border-radius: 0.25rem;
}

.\!rounded-circle {
  border-radius: 50% !important;
}

.rounded-1\.8 {
  border-radius: 0.45rem;
}

.rounded-3\.5xl {
  border-radius: 1.875rem;
}

.rounded-3 {
  border-radius: 0.75rem;
}

.rounded-1\.5 {
  border-radius: 0.375rem;
}

.rounded-0 {
  border-radius: 0px;
}

.rounded-7 {
  border-radius: 1.75rem;
}

.rounded-px {
  border-radius: 1px;
}

.rounded-blur {
  border-radius: 40px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-1\.4 {
  border-radius: 0.35rem;
}

.rounded-t-4 {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}

.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}

.rounded-t-inherit {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.rounded-b-inherit {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}

.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-t-1 {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.rounded-b-1 {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-b-xl {
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}

.rounded-tr-none {
  border-top-right-radius: 0px;
}

.rounded-br-none {
  border-bottom-right-radius: 0px;
}

.rounded-bl-xl {
  border-bottom-left-radius: 0.75rem;
}

.border-0 {
  border-width: 0px;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-r-0 {
  border-right-width: 0px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-blue-900 {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 125 / var(--tw-border-opacity));
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-transparent {
  border-color: transparent;
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(210 214 218 / var(--tw-border-opacity));
}

.border-fuchsia-500 {
  --tw-border-opacity: 1;
  border-color: rgb(203 12 159 / var(--tw-border-opacity));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 236 239 / var(--tw-border-opacity));
}

.border-slate-700 {
  --tw-border-opacity: 1;
  border-color: rgb(52 71 103 / var(--tw-border-opacity));
}

.border-slate-400 {
  --tw-border-opacity: 1;
  border-color: rgb(131 146 171 / var(--tw-border-opacity));
}

.border-black\/12\.5 {
  border-color: rgb(0 0 0 / 0.125);
}

.border-inherit {
  border-color: inherit;
}

.border-lime-500 {
  --tw-border-opacity: 1;
  border-color: rgb(130 214 22 / var(--tw-border-opacity));
}

.border-black\/20 {
  border-color: rgb(0 0 0 / 0.2);
}

.border-slate-100 {
  --tw-border-opacity: 1;
  border-color: rgb(222 226 230 / var(--tw-border-opacity));
}

.border-lime-300 {
  --tw-border-opacity: 1;
  border-color: rgb(190 242 100 / var(--tw-border-opacity));
}

.border-white\/75 {
  border-color: rgb(255 255 255 / 0.75);
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(234 6 6 / var(--tw-border-opacity));
}

.border-fuchsia-300 {
  --tw-border-opacity: 1;
  border-color: rgb(226 147 211 / var(--tw-border-opacity));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity));
}

.border-orange-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 237 213 / var(--tw-border-opacity));
}

.border-cyan-200 {
  --tw-border-opacity: 1;
  border-color: rgb(165 243 252 / var(--tw-border-opacity));
}

.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 232 237 / var(--tw-border-opacity));
}

.border-slate-150 {
  --tw-border-opacity: 1;
  border-color: rgb(203 211 218 / var(--tw-border-opacity));
}

.border-current {
  border-color: currentColor;
}

.border-b-black\/12\.5 {
  border-bottom-color: rgb(0 0 0 / 0.125);
}

.border-t-black\/12\.5 {
  border-top-color: rgb(0 0 0 / 0.125);
}

.border-b-transparent {
  border-bottom-color: transparent;
}

.border-b-gray-200 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(233 236 239 / var(--tw-border-opacity));
}

.border-b-slate-100 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(222 226 230 / var(--tw-border-opacity));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-slate-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(103 116 142 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 236 239 / var(--tw-bg-opacity));
}

.bg-slate-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(52 71 103 / var(--tw-bg-opacity));
}

.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-fuchsia-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(203 12 159 / var(--tw-bg-opacity));
}

.bg-slate-800\/10 {
  background-color: rgb(58 65 111 / 0.1);
}

.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(130 214 22 / var(--tw-bg-opacity));
}

.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(23 193 232 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-inherit {
  background-color: inherit;
}

.bg-red-600\/3 {
  background-color: rgb(234 6 6 / 0.03);
}

.bg-purple-700\/3 {
  background-color: rgb(121 40 202 / 0.03);
}

.bg-green-600\/3 {
  background-color: rgb(23 173 55 / 0.03);
}

.bg-red-500\/3 {
  background-color: rgb(245 57 57 / 0.03);
}

.bg-blue-600\/3 {
  background-color: rgb(33 82 255 / 0.03);
}

.bg-slate-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(131 146 171 / var(--tw-bg-opacity));
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(228 232 237 / var(--tw-bg-opacity));
}

.bg-blue-600\/5 {
  background-color: rgb(33 82 255 / 0.05);
}

.bg-pink-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 131 221 / var(--tw-bg-opacity));
}

.bg-lime-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(205 245 155 / var(--tw-bg-opacity));
}

.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 151 151 / var(--tw-bg-opacity));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 6 6 / var(--tw-bg-opacity));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-tl {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

.bg-none {
  background-image: none;
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/white-curved\.jpg\'\)\] {
  background-image: url("./img/curved-images/white-curved.jpg");
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/ivancik\.jpg\'\)\] {
  background-image: url("./img/ivancik.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/bg-smart-home-1\.jpg\'\)\] {
  background-image: url("./img/bg-smart-home-1.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/bg-smart-home-2\.jpg\'\)\] {
  background-image: url("./img/bg-smart-home-2.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/home-decor-3\.jpg\'\)\] {
  background-image: url("./img/home-decor-3.jpg");
}

.bg-\[url\(\'https\:\/\/raw\.githubusercontent\.com\/creativetimofficial\/public-assets\/master\/soft-ui-design-system\/assets\/img\/window-desk\.jpg\'\)\] {
  background-image: url("https://raw.githubusercontent.com/creativetimofficial/public-assets/master/soft-ui-design-system/assets/img/window-desk.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/office-dark\.jpg\'\)\] {
  background-image: url("./img/office-dark.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved0\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved0.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved8\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved8.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved14\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved14.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved1\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved1.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/\.\.\/assets\/img\/curved-images\/curved8\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved8.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved7\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved7.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved6\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved6.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved9\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved9.jpg");
}

.bg-\[url\(\'\.\.\/\.\.\/assets\/img\/curved-images\/curved11\.jpg\'\)\] {
  background-image: url("./img/curved-images/curved11.jpg");
}

.bg-\[url\(\'\.\.\/img\/vr-bg\.jpg\'\)\] {
  background-image: url("./img/vr-bg.jpg");
}

.from-transparent {
  --tw-gradient-from: transparent;
  --tw-gradient-to: rgb(0 0 0 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-700 {
  --tw-gradient-from: #7928ca;
  --tw-gradient-to: rgb(121 40 202 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-600 {
  --tw-gradient-from: #627594;
  --tw-gradient-to: rgb(98 117 148 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-900 {
  --tw-gradient-from: #141727;
  --tw-gradient-to: rgb(20 23 39 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-600 {
  --tw-gradient-from: #2152ff;
  --tw-gradient-to: rgb(33 82 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-500 {
  --tw-gradient-from: #f53939;
  --tw-gradient-to: rgb(245 57 57 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-600 {
  --tw-gradient-from: #ea0606;
  --tw-gradient-to: rgb(234 6 6 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-600 {
  --tw-gradient-from: #17ad37;
  --tw-gradient-to: rgb(23 173 55 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-400 {
  --tw-gradient-from: #ced4da;
  --tw-gradient-to: rgb(206 212 218 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-black\/40 {
  --tw-gradient-to: rgb(0 0 0 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.4), var(--tw-gradient-to);
}

.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), #fff, var(--tw-gradient-to);
}

.to-transparent {
  --tw-gradient-to: transparent;
}

.to-pink-500 {
  --tw-gradient-to: #ff0080;
}

.to-slate-300 {
  --tw-gradient-to: #a8b8d8;
}

.to-slate-800 {
  --tw-gradient-to: #3a416f;
}

.to-cyan-400 {
  --tw-gradient-to: #21d4fd;
}

.to-yellow-400 {
  --tw-gradient-to: #fbcf33;
}

.to-rose-400 {
  --tw-gradient-to: #ff667c;
}

.to-lime-400 {
  --tw-gradient-to: #98ec2d;
}

.to-gray-100 {
  --tw-gradient-to: #ebeff4;
}

.bg-cover {
  background-size: cover;
}

.bg-150 {
  background-size: 150%;
}

.bg-contain {
  background-size: contain;
}

.bg-clip-border {
  background-clip: border-box;
}

.bg-clip-padding {
  background-clip: padding-box;
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.bg-center {
  background-position: center;
}

.bg-x-25 {
  background-position: 25% 0;
}

.bg-left {
  background-position: left;
}

.bg-right {
  background-position: right;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-current {
  fill: currentColor;
}

.fill-slate-800 {
  fill: #3a416f;
}

.fill-white {
  fill: #fff;
}

.fill-slate-300 {
  fill: #a8b8d8;
}

.fill-transparent {
  fill: transparent;
}

.fill-gray-900 {
  fill: #141727;
}

.stroke-none {
  stroke: none;
}

.p-0 {
  padding: 0px;
}

.p-4 {
  padding: 1rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-1\.2 {
  padding: 0.3rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2\.8 {
  padding: 0.7rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-12 {
  padding: 3rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-2\.7 {
  padding-top: 0.675rem;
  padding-bottom: 0.675rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-1\.6 {
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-1\.2 {
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-2\.2 {
  padding-top: 0.55rem;
  padding-bottom: 0.55rem;
}

.px-2\.2 {
  padding-left: 0.55rem;
  padding-right: 0.55rem;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-1\.8 {
  padding-top: 0.45rem;
  padding-bottom: 0.45rem;
}

.py-2\.6 {
  padding-top: 0.65rem;
  padding-bottom: 0.65rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-3\.6 {
  padding-left: 0.9rem;
  padding-right: 0.9rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.px-2\.8 {
  padding-left: 0.7rem;
  padding-right: 0.7rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-24 {
  padding-left: 6rem;
  padding-right: 6rem;
}

.py-3\.4 {
  padding-top: 0.85rem;
  padding-bottom: 0.85rem;
}

.px-5\.5 {
  padding-left: 1.375rem;
  padding-right: 1.375rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.pl-0 {
  padding-left: 0px;
}

.pl-4 {
  padding-left: 1rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-9 {
  padding-left: 2.25rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pb-0 {
  padding-bottom: 0px;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pt-0 {
  padding-top: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pr-0 {
  padding-right: 0px;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pb-0\.5 {
  padding-bottom: 0.125rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pt-24 {
  padding-top: 6rem;
}

.pb-48 {
  padding-bottom: 12rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pr-9 {
  padding-right: 2.25rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pt-1\.4 {
  padding-top: 0.35rem;
}

.pb-56 {
  padding-bottom: 14rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pl-7 {
  padding-left: 1.75rem;
}

.pt-48 {
  padding-top: 12rem;
}

.pl-8 {
  padding-left: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.-indent-330 {
  text-indent: -82.5rem;
}

.align-baseline {
  vertical-align: baseline;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.align-\[-0\.0667em\] {
  vertical-align: -0.0667em;
}

.font-sans {
  font-family: "Source Sans Pro";
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.5rem;
}

.text-3\.4 {
  font-size: 0.85rem;
}

.text-3 {
  font-size: 0.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xxs {
  font-size: 0.65rem;
  line-height: 1rem;
}

.text-3xs {
  font-size: 0.5rem;
  line-height: 1rem;
}

.text-inherit {
  font-size: inherit;
}

.text-5\.3 {
  font-size: 1.3rem;
}

.text-5 {
  font-size: 1.25rem;
}

.text-4\.8 {
  font-size: 1.2rem;
}

.text-4 {
  font-size: 1rem;
}

.text-3\.5 {
  font-size: 0.875rem;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.ordinal {
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure)
    var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.leading-default {
  line-height: 1.6;
}

.leading-tight {
  line-height: 1.25;
}

.leading-none {
  line-height: 1;
}

.leading-pro {
  line-height: 1.4;
}

.leading-normal {
  line-height: 1.5;
}

.leading-5\.6 {
  line-height: 1.4rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-3 {
  line-height: 0.75rem;
}

.leading-tighter {
  line-height: 1.2;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.tracking-tight-soft {
  letter-spacing: -0.025rem;
}

.tracking-none {
  letter-spacing: 0;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(103 116 142 / var(--tw-text-opacity));
}

.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(131 146 171 / var(--tw-text-opacity));
}

.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-slate-800 {
  --tw-text-opacity: 1;
  color: rgb(58 65 111 / var(--tw-text-opacity));
}

.text-slate-800\/50 {
  color: rgb(58 65 111 / 0.5);
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(234 6 6 / var(--tw-text-opacity));
}

.text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(130 214 22 / var(--tw-text-opacity));
}

.text-cyan-500 {
  --tw-text-opacity: 1;
  color: rgb(23 193 232 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-transparent {
  color: transparent;
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(73 80 87 / var(--tw-text-opacity));
}

.text-fuchsia-500 {
  --tw-text-opacity: 1;
  color: rgb(203 12 159 / var(--tw-text-opacity));
}

.text-neutral-900 {
  --tw-text-opacity: 1;
  color: rgb(17 17 17 / var(--tw-text-opacity));
}

.text-slate-100 {
  --tw-text-opacity: 1;
  color: rgb(222 226 230 / var(--tw-text-opacity));
}

.text-inherit {
  color: inherit;
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(52 78 134 / var(--tw-text-opacity));
}

.text-sky-600 {
  --tw-text-opacity: 1;
  color: rgb(62 161 236 / var(--tw-text-opacity));
}

.text-orange-650 {
  --tw-text-opacity: 1;
  color: rgb(224 61 0 / var(--tw-text-opacity));
}

.text-red-650 {
  --tw-text-opacity: 1;
  color: rgb(212 31 26 / var(--tw-text-opacity));
}

.text-teal-550 {
  --tw-text-opacity: 1;
  color: rgb(50 152 116 / var(--tw-text-opacity));
}

.text-slate-650 {
  --tw-text-opacity: 1;
  color: rgb(89 116 162 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(245 57 57 / var(--tw-text-opacity));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(251 207 51 / var(--tw-text-opacity));
}

.text-lime-600 {
  --tw-text-opacity: 1;
  color: rgb(103 177 8 / var(--tw-text-opacity));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(233 236 239 / var(--tw-text-opacity));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(189 0 0 / var(--tw-text-opacity));
}

.text-sky-900 {
  --tw-text-opacity: 1;
  color: rgb(14 69 109 / var(--tw-text-opacity));
}

.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}

.underline {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-100 {
  opacity: 1;
}

.opacity-0 {
  opacity: 0;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-80 {
  opacity: 0.8;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-90 {
  opacity: 0.9;
}

.opacity-25 {
  opacity: 0.25;
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-sm {
  --tw-shadow: 0 0.25rem 0.375rem -0.0625rem hsla(0, 0%, 8%, 0.12),
    0 0.125rem 0.25rem -0.0625rem hsla(0, 0%, 8%, 0.07);
  --tw-shadow-colored: 0 0.25rem 0.375rem -0.0625rem var(--tw-shadow-color),
    0 0.125rem 0.25rem -0.0625rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-2xl {
  --tw-shadow: 0 0.3125rem 0.625rem 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 0.3125rem 0.625rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-md {
  --tw-shadow: 0 4px 7px -1px rgba(0, 0, 0, 0.11), 0 2px 4px -1px rgba(0, 0, 0, 0.07);
  --tw-shadow-colored: 0 4px 7px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-xl {
  --tw-shadow: 0 20px 27px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 20px 27px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-lg {
  --tw-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.16);
  --tw-shadow-colored: 0 2px 12px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-3xl {
  --tw-shadow: 0 8px 26px -4px hsla(0, 0%, 8%, 0.15), 0 8px 9px -5px hsla(0, 0%, 8%, 0.06);
  --tw-shadow-colored: 0 8px 26px -4px var(--tw-shadow-color), 0 8px 9px -5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-blur {
  --tw-shadow: inset 0 0 1px 1px hsla(0, 0%, 100%, 0.9), 0 20px 27px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: inset 0 0 1px 1px var(--tw-shadow-color),
    0 20px 27px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-soft-xxs {
  --tw-shadow: 0 1px 5px 1px #ddd;
  --tw-shadow-colored: 0 1px 5px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 23px 45px -11px hsla(0, 0%, 8%, 0.25);
  --tw-shadow-colored: 0 23px 45px -11px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.\!shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow) !important;
}

.shadow-soft-dark-xl {
  --tw-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 2px 2px 0 var(--tw-shadow-color), 0 3px 1px -2px var(--tw-shadow-color),
    0 1px 5px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-transparent {
  --tw-shadow-color: transparent;
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.outline-0 {
  outline-width: 0px;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
    var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-2xl {
  --tw-backdrop-blur: blur(30px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-saturate-200 {
  --tw-backdrop-saturate: saturate(2);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property:
    color,
    background-color,
    border-color,
    fill,
    stroke,
    -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-width {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition {
  transition-property:
    color,
    background-color,
    border-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    -webkit-text-decoration-color,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    backdrop-filter,
    -webkit-text-decoration-color,
    -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-250 {
  transition-duration: 250ms;
}

.duration-600 {
  transition-duration: 600ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-350 {
  transition-duration: 350ms;
}

.ease-soft-in-out {
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
}

.ease-soft {
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-soft-in {
  transition-timing-function: cubic-bezier(0.42, 0, 1, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.ease-soft-out {
  transition-timing-function: cubic-bezier(0, 0, 0.58, 1);
}

.ease-bounce {
  transition-timing-function: cubic-bezier(0.34, 1.61, 0.7, 1.3);
}

.will-change-transform {
  will-change: transform;
}

.transform3d {
  transform: perspective(999px) rotateX(0deg) translateZ(0);
}

.transform-dropdown {
  transform: perspective(999px) rotateX(-10deg) translateZ(0) translate3d(0, 37px, 0);
}

.transform-dropdown-show {
  transform: perspective(999px) rotateX(0deg) translateZ(0) translate3d(0, 37px, 5px);
}

.flex-wrap-inherit {
  flex-wrap: inherit;
}

.text-align-inherit {
  text-align: inherit;
}

.clip-rect-0 {
  clip: rect(0, 0, 0, 0);
}

.\[transform\:scale\(\.7\)_translateZ\(50px\)\] {
  transform: scale(0.7) translateZ(50px);
}

.placeholder\:text-gray-500::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(173 181 189 / var(--tw-text-opacity));
}

.placeholder\:text-gray-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(173 181 189 / var(--tw-text-opacity));
}

.before\:visible::before {
  content: var(--tw-content);
  visibility: visible;
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:relative::before {
  content: var(--tw-content);
  position: relative;
}

.before\:-left-5::before {
  content: var(--tw-content);
  left: -1.25rem;
}

.before\:top-1\/2::before {
  content: var(--tw-content);
  top: 50%;
}

.before\:-left-4\.5::before {
  content: var(--tw-content);
  left: -1.125rem;
}

.before\:-left-4::before {
  content: var(--tw-content);
  left: -1rem;
}

.before\:right-2::before {
  content: var(--tw-content);
  right: 0.5rem;
}

.before\:left-auto::before {
  content: var(--tw-content);
  left: auto;
}

.before\:top-0::before {
  content: var(--tw-content);
  top: 0px;
}

.before\:-top-5::before {
  content: var(--tw-content);
  top: -1.25rem;
}

.before\:right-7::before {
  content: var(--tw-content);
  right: 1.75rem;
}

.before\:left-1\/2::before {
  content: var(--tw-content);
  left: 50%;
}

.before\:right-5::before {
  content: var(--tw-content);
  right: 1.25rem;
}

.before\:-right-4\.5::before {
  content: var(--tw-content);
  right: -1.125rem;
}

.before\:-right-4::before {
  content: var(--tw-content);
  right: -1rem;
}

.before\:-right-5::before {
  content: var(--tw-content);
  right: -1.25rem;
}

.before\:left-2::before {
  content: var(--tw-content);
  left: 0.5rem;
}

.before\:right-auto::before {
  content: var(--tw-content);
  right: auto;
}

.before\:right-4::before {
  content: var(--tw-content);
  right: 1rem;
}

.before\:left-4::before {
  content: var(--tw-content);
  left: 1rem;
}

.before\:left-0::before {
  content: var(--tw-content);
  left: 0px;
}

.before\:z-50::before {
  content: var(--tw-content);
  z-index: 50;
}

.before\:z-40::before {
  content: var(--tw-content);
  z-index: 40;
}

.before\:z-30::before {
  content: var(--tw-content);
  z-index: 30;
}

.before\:float-right::before {
  content: var(--tw-content);
  float: right;
}

.before\:float-left::before {
  content: var(--tw-content);
  float: left;
}

.before\:-ml-1\/2::before {
  content: var(--tw-content);
  margin-left: -50%;
}

.before\:-ml-px::before {
  content: var(--tw-content);
  margin-left: -1px;
}

.before\:box-border::before {
  content: var(--tw-content);
  box-sizing: border-box;
}

.before\:block::before {
  content: var(--tw-content);
  display: block;
}

.before\:inline-block::before {
  content: var(--tw-content);
  display: inline-block;
}

.before\:h-2::before {
  content: var(--tw-content);
  height: 0.5rem;
}

.before\:h-1\.25::before {
  content: var(--tw-content);
  height: 0.3125rem;
}

.before\:h-1::before {
  content: var(--tw-content);
  height: 0.25rem;
}

.before\:h-3\.4::before {
  content: var(--tw-content);
  height: 0.85rem;
}

.before\:h-3::before {
  content: var(--tw-content);
  height: 0.75rem;
}

.before\:h-full::before {
  content: var(--tw-content);
  height: 100%;
}

.before\:h-px::before {
  content: var(--tw-content);
  height: 1px;
}

.before\:w-2::before {
  content: var(--tw-content);
  width: 0.5rem;
}

.before\:w-1\.25::before {
  content: var(--tw-content);
  width: 0.3125rem;
}

.before\:w-1::before {
  content: var(--tw-content);
  width: 0.25rem;
}

.before\:w-3\.4::before {
  content: var(--tw-content);
  width: 0.85rem;
}

.before\:w-3::before {
  content: var(--tw-content);
  width: 0.75rem;
}

.before\:w-3\/10::before {
  content: var(--tw-content);
  width: 30%;
}

.before\:w-0\.75::before {
  content: var(--tw-content);
  width: 0.2rem;
}

.before\:w-0::before {
  content: var(--tw-content);
  width: 0px;
}

.before\:-translate-y-1\/2::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.before\:-translate-x-1\/2::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.before\:rotate-45::before {
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.before\:scale-120::before {
  content: var(--tw-content);
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.before\:rounded-3xl::before {
  content: var(--tw-content);
  border-radius: 1.5rem;
}

.before\:rounded-circle::before {
  content: var(--tw-content);
  border-radius: 50%;
}

.before\:rounded-1::before {
  content: var(--tw-content);
  border-radius: 0.25rem;
}

.before\:border-2::before {
  content: var(--tw-content);
  border-width: 2px;
}

.before\:border-l-2::before {
  content: var(--tw-content);
  border-left-width: 2px;
}

.before\:border-r-2::before {
  content: var(--tw-content);
  border-right-width: 2px;
}

.before\:border-solid::before {
  content: var(--tw-content);
  border-style: solid;
}

.before\:border-current::before {
  content: var(--tw-content);
  border-color: currentColor;
}

.before\:border-slate-100::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(222 226 230 / var(--tw-border-opacity));
}

.before\:border-l-slate-100::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(222 226 230 / var(--tw-border-opacity));
}

.before\:border-r-slate-100::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(222 226 230 / var(--tw-border-opacity));
}

.before\:bg-slate-800::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(58 65 111 / var(--tw-bg-opacity));
}

.before\:bg-slate-800\/50::before {
  content: var(--tw-content);
  background-color: rgb(58 65 111 / 0.5);
}

.before\:bg-current::before {
  content: var(--tw-content);
  background-color: currentColor;
}

.before\:bg-white::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.before\:bg-inherit::before {
  content: var(--tw-content);
  background-color: inherit;
}

.before\:bg-fuchsia-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(203 12 159 / var(--tw-bg-opacity));
}

.before\:bg-slate-700::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(52 71 103 / var(--tw-bg-opacity));
}

.before\:bg-yellow-400::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(251 207 51 / var(--tw-bg-opacity));
}

.before\:bg-lime-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(130 214 22 / var(--tw-bg-opacity));
}

.before\:bg-gradient-to-r::before {
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.before\:from-transparent::before {
  content: var(--tw-content);
  --tw-gradient-from: transparent;
  --tw-gradient-to: rgb(0 0 0 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.before\:via-neutral-500\/40::before {
  content: var(--tw-content);
  --tw-gradient-to: rgb(117 117 117 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(117 117 117 / 0.4), var(--tw-gradient-to);
}

.before\:to-neutral-500\/40::before {
  content: var(--tw-content);
  --tw-gradient-to: rgb(117 117 117 / 0.4);
}

.before\:pr-2::before {
  content: var(--tw-content);
  padding-right: 0.5rem;
}

.before\:pl-2::before {
  content: var(--tw-content);
  padding-left: 0.5rem;
}

.before\:align-middle::before {
  content: var(--tw-content);
  vertical-align: middle;
}

.before\:font-awesome::before {
  content: var(--tw-content);
  font-family: FontAwesome;
}

.before\:text-5\.5::before {
  content: var(--tw-content);
  font-size: 1.375rem;
}

.before\:text-5::before {
  content: var(--tw-content);
  font-size: 1.25rem;
}

.before\:font-normal::before {
  content: var(--tw-content);
  font-weight: 400;
}

.before\:leading-default::before {
  content: var(--tw-content);
  line-height: 1.6;
}

.before\:text-gray-600::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(108 117 125 / var(--tw-text-opacity));
}

.before\:text-white::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.before\:antialiased::before {
  content: var(--tw-content);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.before\:transition-all::before {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:duration-350::before {
  content: var(--tw-content);
  transition-duration: 350ms;
}

.before\:ease-soft::before {
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
}

.before\:ease-linear::before {
  content: var(--tw-content);
  transition-timing-function: linear;
}

.before\:content-\[\'\'\]::before {
  --tw-content: "";
  content: var(--tw-content);
}

.before\:content-\[\'\/\'\]::before {
  --tw-content: "/";
  content: var(--tw-content);
}

.before\:content-\[\'\\f0d8\'\]::before {
  --tw-content: "\f0d8";
  content: var(--tw-content);
}

.before\:content-\[none\]::before {
  --tw-content: none;
  content: var(--tw-content);
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:relative::after {
  content: var(--tw-content);
  position: relative;
}

.after\:top-0::after {
  content: var(--tw-content);
  top: 0px;
}

.after\:bottom-0::after {
  content: var(--tw-content);
  bottom: 0px;
}

.after\:left-0::after {
  content: var(--tw-content);
  left: 0px;
}

.after\:top-px::after {
  content: var(--tw-content);
  top: 1px;
}

.after\:top-1\.25::after {
  content: var(--tw-content);
  top: 0.3125rem;
}

.after\:left-\[calc\(-50\%-13px\/2\)\]::after {
  content: var(--tw-content);
  left: calc(-50% - 13px / 2);
}

.after\:top-1::after {
  content: var(--tw-content);
  top: 0.25rem;
}

.after\:left-full::after {
  content: var(--tw-content);
  left: 100%;
}

.after\:right-0::after {
  content: var(--tw-content);
  right: 0px;
}

.after\:left-2::after {
  content: var(--tw-content);
  left: 0.5rem;
}

.after\:z-10::after {
  content: var(--tw-content);
  z-index: 10;
}

.after\:z-1::after {
  content: var(--tw-content);
  z-index: 1;
}

.after\:clear-both::after {
  content: var(--tw-content);
  clear: both;
}

.after\:ml-auto::after {
  content: var(--tw-content);
  margin-left: auto;
}

.after\:ml-1::after {
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.after\:mr-auto::after {
  content: var(--tw-content);
  margin-right: auto;
}

.after\:-mr-1\/2::after {
  content: var(--tw-content);
  margin-right: -50%;
}

.after\:block::after {
  content: var(--tw-content);
  display: block;
}

.after\:inline-block::after {
  content: var(--tw-content);
  display: inline-block;
}

.after\:flex::after {
  content: var(--tw-content);
  display: flex;
}

.after\:table::after {
  content: var(--tw-content);
  display: table;
}

.after\:h-full::after {
  content: var(--tw-content);
  height: 100%;
}

.after\:h-4::after {
  content: var(--tw-content);
  height: 1rem;
}

.after\:h-0\.5::after {
  content: var(--tw-content);
  height: 0.125rem;
}

.after\:h-0::after {
  content: var(--tw-content);
  height: 0px;
}

.after\:h-px::after {
  content: var(--tw-content);
  height: 1px;
}

.after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}

.after\:w-4::after {
  content: var(--tw-content);
  width: 1rem;
}

.after\:w-1\/2::after {
  content: var(--tw-content);
  width: 50%;
}

.after\:w-3\/10::after {
  content: var(--tw-content);
  width: 30%;
}

.after\:translate-x-px::after {
  content: var(--tw-content);
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.after\:-translate-x-px::after {
  content: var(--tw-content);
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.after\:rotate-180::after {
  content: var(--tw-content);
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.after\:items-center::after {
  content: var(--tw-content);
  align-items: center;
}

.after\:justify-center::after {
  content: var(--tw-content);
  justify-content: center;
}

.after\:rounded-2xl::after {
  content: var(--tw-content);
  border-radius: 1rem;
}

.after\:rounded-circle::after {
  content: var(--tw-content);
  border-radius: 50%;
}

.after\:border-3::after {
  content: var(--tw-content);
  border-width: 3px;
}

.after\:border-b-0::after {
  content: var(--tw-content);
  border-bottom-width: 0px;
}

.after\:border-solid::after {
  content: var(--tw-content);
  border-style: solid;
}

.after\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}

.after\:border-t-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.after\:bg-current::after {
  content: var(--tw-content);
  background-color: currentColor;
}

.after\:bg-black\/40::after {
  content: var(--tw-content);
  background-color: rgb(0 0 0 / 0.4);
}

.after\:bg-gradient-to-tl::after {
  content: var(--tw-content);
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

.after\:bg-gradient-to-r::after {
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.after\:from-gray-900::after {
  content: var(--tw-content);
  --tw-gradient-from: #141727;
  --tw-gradient-to: rgb(20 23 39 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-blue-600::after {
  content: var(--tw-content);
  --tw-gradient-from: #2152ff;
  --tw-gradient-to: rgb(33 82 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-red-500::after {
  content: var(--tw-content);
  --tw-gradient-from: #f53939;
  --tw-gradient-to: rgb(245 57 57 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-green-600::after {
  content: var(--tw-content);
  --tw-gradient-from: #17ad37;
  --tw-gradient-to: rgb(23 173 55 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-red-600::after {
  content: var(--tw-content);
  --tw-gradient-from: #ea0606;
  --tw-gradient-to: rgb(234 6 6 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-slate-600::after {
  content: var(--tw-content);
  --tw-gradient-from: #627594;
  --tw-gradient-to: rgb(98 117 148 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-neutral-500\/40::after {
  content: var(--tw-content);
  --tw-gradient-from: rgb(117 117 117 / 0.4);
  --tw-gradient-to: rgb(117 117 117 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-purple-700::after {
  content: var(--tw-content);
  --tw-gradient-from: #7928ca;
  --tw-gradient-to: rgb(121 40 202 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:via-neutral-500\/40::after {
  content: var(--tw-content);
  --tw-gradient-to: rgb(117 117 117 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(117 117 117 / 0.4), var(--tw-gradient-to);
}

.after\:to-slate-800::after {
  content: var(--tw-content);
  --tw-gradient-to: #3a416f;
}

.after\:to-cyan-400::after {
  content: var(--tw-content);
  --tw-gradient-to: #21d4fd;
}

.after\:to-yellow-400::after {
  content: var(--tw-content);
  --tw-gradient-to: #fbcf33;
}

.after\:to-lime-400::after {
  content: var(--tw-content);
  --tw-gradient-to: #98ec2d;
}

.after\:to-rose-400::after {
  content: var(--tw-content);
  --tw-gradient-to: #ff667c;
}

.after\:to-slate-300::after {
  content: var(--tw-content);
  --tw-gradient-to: #a8b8d8;
}

.after\:to-transparent::after {
  content: var(--tw-content);
  --tw-gradient-to: transparent;
}

.after\:to-pink-500::after {
  content: var(--tw-content);
  --tw-gradient-to: #ff0080;
}

.after\:align-middle::after {
  content: var(--tw-content);
  vertical-align: middle;
}

.after\:align-\[\.255em\]::after {
  content: var(--tw-content);
  vertical-align: 0.255em;
}

.after\:font-awesome-5-free::after {
  content: var(--tw-content);
  font-family: Font Awesome\5 Free;
}

.after\:font-awesome::after {
  content: var(--tw-content);
  font-family: FontAwesome;
}

.after\:text-xxs::after {
  content: var(--tw-content);
  font-size: 0.65rem;
  line-height: 1rem;
}

.after\:text-\[0\.67rem\]::after {
  content: var(--tw-content);
  font-size: 0.67rem;
}

.after\:font-bold::after {
  content: var(--tw-content);
  font-weight: 700;
}

.after\:text-slate-800::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(58 65 111 / var(--tw-text-opacity));
}

.after\:text-slate-800\/50::after {
  content: var(--tw-content);
  color: rgb(58 65 111 / 0.5);
}

.after\:text-white::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.after\:antialiased::after {
  content: var(--tw-content);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.after\:opacity-65::after {
  content: var(--tw-content);
  opacity: 0.65;
}

.after\:opacity-85::after {
  content: var(--tw-content);
  opacity: 0.85;
}

.after\:opacity-0::after {
  content: var(--tw-content);
  opacity: 0;
}

.after\:shadow-soft-2xl::after {
  content: var(--tw-content);
  --tw-shadow: 0 0.3125rem 0.625rem 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 0.3125rem 0.625rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:duration-200::after {
  content: var(--tw-content);
  transition-duration: 200ms;
}

.after\:duration-250::after {
  content: var(--tw-content);
  transition-duration: 250ms;
}

.after\:ease-soft-in-out::after {
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
}

.after\:ease-linear::after {
  content: var(--tw-content);
  transition-timing-function: linear;
}

.after\:content-\[\'\\f107\'\]::after {
  --tw-content: "\f107";
  content: var(--tw-content);
}

.after\:content-\[\'\'\]::after {
  --tw-content: "";
  content: var(--tw-content);
}

.after\:content-\[\'\\\\f107\'\]::after {
  --tw-content: "\\f107";
  content: var(--tw-content);
}

.after\:content-\[\'\\f00c\'\]::after {
  --tw-content: "\f00c";
  content: var(--tw-content);
}

.checked\:border-0:checked {
  border-width: 0px;
}

.checked\:border-slate-800\/95:checked {
  border-color: rgb(58 65 111 / 0.95);
}

.checked\:border-transparent:checked {
  border-color: transparent;
}

.checked\:bg-slate-800\/95:checked {
  background-color: rgb(58 65 111 / 0.95);
}

.checked\:bg-transparent:checked {
  background-color: transparent;
}

.checked\:bg-none:checked {
  background-image: none;
}

.checked\:bg-gradient-to-tl:checked {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

.checked\:from-gray-900:checked {
  --tw-gradient-from: #141727;
  --tw-gradient-to: rgb(20 23 39 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.checked\:to-slate-800:checked {
  --tw-gradient-to: #3a416f;
}

.checked\:bg-right:checked {
  background-position: right;
}

.checked\:bg-left:checked {
  background-position: left;
}

.checked\:after\:translate-x-5\.3:checked::after {
  content: var(--tw-content);
  --tw-translate-x: 1.3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.checked\:after\:translate-x-5:checked::after {
  content: var(--tw-content);
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.checked\:after\:-translate-x-5\.3:checked::after {
  content: var(--tw-content);
  --tw-translate-x: -1.3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.checked\:after\:-translate-x-5:checked::after {
  content: var(--tw-content);
  --tw-translate-x: -1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.checked\:after\:opacity-100:checked::after {
  content: var(--tw-content);
  opacity: 1;
}

.hover\:z-30:hover {
  z-index: 30;
}

.hover\:scale-102:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.hover\:border-fuchsia-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(203 12 159 / var(--tw-border-opacity));
}

.hover\:border-slate-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(52 71 103 / var(--tw-border-opacity));
}

.hover\:border-slate-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(131 146 171 / var(--tw-border-opacity));
}

.hover\:border-white:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.hover\:bg-transparent:hover {
  background-color: transparent;
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(233 236 239 / var(--tw-bg-opacity));
}

.hover\:bg-slate-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(52 71 103 / var(--tw-bg-opacity));
}

.hover\:text-fuchsia-500:hover {
  --tw-text-opacity: 1;
  color: rgb(203 12 159 / var(--tw-text-opacity));
}

.hover\:text-slate-700:hover {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.hover\:text-slate-400:hover {
  --tw-text-opacity: 1;
  color: rgb(131 146 171 / var(--tw-text-opacity));
}

.hover\:text-lime-500:hover {
  --tw-text-opacity: 1;
  color: rgb(130 214 22 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(234 6 6 / var(--tw-text-opacity));
}

.hover\:text-fuchsia-800:hover {
  --tw-text-opacity: 1;
  color: rgb(131 8 102 / var(--tw-text-opacity));
}

.hover\:opacity-75:hover {
  opacity: 0.75;
}

.hover\:shadow-soft-2xl:hover {
  --tw-shadow: 0 0.3125rem 0.625rem 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 0.3125rem 0.625rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.hover\:shadow-none:hover {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.hover\:shadow-soft-xs:hover {
  --tw-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.09), 0 2px 3px -1px rgba(0, 0, 0, 0.07);
  --tw-shadow-colored: 0 3px 5px -1px var(--tw-shadow-color), 0 2px 3px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.hover\:transform3d-hover:hover {
  transform: perspective(999px) rotateX(7deg) translate3d(0, -4px, 5px);
}

.focus\:border-fuchsia-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(226 147 211 / var(--tw-border-opacity));
}

.focus\:bg-gray-200:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(233 236 239 / var(--tw-bg-opacity));
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.focus\:text-slate-700:focus {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.focus\:text-gray-700:focus {
  --tw-text-opacity: 1;
  color: rgb(73 80 87 / var(--tw-text-opacity));
}

.focus\:shadow-soft-primary-outline:focus {
  --tw-shadow: 0 0 0 2px #e9aede;
  --tw-shadow-colored: 0 0 0 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:transition-shadow:focus {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.active\:scale-100:active {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.active\:border-slate-400:active {
  --tw-border-opacity: 1;
  border-color: rgb(131 146 171 / var(--tw-border-opacity));
}

.active\:border-white:active {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.active\:border-slate-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(52 71 103 / var(--tw-border-opacity));
}

.active\:border-fuchsia-500:active {
  --tw-border-opacity: 1;
  border-color: rgb(203 12 159 / var(--tw-border-opacity));
}

.active\:bg-fuchsia-500:active {
  --tw-bg-opacity: 1;
  background-color: rgb(203 12 159 / var(--tw-bg-opacity));
}

.active\:bg-slate-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(52 71 103 / var(--tw-bg-opacity));
}

.active\:bg-slate-400:active {
  --tw-bg-opacity: 1;
  background-color: rgb(131 146 171 / var(--tw-bg-opacity));
}

.active\:bg-lime-500:active {
  --tw-bg-opacity: 1;
  background-color: rgb(130 214 22 / var(--tw-bg-opacity));
}

.active\:bg-white:active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.active\:bg-red-600:active {
  --tw-bg-opacity: 1;
  background-color: rgb(234 6 6 / var(--tw-bg-opacity));
}

.active\:text-white:active {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.active\:text-black:active {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.active\:opacity-85:active {
  opacity: 0.85;
}

.active\:shadow-soft-xs:active {
  --tw-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.09), 0 2px 3px -1px rgba(0, 0, 0, 0.07);
  --tw-shadow-colored: 0 3px 5px -1px var(--tw-shadow-color), 0 2px 3px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.hover\:active\:scale-102:active:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.hover\:active\:border-white:active:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.hover\:active\:border-slate-400:active:hover {
  --tw-border-opacity: 1;
  border-color: rgb(131 146 171 / var(--tw-border-opacity));
}

.active\:hover\:border-white:hover:active {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.hover\:active\:border-slate-700:active:hover {
  --tw-border-opacity: 1;
  border-color: rgb(52 71 103 / var(--tw-border-opacity));
}

.hover\:active\:border-fuchsia-500:active:hover {
  --tw-border-opacity: 1;
  border-color: rgb(203 12 159 / var(--tw-border-opacity));
}

.active\:hover\:bg-transparent:hover:active {
  background-color: transparent;
}

.hover\:active\:bg-transparent:active:hover {
  background-color: transparent;
}

.active\:hover\:text-fuchsia-500:hover:active {
  --tw-text-opacity: 1;
  color: rgb(203 12 159 / var(--tw-text-opacity));
}

.active\:hover\:text-slate-700:hover:active {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.hover\:active\:text-slate-400:active:hover {
  --tw-text-opacity: 1;
  color: rgb(131 146 171 / var(--tw-text-opacity));
}

.hover\:active\:text-lime-500:active:hover {
  --tw-text-opacity: 1;
  color: rgb(130 214 22 / var(--tw-text-opacity));
}

.hover\:active\:text-white:active:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:active\:text-red-600:active:hover {
  --tw-text-opacity: 1;
  color: rgb(234 6 6 / var(--tw-text-opacity));
}

.active\:hover\:text-white:hover:active {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:active\:text-slate-700:active:hover {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.hover\:active\:text-fuchsia-500:active:hover {
  --tw-text-opacity: 1;
  color: rgb(203 12 159 / var(--tw-text-opacity));
}

.hover\:active\:opacity-75:active:hover {
  opacity: 0.75;
}

.active\:hover\:opacity-75:hover:active {
  opacity: 0.75;
}

.active\:hover\:shadow-none:hover:active {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.hover\:active\:shadow-none:active:hover {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.group:hover .group-hover\:translate-x-1\.25 {
  --tw-translate-x: 0.3125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.peer:checked ~ .peer-checked\:bg-slate-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(131 146 171 / var(--tw-bg-opacity));
}

.dark .dark\:pointer-events-none {
  pointer-events: none;
}

.dark .dark\:block {
  display: block;
}

.dark .dark\:inline-block {
  display: inline-block;
}

.dark .dark\:hidden {
  display: none;
}

.dark .dark\:cursor-not-allowed {
  cursor: not-allowed;
}

.dark .dark\:border-white\/40 {
  border-color: rgb(255 255 255 / 0.4);
}

.dark .dark\:border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.dark .dark\:border-slate-700 {
  --tw-border-opacity: 1;
  border-color: rgb(52 71 103 / var(--tw-border-opacity));
}

.dark .dark\:bg-slate-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 23 40 / var(--tw-bg-opacity));
}

.dark .dark\:bg-gray-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 19 34 / var(--tw-bg-opacity));
}

.dark .dark\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.dark .dark\:bg-gray-950\/80 {
  background-color: rgb(17 19 34 / 0.8);
}

.dark .dark\:bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 236 239 / var(--tw-bg-opacity));
}

.dark .dark\:bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(108 117 125 / var(--tw-bg-opacity));
}

.dark .dark\:bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(58 65 111 / var(--tw-bg-opacity));
}

.dark .dark\:bg-transparent {
  background-color: transparent;
}

.dark .dark\:bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.dark .dark\:bg-gradient-to-tl {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

.dark .dark\:bg-none {
  background-image: none;
}

.dark .dark\:from-transparent {
  --tw-gradient-from: transparent;
  --tw-gradient-to: rgb(0 0 0 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark .dark\:from-slate-850 {
  --tw-gradient-from: #323a54;
  --tw-gradient-to: rgb(50 58 84 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark .dark\:from-gray-900 {
  --tw-gradient-from: #141727;
  --tw-gradient-to: rgb(20 23 39 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark .dark\:via-white {
  --tw-gradient-to: rgb(255 255 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), #fff, var(--tw-gradient-to);
}

.dark .dark\:to-transparent {
  --tw-gradient-to: transparent;
}

.dark .dark\:to-gray-850 {
  --tw-gradient-to: #1a2035;
}

.dark .dark\:to-slate-800 {
  --tw-gradient-to: #3a416f;
}

.dark .dark\:fill-white {
  fill: #fff;
}

.dark .dark\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .dark\:text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

.dark .dark\:text-slate-950 {
  --tw-text-opacity: 1;
  color: rgb(20 23 40 / var(--tw-text-opacity));
}

.dark .dark\:text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}

.dark .dark\:text-white\/40 {
  color: rgb(255 255 255 / 0.4);
}

.dark .dark\:text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(130 214 22 / var(--tw-text-opacity));
}

.dark .dark\:text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}

.dark .dark\:opacity-80 {
  opacity: 0.8;
}

.dark .dark\:opacity-100 {
  opacity: 1;
}

.dark .dark\:opacity-60 {
  opacity: 0.6;
}

.dark .dark\:shadow-soft-dark-xl {
  --tw-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 2px 2px 0 var(--tw-shadow-color), 0 3px 1px -2px var(--tw-shadow-color),
    0 1px 5px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.dark .dark\:shadow-dark-blur {
  --tw-shadow: inset 0 0 1px 1px hsla(0, 0%, 100%, 0.4), 0 20px 27px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: inset 0 0 1px 1px var(--tw-shadow-color),
    0 20px 27px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.dark .dark\:placeholder\:text-white\/80::-moz-placeholder {
  color: rgb(255 255 255 / 0.8);
}

.dark .dark\:placeholder\:text-white\/80::placeholder {
  color: rgb(255 255 255 / 0.8);
}

.dark .dark\:before\:bg-white::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.dark .dark\:before\:text-white::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .dark\:before\:opacity-80::before {
  content: var(--tw-content);
  opacity: 0.8;
}

.dark .dark\:after\:text-white\/50::after {
  content: var(--tw-content);
  color: rgb(255 255 255 / 0.5);
}

.dark .dark\:after\:text-white::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .dark\:hover\:bg-gray-200\/80:hover {
  background-color: rgb(233 236 239 / 0.8);
}

.dark .dark\:hover\:text-slate-700:hover {
  --tw-text-opacity: 1;
  color: rgb(52 71 103 / var(--tw-text-opacity));
}

.dark .dark\:hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .dark\:active\:border-white:active {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.dark .dark\:hover\:active\:border-white:active:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.dark .dark\:active\:hover\:text-white:hover:active {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 576px) {
  .sm\:m-7 {
    margin: 1.75rem;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-auto {
    margin-top: auto;
    margin-bottom: auto;
  }

  .sm\:-mx-2 {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }

  .sm\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .sm\:mr-16 {
    margin-right: 4rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mr-6 {
    margin-right: 1.5rem;
  }

  .sm\:mr-1 {
    margin-right: 0.25rem;
  }

  .sm\:-mr-6 {
    margin-right: -1.5rem;
  }

  .sm\:mt-4 {
    margin-top: 1rem;
  }

  .sm\:mr-0 {
    margin-right: 0px;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:ml-16 {
    margin-left: 4rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:ml-1 {
    margin-left: 0.25rem;
  }

  .sm\:-ml-6 {
    margin-left: -1.5rem;
  }

  .sm\:mt-64 {
    margin-top: 16rem;
  }

  .sm\:ml-2 {
    margin-left: 0.5rem;
  }

  .sm\:mt-12 {
    margin-top: 3rem;
  }

  .sm\:ml-auto {
    margin-left: auto;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:w-6\/12 {
    width: 50%;
  }

  .sm\:w-4\/12 {
    width: 33.333333%;
  }

  .sm\:w-8\/12 {
    width: 66.666667%;
  }

  .sm\:w-3\/12 {
    width: 25%;
  }

  .sm\:w-10\/12 {
    width: 83.333333%;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-15\/100 {
    width: 15%;
  }

  .sm\:max-w-125 {
    max-width: 31.25rem;
  }

  .sm\:flex-0 {
    flex: 0 0 auto;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:pt-4 {
    padding-top: 1rem;
  }

  .sm\:pl-2 {
    padding-left: 0.5rem;
  }

  .sm\:indent-0 {
    text-indent: 0px;
  }

  .before\:sm\:right-7::before {
    content: var(--tw-content);
    right: 1.75rem;
  }

  .before\:sm\:left-7::before {
    content: var(--tw-content);
    left: 1.75rem;
  }
}

@media (max-width: 576px) {
  .sm-max\:right-auto {
    right: auto;
  }
}

@media (min-width: 768px) {
  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:ml-0 {
    margin-left: 0px;
  }

  .md\:mr-auto {
    margin-right: auto;
  }

  .md\:-mt-56 {
    margin-top: -14rem;
  }

  .md\:-ml-12 {
    margin-left: -3rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mt-12 {
    margin-top: 3rem;
  }

  .md\:block {
    display: block;
  }

  .md\:w-8\/12 {
    width: 66.666667%;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:w-6\/12 {
    width: 50%;
  }

  .md\:w-4\/12 {
    width: 33.333333%;
  }

  .md\:w-7\/12 {
    width: 58.333333%;
  }

  .md\:w-5\/12 {
    width: 41.666667%;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-10\/12 {
    width: 83.333333%;
  }

  .md\:w-1\/12 {
    width: 8.333333%;
  }

  .md\:w-11\/12 {
    width: 91.666667%;
  }

  .md\:w-2\/12 {
    width: 16.666667%;
  }

  .md\:flex-0 {
    flex: 0 0 auto;
  }

  .md\:flex-none {
    flex: none;
  }

  .md\:scale-60 {
    --tw-scale-x: 0.6;
    --tw-scale-y: 0.6;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }

  .md\:scale-70 {
    --tw-scale-x: 0.7;
    --tw-scale-y: 0.7;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:pr-4 {
    padding-right: 1rem;
  }

  .md\:pl-4 {
    padding-left: 1rem;
  }

  .md\:text-right {
    text-align: right;
  }
}

@media (max-width: 768px) {
  .md-max\:static {
    position: static;
  }

  .md-max\:relative {
    position: relative;
  }

  .md-max\:left-0 {
    left: 0px;
  }

  .md-max\:right-0 {
    right: 0px;
  }

  .md-max\:w-full {
    width: 100%;
  }
}

@media (min-width: 992px) {
  .lg\:pointer-events-none {
    pointer-events: none;
  }

  .lg\:pointer-events-auto {
    pointer-events: auto;
  }

  .lg\:absolute {
    position: absolute;
  }

  .lg\:right-0 {
    right: 0px;
  }

  .lg\:left-auto {
    left: auto;
  }

  .lg\:top-0 {
    top: 0px;
  }

  .lg\:left-0 {
    left: 0px;
  }

  .lg\:right-auto {
    right: auto;
  }

  .lg\:float-right {
    float: right;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mt-2 {
    margin-top: 0.5rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mr-24 {
    margin-right: 6rem;
  }

  .lg\:mt-12 {
    margin-top: 3rem;
  }

  .lg\:mr-0 {
    margin-right: 0px;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:ml-16 {
    margin-left: 4rem;
  }

  .lg\:-mt-32 {
    margin-top: -8rem;
  }

  .lg\:ml-auto {
    margin-left: auto;
  }

  .lg\:ml-0 {
    margin-left: 0px;
  }

  .lg\:mt-4 {
    margin-top: 1rem;
  }

  .lg\:-mt-48 {
    margin-top: -12rem;
  }

  .lg\:mr-48 {
    margin-right: 12rem;
  }

  .lg\:ml-12 {
    margin-left: 3rem;
  }

  .lg\:-mt-6 {
    margin-top: -1.5rem;
  }

  .lg\:mt-auto {
    margin-top: auto;
  }

  .lg\:-ml-6 {
    margin-left: -1.5rem;
  }

  .lg\:mt-48 {
    margin-top: 12rem;
  }

  .lg\:mt-24 {
    margin-top: 6rem;
  }

  .lg\:mt-6 {
    margin-top: 1.5rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:w-7\/12 {
    width: 58.333333%;
  }

  .lg\:w-6\/12 {
    width: 50%;
  }

  .lg\:w-5\/12 {
    width: 41.666667%;
  }

  .lg\:w-10\/12 {
    width: 83.333333%;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-8\/12 {
    width: 66.666667%;
  }

  .lg\:w-3\/12 {
    width: 25%;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-4\/12 {
    width: 33.333333%;
  }

  .lg\:w-2\/12 {
    width: 16.666667%;
  }

  .lg\:w-1\/12 {
    width: 8.333333%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-9\/12 {
    width: 75%;
  }

  .lg\:min-w-160 {
    min-width: 40rem;
  }

  .lg\:min-w-92 {
    min-width: 23rem;
  }

  .lg\:min-w-60 {
    min-width: 15rem;
  }

  .lg\:max-w-120 {
    max-width: 30rem;
  }

  .lg\:flex-0 {
    flex: 0 0 auto;
  }

  .lg\:flex-none {
    flex: none;
  }

  .lg\:basis-auto {
    flex-basis: auto;
  }

  .lg\:origin-top {
    transform-origin: top;
  }

  .lg\:-rotate-90 {
    --tw-rotate: -90deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }

  .lg\:cursor-pointer {
    cursor: pointer;
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .lg\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .lg\:pb-40 {
    padding-bottom: 10rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-right {
    text-align: right;
  }

  .lg\:opacity-0 {
    opacity: 0;
  }

  .lg\:opacity-100 {
    opacity: 1;
  }

  .lg\:shadow-soft-3xl {
    --tw-shadow: 0 8px 26px -4px hsla(0, 0%, 8%, 0.15), 0 8px 9px -5px hsla(0, 0%, 8%, 0.06);
    --tw-shadow-colored: 0 8px 26px -4px var(--tw-shadow-color),
      0 8px 9px -5px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }

  .lg\:transition-colors {
    transition-property:
      color,
      background-color,
      border-color,
      fill,
      stroke,
      -webkit-text-decoration-color;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-property:
      color,
      background-color,
      border-color,
      text-decoration-color,
      fill,
      stroke,
      -webkit-text-decoration-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .lg\:transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .lg\:duration-300 {
    transition-duration: 300ms;
  }

  .lg\:duration-250 {
    transition-duration: 250ms;
  }

  .lg\:ease-soft {
    transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .lg\:transform-dropdown {
    transform: perspective(999px) rotateX(-10deg) translateZ(0) translate3d(0, 37px, 0);
  }

  .lg\:transform-dropdown-nested {
    transform: perspective(999px) rotateX(0deg) translateZ(0) translateZ(5px);
  }

  .lg\:before\:absolute::before {
    content: var(--tw-content);
    position: absolute;
  }

  .lg\:before\:left-0::before {
    content: var(--tw-content);
    left: 0px;
  }

  .lg\:before\:-left-2::before {
    content: var(--tw-content);
    left: -0.5rem;
  }

  .lg\:before\:top-0::before {
    content: var(--tw-content);
    top: 0px;
  }

  .lg\:before\:left-7::before {
    content: var(--tw-content);
    left: 1.75rem;
  }

  .lg\:before\:right-auto::before {
    content: var(--tw-content);
    right: auto;
  }

  .lg\:before\:-z-1::before {
    content: var(--tw-content);
    z-index: -1;
  }

  .before\:lg\:-mr-px::before {
    content: var(--tw-content);
    margin-right: -1px;
  }

  .before\:lg\:-ml-px::before {
    content: var(--tw-content);
    margin-left: -1px;
  }

  .lg\:before\:-ml-px::before {
    content: var(--tw-content);
    margin-left: -1px;
  }

  .lg\:before\:-rotate-90::before {
    content: var(--tw-content);
    --tw-rotate: -90deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }

  .lg\:before\:font-awesome::before {
    content: var(--tw-content);
    font-family: FontAwesome;
  }

  .lg\:before\:text-5\.5::before {
    content: var(--tw-content);
    font-size: 1.375rem;
  }

  .lg\:before\:text-5::before {
    content: var(--tw-content);
    font-size: 1.25rem;
  }

  .lg\:before\:text-white::before {
    content: var(--tw-content);
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }

  .lg\:before\:transition-all::before {
    content: var(--tw-content);
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .lg\:before\:duration-350::before {
    content: var(--tw-content);
    transition-duration: 350ms;
  }

  .lg\:before\:ease-soft::before {
    content: var(--tw-content);
    transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .lg\:before\:content-\[\'\\f0d8\'\]::before {
    --tw-content: "\f0d8";
    content: var(--tw-content);
  }

  .lg\:after\:absolute::after {
    content: var(--tw-content);
    position: absolute;
  }

  .lg\:after\:top-0::after {
    content: var(--tw-content);
    top: 0px;
  }

  .lg\:after\:left-0::after {
    content: var(--tw-content);
    left: 0px;
  }

  .lg\:after\:-bottom-6::after {
    content: var(--tw-content);
    bottom: -1.5rem;
  }

  .lg\:after\:h-full::after {
    content: var(--tw-content);
    height: 100%;
  }

  .lg\:after\:w-full::after {
    content: var(--tw-content);
    width: 100%;
  }

  .lg\:after\:content-\[\'\'\]::after {
    --tw-content: "";
    content: var(--tw-content);
  }

  .lg\:hover\:after\:top-full:hover::after {
    content: var(--tw-content);
    top: 100%;
  }

  .group:hover .lg\:group-hover\:pointer-events-auto {
    pointer-events: auto;
  }

  .group:hover .lg\:group-hover\:opacity-75 {
    opacity: 0.75;
  }

  .group:hover .lg\:group-hover\:opacity-100 {
    opacity: 1;
  }

  .group:hover .lg\:group-hover\:transform-dropdown-show {
    transform: perspective(999px) rotateX(0deg) translateZ(0) translate3d(0, 37px, 5px);
  }

  .group:hover .lg\:group-hover\:before\:-top-4\.8::before {
    content: var(--tw-content);
    top: -1.2rem;
  }

  .group:hover .lg\:group-hover\:before\:-top-4::before {
    content: var(--tw-content);
    top: -1rem;
  }

  .group:focus .lg\:group-focus\:pointer-events-auto {
    pointer-events: auto;
  }

  .group:focus .lg\:group-focus\:opacity-100 {
    opacity: 1;
  }

  .group:focus .lg\:group-focus\:transform-dropdown-show {
    transform: perspective(999px) rotateX(0deg) translateZ(0) translate3d(0, 37px, 5px);
  }

  .group:focus .lg\:group-focus\:before\:-top-5::before {
    content: var(--tw-content);
    top: -1.25rem;
  }
}

@media (max-width: 992px) {
  .lg-max\:pointer-events-none {
    pointer-events: none;
  }

  .lg-max\:pointer-events-auto {
    pointer-events: auto;
  }

  .lg-max\:relative {
    position: relative;
  }

  .lg-max\:top-0 {
    top: 0px;
  }

  .lg-max\:mt-6 {
    margin-top: 1.5rem;
  }

  .lg-max\:block {
    display: block;
  }

  .lg-max\:hidden {
    display: none;
  }

  .lg-max\:h-0 {
    height: 0px;
  }

  .lg-max\:h-64 {
    height: 16rem;
  }

  .lg-max\:max-h-0 {
    max-height: 0px;
  }

  .lg-max\:max-h-116 {
    max-height: 29rem;
  }

  .lg-max\:w-full {
    width: 100%;
  }

  .lg-max\:origin-top {
    transform-origin: top;
  }

  .lg-max\:transform-none {
    transform: none;
  }

  .lg-max\:overflow-auto {
    overflow: auto;
  }

  .lg-max\:overflow-hidden {
    overflow: hidden;
  }

  .lg-max\:overflow-scroll {
    overflow: scroll;
  }

  .lg-max\:overflow-x-hidden {
    overflow-x: hidden;
  }

  .lg-max\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  }

  .lg-max\:bg-transparent {
    background-color: transparent;
  }

  .lg-max\:pt-0 {
    padding-top: 0px;
  }

  .lg-max\:pb-0 {
    padding-bottom: 0px;
  }

  .lg-max\:text-slate-700 {
    --tw-text-opacity: 1;
    color: rgb(52 71 103 / var(--tw-text-opacity));
  }

  .lg-max\:opacity-0 {
    opacity: 0;
  }

  .lg-max\:opacity-100 {
    opacity: 1;
  }

  .lg-max\:shadow-soft-3xl {
    --tw-shadow: 0 8px 26px -4px hsla(0, 0%, 8%, 0.15), 0 8px 9px -5px hsla(0, 0%, 8%, 0.06);
    --tw-shadow-colored: 0 8px 26px -4px var(--tw-shadow-color),
      0 8px 9px -5px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }

  .lg-max\:shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }

  .lg-max\:transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .lg-max\:duration-350 {
    transition-duration: 350ms;
  }

  .lg-max\:ease-soft {
    transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  }
}

@media (min-width: 1200px) {
  .xl\:left-\[18\%\] {
    left: 18%;
  }

  .xl\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .xl\:ml-4 {
    margin-left: 1rem;
  }

  .xl\:ml-68 {
    margin-left: 17rem;
  }

  .xl\:ml-30 {
    margin-left: 7.5rem;
  }

  .xl\:mr-30 {
    margin-right: 7.5rem;
  }

  .xl\:mr-68 {
    margin-right: 17rem;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mt-0 {
    margin-top: 0px;
  }

  .xl\:mr-12 {
    margin-right: 3rem;
  }

  .xl\:mr-4 {
    margin-right: 1rem;
  }

  .xl\:mt-6 {
    margin-top: 1.5rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:w-3\/12 {
    width: 25%;
  }

  .xl\:w-9\/12 {
    width: 75%;
  }

  .xl\:w-full {
    width: 100%;
  }

  .xl\:w-8\/12 {
    width: 66.666667%;
  }

  .xl\:w-4\/12 {
    width: 33.333333%;
  }

  .xl\:w-7\/12 {
    width: 58.333333%;
  }

  .xl\:w-5\/12 {
    width: 41.666667%;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:flex-0 {
    flex: 0 0 auto;
  }

  .xl\:flex-none {
    flex: none;
  }

  .xl\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }

  .xl\:scale-60 {
    --tw-scale-x: 0.6;
    --tw-scale-y: 0.6;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }

  @-webkit-keyframes fade-up {
    from {
      opacity: 0;
      transform: translateY(100%);
    }

    to {
      opacity: 1;
    }
  }

  @keyframes fade-up {
    from {
      opacity: 0;
      transform: translateY(100%);
    }

    to {
      opacity: 1;
    }
  }

  .xl\:animate-fade-up {
    -webkit-animation: fade-up 1.5s both;
    animation: fade-up 1.5s both;
  }

  .xl\:bg-transparent {
    background-color: transparent;
  }

  .xl\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  }

  .xl\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .xl\:text-8xl {
    font-size: 5rem;
    line-height: 1;
  }

  .xl\:shadow-soft-xl {
    --tw-shadow: 0 20px 27px 0 rgba(0, 0, 0, 0.05);
    --tw-shadow-colored: 0 20px 27px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}

@media (max-width: 1200px) {
  .xl-max\:pointer-events-none {
    pointer-events: none;
  }

  .xl-max\:cursor-not-allowed {
    cursor: not-allowed;
  }

  .xl-max\:border-0 {
    border-width: 0px;
  }

  .xl-max\:bg-gradient-to-tl {
    background-image: linear-gradient(to top left, var(--tw-gradient-stops));
  }

  .xl-max\:from-purple-700 {
    --tw-gradient-from: #7928ca;
    --tw-gradient-to: rgb(121 40 202 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .xl-max\:to-pink-500 {
    --tw-gradient-to: #ff0080;
  }

  .xl-max\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }

  .xl-max\:opacity-65 {
    opacity: 0.65;
  }
}
