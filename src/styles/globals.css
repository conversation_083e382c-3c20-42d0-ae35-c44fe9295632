@tailwind base;
@tailwind components;
@tailwind utilities;

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {
  html {
    font-family: "Source Sans Pro";
  }
  body {
    font-family: "Source Sans Pro";
  }
}

html {
  font-family: "Source Sans Pro";
}
body {
  font-family: "Source Sans Pro";
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@layer components {
  .btn {
    @apply mb-0 inline-block cursor-pointer rounded-lg border-0 bg-primary px-4 py-2 text-center align-middle font-bold leading-pro tracking-tight-soft text-white shadow-soft-md hover:brightness-90 transition-all ease-soft-in hover:shadow-soft-xs active:opacity-85;
  }
  .input {
    @apply block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80;
  }

  .siteHeadline {
  }
}
