import type { BaseWithValueColDefParams } from "ag-grid-community/dist/lib/entities/colDef";
import type { ICellRendererParams } from "ag-grid-community";

export const twoDecimalPlacesFormatter = (params: BaseWithValueColDefParams): string => {
  return `${Math.round(params.value * 100) / 100}` || "";
};

export const zeroDecimalPlacesFormatterKw = (params: BaseWithValueColDefParams): string => {
  const options = {
    style: "currency",
    currency: "EUR",
  };
  return `${params.value.toLocaleString("de-DE", options)} kW` || "";
};

export const twoDecimalPlacesFormatterKw = (params: BaseWithValueColDefParams): string => {
  return `${Math.round(params.value * 100) / 100} kW` || "";
};

export const twoDecimalPlacesFormatterKwh = (params: BaseWithValueColDefParams): string => {
  return `${Math.round(params.value * 100) / 100} kWh` || "";
};

export const twoDecimalPlacesFormatterWithCurrency = (
  params: BaseWithValueColDefParams,
): string => {
  return `€ ${Math.round(params.value * 100) / 100}` || "";
};

export const dateRenderer = (params: BaseWithValueColDefParams): string => {
  if (params.value) {
    const date = new Date(params.value);
    return date.toLocaleDateString("de-De");
  }
  return "";
};

export const dateTimeRenderer = (params: BaseWithValueColDefParams): string => {
  if (params.value) {
    const date = new Date(params.value);
    return date.toLocaleString("de-De");
  }
  return "";
};

export const monthYearComparator = (valA: string, valB: string): number => {
  if (!valA || !valB) {
    return 0;
  }

  const [monthA, yearA] = valA.split("/").map(Number);
  const [monthB, yearB] = valB.split("/").map(Number);

  if (!yearA || !yearB) {
    return 0; // Oder ein anderer Standard-Rückgabewert, wenn Sie eine solche Situation behandeln möchten
  }

  if (yearA !== yearB) return yearA - yearB;
  return (monthA || 0) - (monthB || 0);
};
export const monthYearRenderer = (params: ICellRendererParams): string => {
  return params.value;
};
