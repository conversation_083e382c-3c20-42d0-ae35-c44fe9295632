import Redis from "ioredis";
import { env } from "../../env.js";

class RedisClient {
  private static instance: RedisClient;
  private client;
  private constructor() {
    this.client = new Redis({
      password: env.REDIS_PASS,
      host: env.REDIS_HOST,
    });
  }
  public static getInstance(): RedisClient {
    if (!RedisClient.instance) {
      RedisClient.instance = new RedisClient();
    }
    return RedisClient.instance;
  }

  async set(key: string, value: any) {
    await this.client.set(key, JSON.stringify({ value, lastupdate: Date.now() }));
    return value;
  }

  async get(key: string, cache_lifetime_min = 0): Promise<any> {
    const cache = await this.client.get(key);
    if (!cache) {
      return false;
    }
    const now = Date.now();
    const data = JSON.parse(cache);
    if (data?.lastupdate && data.lastupdate + cache_lifetime_min * 60 * 1000 <= now) {
      return null;
    }
    return data.value;
  }
}

export default RedisClient;
