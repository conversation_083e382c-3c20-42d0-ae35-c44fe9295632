import nodemailer from "nodemailer";
import { type ZodError } from "zod/lib/ZodError";
import { env } from "../../env.js";

interface Props {
  zodError?: ZodError;
}

export const SendErrorMessage = async (message: string, params?: Props) => {
  void (await sendMessage(`Error: ${message}`, params));
};

export const SendInfoMessage = async (message: string, params?: Props) => {
  void (await sendMessage(`Info: ${message}`, params));
};

export const SendDebugMessage = async (message: string, params?: Props) => {
  if (env.DEBUG == "true") {
    void (await sendMessage(`Debug: ${message}`, params));
  }
};

const sendMessage = async (message: string, params?: Props) => {
  const now = new Date().toISOString();
  message = `${now}\n\n${message}`;

  if (params && params.zodError) {
    message += `${params.zodError.message.replaceAll('"', "'")}`;
  }

  try {
    const res = await fetch(env.CHAT_WEBHOOK, {
      method: "POST",
      body: `payload={"text": "${message}"}`,
    });
    console.log(env.CHAT_WEBHOOK, res, res.status, res.statusText);
  } catch (e) {
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
      secure: true,
    });

    const invoiceMailData = {
      from: env.EMAIL_FROM,
      to: env.NODE_ENV == "production" ? "<EMAIL>" : env.MAIL_FOR_LOGGING_MESSAGES,
      replyTo: `<EMAIL>`,
      bcc: "<EMAIL>",
      subject: `Ad hoc Error on ${env.NODE_ENV}`,
      text: message,
    };

    transporter.sendMail(invoiceMailData, () => {
      return;
    });
  }
};
