import type { CdrWithIsoString } from "~/types/cdr/cdr";

function formatDate(date: Date): string {
  const day = String(date.getUTCDate()).padStart(2, "0");
  const month = String(date.getUTCMonth() + 1).padStart(2, "0"); // months are zero indexed in JavaScript
  const year = date.getUTCFullYear();

  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const seconds = String(date.getUTCSeconds()).padStart(2, "0");

  const timezoneOffset = -date.getTimezoneOffset();
  const sign = timezoneOffset >= 0 ? "+" : "-";
  const timezoneOffsetHours = String(Math.floor(Math.abs(timezoneOffset) / 60)).padStart(2, "0");
  const timezoneOffsetMinutes = String(Math.abs(timezoneOffset) % 60).padStart(2, "0");

  return `${day}.${month}.${year} ${hours}:${minutes}:${seconds} ${sign}${timezoneOffsetHours}:${timezoneOffsetMinutes}`;
}

function isValidDate(d: any): d is Date {
  return Object.prototype.toString.call(d) === "[object Date]" && !isNaN(d);
}

export const convertToIsoString = (obj: any): CdrWithIsoString => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && isValidDate(obj[key])) {
      obj[key] = formatDate(obj[key]);
    }
  }
  return obj as CdrWithIsoString;
};

export const convertNumbersToStrings = (obj: any): any => {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }
  const newObj: any = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === "number") {
        newObj[key] = obj[key].toString().replace(".", ",");
      } else {
        newObj[key] = obj[key];
      }
    }
  }
  return newObj;
};
