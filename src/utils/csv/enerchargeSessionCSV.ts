import fs from "fs";
import { join } from "path";
import { Decimal } from "@prisma/client/runtime/binary";
import { CsvError, parse } from "csv-parse";
import prisma from "~/server/db/prisma";
import { parse as parseDate, format as formatDate } from "date-fns";
import { isNaNValue } from "is-what";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const csvParserCallback = async (error: CsvError | undefined, records: any[]) => {
  if (error) {
    console.error("Error reading CSV file:", error);
    return;
  }

  const columns = [
    "ID",
    "SessionID",
    "KundenID",
    "KundenID2",
    "ChargePointNR",
    "EVSEID",
    "Ladetype",
    "Start_Session",
    "End_Session",
    "StartEnergieinhalt",
    "EndeEnergieinhalt",
    "Energy_content",
    "Parktime",
    "Chargetime",
    "Preis",
    "payment_method_AuswahltId",
    "payment_method_Auswahlt",
    "Billnr_Payment",
    "Billnr_Gesamt",
    "tariff_model",
    "TA_NR",
    "Payment_Kartennummer",
    "Payment_Zahlungstyp",
    "OCPP_TransactionID",
    "Selbstabrechnung",
    "Teilstorno_Versuche",
    "PDF_Update_Finish",
    "Webcode",
    "Session_StatusCode",
    "Session_Status",
    "SessionIDMes",
  ];

  // constructs insert into sql with all cols and for each csv row one values pair
  // matching the cols
  const placeholders: any[] = [];
  const values: any[] = [];

  records.forEach((record, index) => {
    const values: any[] = [];
    columns.forEach((col) => {
      // transform value to match mysql typ
      const value = transformValue(record[col], col);
      values.push(value);
    });

    placeholders.push(`(${values.join(", ")})`);
  });
  if (placeholders.length == 0) {
    return;
  }
  // Construct the SQL query
  const query = `
          INSERT INTO EnerchargeSession (${columns.join(", ")})
          VALUES ${placeholders.join(", ")}
              ON DUPLICATE KEY UPDATE ID = ID;
        `;

  try {
    // Execute the raw query
    await prisma.$executeRawUnsafe(query, ...values);
    console.log("Data imported successfully!");
  } catch (error) {
    console.error("Error inserting data:", error);
  } finally {
    await prisma.$disconnect();
  }
};

export const importEnerchargeSessionCSV = async (csv_file_path: string) => {
  //read csv file
  const fileContent = fs.readFileSync(csv_file_path, { encoding: "utf-8" });
  //use csv parser with callbackfunction to parse the csv
  parse(
    fileContent,
    {
      delimiter: ";",
      columns: true,
      skip_empty_lines: true,
    },
    csvParserCallback, //will create sql to import it to DB "EnerchargeSession"
  );
};

const transformValue = (
  value: string,
  columnName: string,
): Decimal | string | null | number | boolean | bigint => {
  if (value === null || value === undefined) {
    return null;
  }

  // Columns that need to be parsed as integers
  const intColumns = [
    "ID",
    "ChargePointNR",
    "Ladetype",
    "Parktime",
    "Chargetime",
    "payment_method_AuswahltId",
    "Billnr_Payment",
    "Billnr_Gesamt",
    "tariff_model",
    "Teilstorno_Versuche",
    "Session_StatusCode",
  ];

  // Columns that are decimals
  const decimalColumns = ["StartEnergieinhalt", "EndeEnergieinhalt", "Energy_content", "Preis"];

  // Columns that are dates
  const dateColumns = ["Start_Session", "End_Session"];

  // Columns that are booleans
  const booleanColumns = ["Selbstabrechnung", "PDF_Update_Finish"];

  if (intColumns.includes(columnName)) {
    return parseInt(value, 10);
  }

  if (decimalColumns.includes(columnName)) {
    return new Decimal(value);
  }

  if (dateColumns.includes(columnName)) {
    const parsedDate = parseDate(value, "MM/dd/yyyy HH:mm:ss", new Date());
    if (isNaNValue(parsedDate.getTime())) {
      return null;
    }
    const formated = formatDate(parsedDate, "yyyy-MM-dd HH:mm:ss");
    return `'${formated}'`;
  }

  if (booleanColumns.includes(columnName)) {
    return value.toLowerCase() === "true";
  }

  // For all other columns, return the string value
  return `'${value}'`;
};

/**
 * Rekursiver Algorithmus, um alle CSV-Dateien in einem Verzeichnis und dessen Unterverzeichnissen zu finden.
 * @param dir - Das Startverzeichnis.
 * @returns Ein Array der absoluten Pfade aller CSV-Dateien.
 */
export const findAllCsvFiles = (dir: string): string[] => {
  let csvFiles: string[] = [];

  try {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    for (const item of items) {
      const itemPath = join(dir, item.name);

      if (item.isDirectory()) {
        // Rekursiver Aufruf für Unterverzeichnisse
        csvFiles = csvFiles.concat(findAllCsvFiles(itemPath));
      } else if (item.isFile() && item.name.endsWith(".csv")) {
        // Füge CSV-Datei hinzu
        csvFiles.push(itemPath);
      }
    }
  } catch (error) {
    const errorMsg = (error as Error).message;
    Logger(
      `Error reading keba files ${errorMsg}`,
      "Sync Enercharge failed",
      "Finance",
      LogType.ERROR,
    );
  }
  return csvFiles;
};
