import { Cdr, CdrMapping, CdrPayout } from "@prisma/client";
import { convertNumbersToStrings, convertToIsoString } from "~/utils/csv/cdr";

export const generateCSV = (cdrs: Cdr[] | (Cdr & CdrPayout)[], cdrMapping: CdrMapping) => {
  const data = [];

  const csvMapping = cdrMapping;
  if (csvMapping == CdrMapping.EnBW) {
    // Kunde wünscht folgende Felder in der CSV Datei:
    // CDR-ID / CPO Session-ID; OperatorId; EvseId; SessionStart; SessionEnd;
    // Duration [min] Rounded; ConsumedEnergy [kWh]; Total Cost; Currency

    const requiredFields = [
      "ExternalReference", // "CDR-ID / CPO Session-ID"
      "Infra_Provider_ID", // "OperatorId"
      "Charge_Point_ID", // "EvseId"
      "Start_datetime", // "SessionStart"
      "End_datetime", // "SessionEnd"
      "DurationInSec", // "Duration [min] Rounded"
      "Volume", // "ConsumedEnergy [kWh]"
      "Calculated_Cost", // "Total Cost"
      "CostCurrency", // "Currency", will be replaced with "€"
    ];

    const headerMapping = {
      ExternalReference: "CDR-ID / CPO Session-ID",
      Infra_Provider_ID: "OperatorId",
      Charge_Point_ID: "EvseId",
      Start_datetime: "SessionStart",
      End_datetime: "SessionEnd",
      DurationInSec: "Duration [min] Rounded",
      Volume: "ConsumedEnergy [kWh]",
      Calculated_Cost: "Total Cost",
      CostCurrency: "Currency",
    };

    // Header
    data.push(requiredFields.map((field) => (headerMapping as Record<string, string>)[field]));

    // Data
    for (const cdr of cdrs) {
      const formattedCdr = convertNumbersToStrings(convertToIsoString(cdr));
      const row = [];
      for (const field of requiredFields) {
        if (field === "CostCurrency") {
          row.push("€"); // Use "€" directly
        } else if (field === "DurationInSec") {
          row.push(Math.round(formattedCdr[field] / 60)); // Convert seconds to minutes and round
        } else {
          row.push(formattedCdr[field] || "");
        }
      }
      data.push(row);
    }
  } else if (csvMapping == CdrMapping.Standard_Credit) {
    const requiredFields = [
      "CDR_ID", // "CDR-ID / CPO Session-ID"
      "Start_datetime", // "OperatorId"
      "End_datetime", // "EvseId"
      "DurationInSec", // "SessionStart"
      "Volume", // "SessionEnd"
      "Location_ID", // "Total Cost"
      "Service_Provider_ID", // "Currency", will be replaced with "€"
      "Charge_Point_ID",
      "Authentication_ID",
      "Contract_ID",
      "Charge_Point_Address",
      "Charge_Point_ZIP",
      "Charge_Point_City",
      "Charge_Point_Country",
      "MeterStart",
      "MeterStop",
      "ExternalReference",
      "energyPayout",
      "sessionPayout",
      "blockingPayout",
      "payoutSum",
      "stripeFee",
    ];

    const headerMapping = {
      CDR_ID: "CDR_ID",
      Start_datetime: "Start_datetime",
      End_datetime: "End_datetime",
      DurationInSec: "DurationInSec",
      Volume: "Volume kWh",
      Location_ID: "Location_ID",
      Service_Provider_ID: "Service_Provider_ID",
      Charge_Point_ID: "Charge_Point_ID",
      Authentication_ID: "Authentication_ID",
      Contract_ID: "Contract_ID",
      Charge_Point_Address: "Charge_Point_Address",
      Charge_Point_ZIP: "Charge_Point_ZIP",
      Charge_Point_City: "Charge_Point_City",
      Charge_Point_Country: "Charge_Point_Country",
      MeterStart: "MeterStart wh",
      MeterStop: "MeterStop wh",
      ExternalReference: "ExternalReference",
      energyPayout: "Energy Payout €",
      sessionPayout: "Session Payout €",
      blockingPayout: "Blocking Payout €",
      stripeFee: "Gebühr (Stripe / Terminal) €",
      payoutSum: "Payout Sum €",
      transactionId: "OCPP TransactionID",
    };

    // Header
    data.push(requiredFields.map((field) => (headerMapping as Record<string, string>)[field]));

    for (const cdr of cdrs) {
      const formattedCdr = convertNumbersToStrings(convertToIsoString(cdr));
      const row = [];
      for (const field of requiredFields) {
        if (field === "creditPayout") {
          row.push("€"); // Use "€" directly
        } else if (field === "DurationInSec") {
          row.push(Math.round(formattedCdr[field] / 60)); // Convert seconds to minutes and round
        } else if (field === "Volume") {
          row.push(cdr.Volume?.toFixed(2));
        } else {
          row.push(formattedCdr[field] || "");
        }
      }
      data.push(row);
    }
  } else {
    const excludedFields = [
      "invoiceId",
      "tarifId",
      "billable",
      "cost",
      "tarif",
      "Charging_Time_Cost",
      "creditInvoiceId",
      "cpoInvoiceId",
      "Product_Type",
      "Tariff_Type",
      "OBIS_Code",
      "OU_Code",
      "Token_OU_Code",
      "Token_OU_Name",
    ];

    // Header
    data.push(Object.keys(cdrs[0] || {}).filter((key) => !excludedFields.includes(key)));

    // Data
    for (const cdr of cdrs) {
      const formattedCdr = convertNumbersToStrings(convertToIsoString(cdr));
      const row = [];
      for (const key of Object.keys(formattedCdr)) {
        if (!excludedFields.includes(key)) {
          row.push(formattedCdr[key]);
        }
      }
      data.push(row);
    }
  }

  return data.map((d) => d.join(";")).join("\n");
};
