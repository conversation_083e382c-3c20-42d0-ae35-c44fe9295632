import fs from "fs";
import { join } from "path";
import { Decimal } from "@prisma/client/runtime/binary";
import { CsvError, parse } from "csv-parse";
import prisma from "~/server/db/prisma";
import { parse as parseDate, format as formatDate } from "date-fns";
import { isNaNValue } from "is-what";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const csvParserCallback = async (error: CsvError | undefined, records: any[]) => {
  if (error) {
    console.error("Error reading CSV file:", error);
    return;
  }

  const columns = [
    "Zeilenart",
    "Zahlref.-Nr.",
    "VU ZE",
    "Kundennr ZE",
    "Name ZE",
    "PLZ ZE",
    "Ort ZE",
    "Straße ZE",
    "VU",
    "Kundennr VU",
    "Name VU",
    "PLZ VU",
    "Ort VU",
    "Straße VU",
    "MCC Code",
    "<PERSON><PERSON>art",
    "K.Prod Separate Pricing",
    "Transaktion",
    "Terminal-ID",
    "Kaufdatum",
    "Kaufdatum Uhrzeit",
    "Processing Datum",
    "Kartennr.",
    "Währung",
    "Betrag TXN",
    "CashBack Betrag",
    "Anzahl",
    "Text Summe",
    "Summe Serv.Geb.",
    "Summe TXN-Geb.",
    "Händlerentgelt in Zahlwährung",
    "Einzel-IC in Zahlwährung",
    "Summe Interchange",
    "Acq. Serv. Geb",
    "Scheme Fee",
    "Netto PLV TXN",
    "Netto Pre Auth",
    "Netto PLV non TXN",
    "Sicherheitseinbehalt Betrag",
    "SicherheitseinbehaltFälligkeit",
    "Nettobetrag Geb.",
    "Ust. Geb.",
    "Bruttobetrag Geb.",
    "Netto",
    "Auszahlung an Vertragsnr.",
    "Bankname",
    "Konto (IBAN)",
    "BLZ (BIC)",
    "Genehmigungsnr.TXN",
    "DCC TXN Betrag",
    "DCC CurrencyCode",
    "MerchantReference",
    "MessageText",
    "Merchant Voucher No",
    "BSPCode",
    "CustomerOrderNo",
    "OrderInvoiceNumber",
    "PassangerName",
    "MerchantPurchaseRef",
    "TravelAgencyCode",
    "Agency Nr.",
    "TravelAgencyName",
    "Ticket Nr.",
    "Buyer VAT No.",
    "Umrechnungskurs",
  ];

  // constructs insert into sql with all cols and for each csv row one values pair
  // matching the cols
  const placeholders: any[] = [];
  const values: any[] = [];

  records.forEach((record, index) => {
    //if (record["Zeilenart"] !== "A") {
    //  return;
    //}
    if ("Kundennummer ZE" in record) {
      record["Kundennr ZE"] = record["Kundennummer ZE"];
      delete record["Kundennummer ZE"];
    }

    if ("Betrag Transaktion" in record) {
      record["Betrag TXN"] = record["Betrag Transaktion"];
      delete record["Betrag Transaktion"];
    }

    if ("Auszahlung an Vertragsnummer" in record) {
      record["Auszahlung an Vertragsnr."] = record["Auszahlung an Vertragsnummer"];
      delete record["Auszahlung an Vertragsnummer"];
    }

    if ("Kartennummer" in record) {
      record["Kartennr."] = record["Kartennummer"];
      delete record["Kartennummer"];
    }

    const values: any[] = [];
    columns.forEach((col) => {
      let value = transformValue(record[col], col);
      if (value == "" || value == null) {
        value = "''";
      }
      values.push(value);
    });

    placeholders.push(`(${values.join(", ")})`);
  });
  if (placeholders.length == 0) {
    return;
  }
  // Construct the SQL query
  const query = `INSERT INTO PayoneSession (${columns
    .map((item) => `\`${item}\``)
    .join(", ")}) VALUES ${placeholders.join(
    ", ",
  )} ON DUPLICATE KEY UPDATE \`Zahlref.-Nr.\` = \`Zahlref.-Nr.\``;

  try {
    // Execute the raw query
    await prisma.$executeRawUnsafe(query, ...values);
    console.log("Data imported successfully!");
  } catch (error) {
    console.error("Error inserting data:", error);
  } finally {
    await prisma.$disconnect();
  }
};

export const importPayoneCSVFiles = async (csv_file_path: string) => {
  //read csv file
  const fileContent = fs.readFileSync(csv_file_path, { encoding: "utf16le" });
  //use csv parser with callbackfunction to parse the csv
  parse(
    fileContent,
    {
      delimiter: ";",
      columns: true,
      skip_empty_lines: true,
    },
    csvParserCallback, //will create sql to import it to DB "PayoneSession"
  );
};

const transformValue = (
  value: string,
  columnName: string,
): Decimal | string | null | number | boolean | bigint => {
  if (value === null || value === undefined) {
    return null;
  }

  // Columns that are decimals
  const decimalColumns = [
    "Betrag TXN",
    "Händlerentgelt in Zahlwährung",
    "Einzel-IC in Zahlwährung",
  ];

  // Columns that are dates
  const dateColumns = ["Kaufdatum", "Processing Datum"];

  if (decimalColumns.includes(columnName)) {
    if (value == "") {
      return new Decimal("0.0");
    }
    const preparedValue = new Decimal(value.trim().replaceAll(",", "."));
    return preparedValue ?? new Decimal("0.0");
  }

  if (dateColumns.includes(columnName)) {
    if (value == "") {
      return "null";
    }
    const parsedDate = parseDate(value, "dd.MM.yyyy", new Date());

    const formated = formatDate(parsedDate, "yyyy-MM-dd");
    return `'${formated}'`;
  }

  // For all other columns, return the string value
  return `'${value.trim()}'`;
};

/**
 * Rekursiver Algorithmus, um alle CSV-Dateien in einem Verzeichnis und dessen Unterverzeichnissen zu finden.
 * @param dir - Das Startverzeichnis.
 * @returns Ein Array der absoluten Pfade aller CSV-Dateien.
 */
export const findAllCsvFiles = (dir: string): string[] => {
  let csvFiles: string[] = [];

  try {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    for (const item of items) {
      const itemPath = join(dir, item.name);

      if (item.isDirectory()) {
        // Rekursiver Aufruf für Unterverzeichnisse
        csvFiles = csvFiles.concat(findAllCsvFiles(itemPath));
      } else if (item.isFile() && item.name.endsWith(".csv")) {
        // Füge CSV-Datei hinzu
        csvFiles.push(itemPath);
      }
    }
  } catch (error) {
    const errorMsg = (error as Error).message;
    Logger(
      `Error reading payone files ${errorMsg}`,
      "Sync PayOne failed",
      "Finance",
      LogType.ERROR,
    );
  }
  return csvFiles;
};
