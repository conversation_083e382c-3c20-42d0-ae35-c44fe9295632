import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export const getCpoContracts = async () => {
  const session = await getServerSession(authOptions);
  const isAdmin = session?.user?.role == Role.ADMIN;
  if (!session || !isAdmin) {
    return [];
  }
  const cpoContracts = await prisma.cPOContract.findMany({ include: { contact: true } });
  return cpoContracts ?? [];
};
