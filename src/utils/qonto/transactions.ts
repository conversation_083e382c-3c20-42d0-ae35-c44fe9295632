import { env } from "../../env.js";
import prisma from "../../server/db/prisma";

import { z } from "zod";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const QontoTransactionRawSchema = z.object({
  id: z.string(),
  transaction_id: z.string(),
  amount: z.number(),
  amount_cents: z.number(),
  settled_balance: z.number(),
  settled_balance_cents: z.number(),
  local_amount: z.number(),
  local_amount_cents: z.number(),
  side: z.string(),
  operation_type: z.string(),
  currency: z.string(),
  local_currency: z.string(),
  label: z.string(),
  settled_at: z.string(),
  emitted_at: z.string(),
  updated_at: z.string(),
  status: z.string(),
  note: z.string().nullable(),
  reference: z.string().nullable(),
  vat_amount: z.number().nullable(),
  vat_amount_cents: z.number().nullable(),
  vat_rate: z.number().nullable(),
  initiator_id: z.string().nullable(),
  attachment_lost: z.boolean(),
  attachment_required: z.boolean(),
  card_last_digits: z.string().nullable(),
  category: z.string(),
  label_ids: z.array(z.string()).nullable(),
  attachment_ids: z.array(z.string()).nullable(),
  transfer: z.object({ counterparty_account_number: z.string() }).optional(),
  income: z.object({ counterparty_account_number: z.string() }).optional(),
});
const QontoTransactionArraySchema = z.object({
  transactions: z.array(QontoTransactionRawSchema),
  meta: z.object({
    current_page: z.number(),
    next_page: z.number().nullable(),
    prev_page: z.number().nullable(),
    total_pages: z.number(),
    total_count: z.number(),
    per_page: z.number(),
  }),
});

interface QontoTransactionRaw {
  id: string;
  transaction_id: string;
  amount: number;
  amount_cents: number;
  settled_balance: number;
  settled_balance_cents: number;
  local_amount: number;
  local_amount_cents: number;
  side: string;
  operation_type: string;
  currency: string;
  local_currency: string;
  label: string;
  settled_at: Date;
  emitted_at: Date;
  updated_at: Date;
  status: string;
  note: string;
  reference: string;
  vat_amount?: number;
  vat_amount_cents?: number;
  vat_rate?: number;
  initiator_id?: string;
  attachment_lost: boolean;
  attachment_required: boolean;
  card_last_digits?: string;
  category: string;
  label_ids?: string[];
  attachment_ids?: string[];
  iban?: string;
}

// Funktion zum Filtern der Eigenschaften
function extractQontoProperties(data: any[]) {
  return data.map((obj) => {
    let iban;
    if (obj?.transfer?.counterparty_account_number) {
      iban = obj?.transfer?.counterparty_account_number;
    }
    if (!iban && obj?.income?.counterparty_account_number) {
      iban = obj?.income?.counterparty_account_number;
    }

    const extractedObj: QontoTransactionRaw = {
      id: obj.id,
      transaction_id: obj.transaction_id,
      amount: obj.amount,
      amount_cents: obj.amount_cents,
      settled_balance: obj.settled_balance,
      settled_balance_cents: obj.settled_balance_cents,
      local_amount: obj.local_amount,
      local_amount_cents: obj.local_amount_cents,
      side: obj.side,
      operation_type: obj.operation_type,
      currency: obj.currency,
      local_currency: obj.local_currency,
      label: obj.label,
      settled_at: obj.settled_at,
      emitted_at: obj.emitted_at,
      updated_at: obj.updated_at,
      status: obj.status,
      note: obj.note !== null ? obj.note : undefined,
      reference: obj.reference,
      vat_amount: obj.vat_amount !== null ? obj.vat_amount : undefined,
      vat_amount_cents: obj.vat_amount_cents !== null ? obj.vat_amount_cents : undefined,
      vat_rate: obj.vat_rate !== null ? obj.vat_rate : undefined,
      initiator_id: obj.initiator_id !== null ? obj.initiator_id : undefined,
      attachment_lost: obj.attachment_lost,
      attachment_required: obj.attachment_required,
      card_last_digits: obj.card_last_digits !== null ? obj.card_last_digits : undefined,
      category: obj.category,
      label_ids: undefined,
      attachment_ids: undefined,
      iban: iban,
    };
    return extractedObj;
  });
}

export const fetchStripeTransactionsFromQonto = async (): Promise<boolean> => {
  return await fetchTransactions(env.QONTO_STRIPE_IBAN);
};

export const fetchMainTransactionsFromQonto = async (): Promise<boolean> => {
  return await fetchTransactions(env.QONTO_MAIN_IBAN);
};
const fetchTransactions = async (iban: string, target_page = 1): Promise<boolean> => {
  const url = `${env.QONTO_API_URL}/transactions?iban=${iban}&page=${target_page}`;
  const options = {
    method: "GET",
    next: { revalidate: 0 },
    headers: {
      "cache-control": "no-store",
      Accept: "application/json",
      Authorization: `${env.QONTO_LOGIN}:${env.QONTO_SECRET}`,
    },
  };
  const response = await fetch(url, options);
  let data;
  const raw = await response.json();
  try {
    data = QontoTransactionArraySchema.safeParse(raw);
    if (!data.success) {
      Logger(
        "Zod error when fetching QontoTransaction",
        "Faild to fetch Qonto Transactions",
        "Finance",
        LogType.ERROR,
      );
      return false;
    }
    const rawTransactionsFromQonto = data.data.transactions;
    const transactionsFromQonto = extractQontoProperties(rawTransactionsFromQonto);
    const transactionsForDB = transactionsFromQonto.map((qontoTransaction: QontoTransactionRaw) => {
      return prisma?.qontoTransaction.upsert({
        where: { transaction_id: qontoTransaction.transaction_id },
        update: { ...qontoTransaction },
        create: { ...qontoTransaction },
      });
    });
    Logger(
      `Received ${transactionsForDB.length} QontoTransactions`,
      "Fetched Qonto Transactions",
      "Finance",
      LogType.INFO,
    );
    // execute in one transaction. (There is no upsertMany...)
    const prismaUpsert = await prisma?.$transaction(transactionsForDB);
  } catch (error) {
    Logger(
      "Prisma error fetching QontoTransaction",
      "Faild to fetch Qonto Transactions",
      "Finance",
      LogType.ERROR,
    );
    console.error(error);
    return false;
  }
  if (data?.success) {
    if (data.data.meta.current_page != data.data.meta.total_pages && data.data.meta.next_page) {
      return await fetchTransactions(iban, data.data.meta.next_page);
    }
  }
  return true;
};
