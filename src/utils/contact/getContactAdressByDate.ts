import type { ContactAddress } from "@prisma/client";

export const getContactAdressByDate = (
  contactAddress: ContactAddress[],
  dateToSearch: Date,
): ContactAddress | undefined => {
  dateToSearch = new Date(dateToSearch);
  const validAdress = contactAddress.find((adress: ContactAddress) => {
    return new Date(adress.validFrom) <= dateToSearch && new Date(adress.validTo) >= dateToSearch;
  });
  if (validAdress) {
    return validAdress;
  }
  console.warn("No address found");
  return undefined;
  // Logger('No valid Emp Adress found', 'Emp Adress not found', 'invoice', LogType.WARN)
};
