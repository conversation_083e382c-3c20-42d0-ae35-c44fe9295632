export const formatEuro = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(0) + "m €";
  } else if (value >= 1000) {
    return (value / 1000).toFixed(0) + "k €";
  } else {
    return value.toFixed(0) + " €";
  }
};

export const formatKwh = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(3) + " GWh";
  } else if (value >= 1000) {
    return (value / 1000).toFixed(0) + " MWh";
  } else {
    return value.toFixed(0) + " kWh";
  }
};

export const convertFloatToTime = (floatHours: number): string => {
  // Stunden extrahieren
  const hours = Math.floor(floatHours);
  // Minuten extrahieren, indem der Stundenanteil subtrahiert und das Ergebnis mit 60 multipliziert wird
  const minutes = Math.floor((floatHours - hours) * 60);
  // Sekunden extrahieren, ähnlich wie bei Minuten, jedoch unter Berücksichtigung der bereits berechneten Minuten
  const seconds = Math.round(((floatHours - hours) * 60 - minutes) * 60);

  // Führende Nullen hinzufügen, falls notwendig
  const paddedHours = hours.toString().padStart(2, "0");
  const paddedMinutes = minutes.toString().padStart(2, "0");
  const paddedSeconds = seconds.toString().padStart(2, "0");

  return `${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
};
