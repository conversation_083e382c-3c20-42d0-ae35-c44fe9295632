import { timestamp } from "rxjs";

export async function getOcppMessagesFromToday() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const messages = await prisma.oCPPErrorStatusMessage.findMany({
    where: {
      timestamp: {
        gte: today,
      },
    },
  });
  return messages;
}

import prisma from "~/server/db/prisma";
import { ChargePointError } from "@prisma/client";

export async function getChargepointErrors() {
  const errors = await prisma.$queryRawUnsafe(`
        SELECT cpe.chargePointId,
               cpe.error
        FROM ChargePointError cpe
        WHERE cpe.createdAt = (SELECT MAX(createdAt) FROM ChargePointError)
          and error not like 'eth interface had 5 link%';
    `);

  return errors;
}
