import prisma from "~/server/db/prisma";
import { NotificationType } from "@prisma/client";

export interface CreateSystemNotificationParams {
  nachricht: string;
  type?: NotificationType;
  userId?: string;
  userIds?: string[];
}

/**
 * Creates a system notification for one or multiple users
 */
export async function createSystemNotification({
  nachricht,
  type = NotificationType.INFO,
  userId,
  userIds,
}: CreateSystemNotificationParams) {
  try {
    // If specific user IDs are provided, use them
    const targetUserIds = userIds || (userId ? [userId] : []);
    
    if (targetUserIds.length === 0) {
      throw new Error("At least one user ID must be provided");
    }

    // Create notifications for all target users
    const notifications = await prisma.systemNotification.createMany({
      data: targetUserIds.map(id => ({
        nachricht,
        type,
        userId: id,
      })),
    });

    return notifications;
  } catch (error) {
    console.error("Error creating system notification:", error);
    throw error;
  }
}

/**
 * Creates a system notification for all users in a specific OU
 */
export async function createSystemNotificationForOU({
  nachricht,
  type = NotificationType.INFO,
  ouId,
}: {
  nachricht: string;
  type?: NotificationType;
  ouId: string;
}) {
  try {
    // Get all users in the OU
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { ouId: ouId },
          { selectedOuId: ouId },
        ],
      },
      select: { id: true },
    });

    if (users.length === 0) {
      console.warn(`No users found for OU ${ouId}`);
      return { count: 0 };
    }

    const userIds = users.map(user => user.id);
    return await createSystemNotification({
      nachricht,
      type,
      userIds,
    });
  } catch (error) {
    console.error("Error creating system notification for OU:", error);
    throw error;
  }
}

/**
 * Creates a system notification for all admin users
 */
export async function createSystemNotificationForAdmins({
  nachricht,
  type = NotificationType.INFO,
}: {
  nachricht: string;
  type?: NotificationType;
}) {
  try {
    // Get all admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        role: "ADMIN",
      },
      select: { id: true },
    });

    if (adminUsers.length === 0) {
      console.warn("No admin users found");
      return { count: 0 };
    }

    const userIds = adminUsers.map(user => user.id);
    return await createSystemNotification({
      nachricht,
      type,
      userIds,
    });
  } catch (error) {
    console.error("Error creating system notification for admins:", error);
    throw error;
  }
}
