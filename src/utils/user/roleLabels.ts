import { Role } from "@prisma/client";

export const getRoleLabel = (role: Role) => {
  switch (role) {
    case Role.ADMIN:
      return "Admin";
    case Role.USER:
      return "Standardbenutzer";
    case Role.CARD_HOLDER:
      return "Nutzer mit Ladekarte";
    case Role.CARD_MANAGER:
      return "Manager";
    case Role.CPO:
      return "Betreiber";
    default:
      return "Benutzer";
  }
};
