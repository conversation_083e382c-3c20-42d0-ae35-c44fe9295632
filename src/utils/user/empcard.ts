import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { pushEmpCard } from "~/utils/longship";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { NotificationType } from "@prisma/client";

export const activateCard = async (cardId: string, cardNumber: string) => {
  // Get PhysicalCard uid by its number
  const physicalCard = await prisma?.physicalCard.findUnique({
    where: {
      visualNumber: cardNumber,
    },
    include: {
      EMPCard: true,
    },
  });

  if (physicalCard && (!physicalCard.EMPCard || !physicalCard?.EMPCard?.activatedAt)) {
    // Update EMPCard to link with PhysicalCard
    const updatedEmpCard = await prisma?.eMPCard.update({
      where: {
        id: cardId,
      },
      include: {
        tarifs: { include: { tarif: { include: { ou: true } } } },
        physicalCard: true,
        user: true,
        contact: true
      },
      data: {
        physicalCardId: physicalCard.uid,
        active: true,
        activatedAt: new Date(),
      },
    });
    if (updatedEmpCard?.physicalCard?.uid) {
      const updatedPhysical = await prisma?.physicalCard.update({
        where: {
          uid: updatedEmpCard.physicalCard.uid,
        },

        data: {
          valid: true,
        },
      });
    }
    if (updatedEmpCard) {
      // Create admin notification for card activation
      const cardOwner = updatedEmpCard.user
        ? `${updatedEmpCard.user.name} ${updatedEmpCard.user.lastName}`
        : updatedEmpCard.contact?.companyName || updatedEmpCard.contact?.name || "Unbekannt";

      await createSystemNotificationForAdmins({
        nachricht: `Ladekarte aktiviert für ${cardOwner} (Kartennummer: ${cardNumber})`,
        type: NotificationType.INFO,
      });

      return await pushEmpCard(updatedEmpCard);
    }
  }
  return ["Karte nicht gefunden"];
};

export const getEMPCardForUser = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return [];
  }

  const id = session?.user.id;
  const empcards = await prisma.eMPCard.findMany({
    where: {
      userId: id,
    },
    include: { physicalCard: true, tarifs: { include: { tarif: true } } },
  });
  return empcards;
};
