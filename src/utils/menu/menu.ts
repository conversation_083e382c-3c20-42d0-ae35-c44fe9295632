import { MenuItem } from "~/utils/menu/menuDef";

export const findMenuItemByPathname = (pathname: string, visibleMenuItems: MenuItem[]) => {
  let menuEntry = visibleMenuItems?.find((entry) => entry?.href === pathname);
  if (!menuEntry) {
    for (const item of visibleMenuItems) {
      if (pathname.startsWith(item.href || "")) {
        // Prü<PERSON>, ob das aktuelle href des Items länger ist als das bisher gefundene,
        // um den besten Treffer zu erhalten
        if (!menuEntry || (item.href && item.href.length > (menuEntry.href || "").length)) {
          menuEntry = item;
        }
      }
    }
  }
  return menuEntry;
};
