import prisma from "~/server/db/prisma";
import { ouColors, ouLogos } from "~/styles/oucolors/oucolors";

export const getCPOOu = async (registrationSlug: string) => {
  const ou = await prisma.ou.findUnique({
    where: {
      registrationSlug: registrationSlug,
    },
    include: {
      Contact: true,
    },
  });
  if (!ou || !ou?.Contact?.cpo) {
    throw new Error("Company not found");
  }
  return ou;
};

export const getBackgroundColor = (ouname?: string) => {
  if (!ouname) {
    return "";
  }

  if (ouColors[ouname]) {
    const oucol = ouColors[ouname];
    if (oucol) {
      return oucol;
    }
  }
  const oucol = ouColors.Default;
  if (oucol) {
    return oucol;
  }
  return "";
};

export const getCompanyLogo = (ouname: string): string => {
  const defaultLogo = ouLogos?.Default?.logo ?? "";
  if (Object.keys(ouLogos).includes(ouname)) {
    return ouLogos[ouname]?.logo ?? defaultLogo;
  }
  return defaultLogo;
};
