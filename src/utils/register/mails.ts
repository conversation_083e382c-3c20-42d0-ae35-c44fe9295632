import { env } from "~/env";
import nodemailer from "nodemailer";
import { ouColors } from "~/styles/oucolors/oucolors";
import type { UserWithOu } from "~/types/prisma/user";
const getBackgroundColor = (label: string) => {
  if (ouColors[label]) {
    const oucol = ouColors[label];
    if (oucol) {
      return oucol["--color-primary"];
    }
  }
  const oucol = ouColors.Default;
  if (oucol) {
    return oucol["--color-primary"];
  }
  return "";
};

export const sendVerifyEmailMail = async (user: UserWithOu) => {
  const primColor = getBackgroundColor(user.ou.name);
  const htmlContent = `
      <!DOCTYPE html> <html lang="de">
        <head>
            <title>Zugang zum Ladeportal</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                 .container { background-color: #DBDBDBFF; padding: 20px; border-radius: 10px }
                h1 { color: ${primColor}; }
                .customlink {text-decoration: none; font-size: large; padding:4px; border: 1px solid; border-radius:5px; background-color: ${primColor}; color:black }
              
                a {font-size: large; ${primColor}; }
            </style>
        </head>
        <body >
            <div class="container">
                <h1>Willkommen beim Ladeportal!</h1>
                <p>Hallo ${user.name} ${user.lastName},</p>
                <p>Zum Abschluss der Registrierung bestätige bitte deine E-Mail Adresse über folgenden Link:</p>
                 <a class="customlink" href="${env.NEXT_PUBLIC_SITE_URL}/api/user/verify-email?signupHash=${user.signUpHash}">Hier klicken zum Bestätigen</a>
                 <p>Weitere Details zum Ablauf erfährst du nach Abschluss der Registrierung.</p>
                 <p>Viel Spaß beim Nutzen unseres Portals!</p>
                <p>Beste Grüße <br>Dein Team von Eulektro</p>
           
            
           
                Eulektro GmbH <br>
                Werderstraße 69 <br>
                28199 Bremen&nbsp; <br>
                +49 421 17512890<br>
                <a href="https://www.eulektro.de" class="">www.eulektro.de</a><br
                <a href="mailto:<EMAIL>" class=""><EMAIL></a><br
                Registergericht: Amtsgericht Bremen<br>
                Registernummer: HRB 36822 HB<br>
                Geschäftsführer: Jan Runkel - Jan Kahrs
            </div>
        </body>
        </html>`;
  // Create transporter
  const transporter = nodemailer.createTransport({
    port: 465,
    host: env.EMAIL_SERVER_HOST,
    auth: {
      user: env.EMAIL_SERVER_USER,
      pass: env.EMAIL_SERVER_PASSWORD,
    },
  });

  // Send email
  await transporter.sendMail({
    from: "<EMAIL>",
    to: user.email,
    subject: "Ladeportal Registrierung abschließen",
    html: htmlContent,
  });
};
