import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env";
import { getFirstDayOfLastMonth, getLastDayOfLastMonth } from "~/utils/date/date";
import prisma from "~/server/db/prisma";
import { LogType, Prisma } from "@prisma/client";
import Logger from "~/server/logger/logger";

/**
 * Will use the direct token of each CPO contract in order to get the transaction id of the session.
 * The transactionId is necessary to match the local enercharge session to longship CDR
 * Only with this mapping we know how much a direct payment cdr costs
 * The transactionId will be used in order to identifiy the enercharge session when credit and invoice
 * are generated
 */
export const fetchTransactionIDsFromSessionsByDirectPaymentTokenOfCPOContract = async (
  startdate = "",
  enddate = "",
) => {
  let start = getFirstDayOfLastMonth();
  let end = getLastDayOfLastMonth();
  if (startdate && enddate) {
    start = new Date(startdate);
    end = new Date(enddate);
  }
  const headers = LongshipHeaders({});
  const contracts = await prisma?.cPOContract.findMany();
  const cpContractsIds = contracts
    ?.map((contract) => contract.directPaymentContractId)
    .filter((id) => id != "none" && id);

  const sessions = [];
  if (cpContractsIds) {
    for (const contractId of cpContractsIds) {
      const url = new URL(`sessions`, env.LONGSHIP_API);
      url.searchParams.set("contractid", contractId);
      url.searchParams.set("from", start.toISOString());
      url.searchParams.set("to", end.toISOString());

      // ToDo find a way to request more session at once
      const response = await fetch(url, {
        method: "GET",
        headers: headers,
      });
      const sessionsOfAuthid = await response.json();
      sessions.push(...sessionsOfAuthid);
    }
  }
  const cdrid_transactionid = sessions?.map((session) => {
    return { cdrId: session?.id, transactionId: session?.transactionId };
  });

  try {
    for (const { cdrId, transactionId } of cdrid_transactionid) {
      await prisma.cdr.update({
        where: { CDR_ID: cdrId },
        data: { transactionId: transactionId?.toString() },
      });
    }
    return `${cdrid_transactionid?.length} updated`;
  } catch (e) {
    if (e instanceof Prisma.PrismaClientKnownRequestError) {
      Logger(
        `Prisma Update Transaction failed ${e.message}`,
        "TransactionId Sync Prisma Error",
        "Direct Payment Sync",
        LogType.ERROR,
      );
    } else {
      Logger(
        `Prisma Update Transaction failed`,
        "TransactionId Sync Prisma Error",
        "Direct Payment Sync",
        LogType.ERROR,
      );
    }
  }
};
