// import { prisma } from "../../../server/db/client";
// export const getUser = async (username: string, password: string) => {
export const getUser = async () => {
  // const result = await prisma.credentials.findUnique({
  //     where: {userName: userName, password: password},
  //     select: {
  //         userName: true,
  //     }
  // })
  // if (result) {
  //     return result;
  // }
  // }
  return null;
};
