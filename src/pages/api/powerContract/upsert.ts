import { type NextApiRequest, type NextApiResponse } from "next";

import prisma from "../../../server/db/prisma";

const upsert = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.body) {
    const { data: data } = req.body;

    if (!data.end) {
      data.end = "2100-12-31";
    }

    if (data.id) {
      await prisma.powerContract.update({
        where: {
          id: data?.id || 0,
        },
        data: {
          ...data,
          basePrice: parseFloat(data.basePrice) || 0,
          kwhPrice: parseFloat(data.kwhPrice) || 0,
          start: new Date(data.start),
          end: new Date(data.end),
        },
      });
    } else {
      await prisma.powerContract.create({
        data: {
          ...data,
          basePrice: parseFloat(data.basePrice) || 0,
          kwhPrice: parseFloat(data.kwhPrice) || 0,
          start: new Date(data.start),
          end: new Date(data.end),
          locationId: data.locationId,
        },
      });
    }

    res.status(200).json(data);
  } else {
    res.status(500).json("");
  }
};

export default upsert;
