import { type NextApiRequest, type NextApiResponse } from "next";
import { env } from "../../../env.js";

const index = async (req: NextApiRequest, res: NextApiResponse) => {
  const hostname = env.NEXT_PUBLIC_SITE_URL;
  await fetch(`${hostname}/api/charger/import`);
  await fetch(`${hostname}/api/longship/fetchLocation`);
  await fetch(`${hostname}/api/longship/copyou`);
  await fetch(`${hostname}/api/longship/fetchCdr?copy_mode=full`);
  await fetch(`${hostname}/api/voltego?copy_mode=full`);
  await fetch(`${hostname}/api/cdr/cost/calc`);

  res.status(200).json("ok");
};

export default index;
