import { type NextApiRequest, type NextApiResponse } from "next";
import { getAllData } from "~/utils/longship";
import prisma from "~/server/db/prisma";
import { endpoints } from "~/server/api/longship/constants";

const longshipOu = async (req: NextApiRequest, res: NextApiResponse) => {
  const ou = await getAllData(endpoints.ORGANIZATIONAL_UNITS);

  await prisma.ou.createMany({
    data: ou,
    skipDuplicates: true,
  });

  res.status(200).json("success");
};

export default longshipOu;
