import type { NextApiRequest, NextApiResponse } from "next";
import { getSession } from "next-auth/react";

import prisma from "../../../server/db/prisma";

function calculateVolumePerDayPerServiceProviderArray(data: any[]) {
  const result = [];
  for (const record of data) {
    const endDate = new Date(record.End_datetime);
    const endDateKey = endDate.toDateString();
    const serviceProvider = record.Service_Provider_ID;
    let dateData: any = result.find(
      (item) => item.date === endDateKey && item.serviceProvider === serviceProvider,
    );
    if (!dateData) {
      dateData = {
        date: endDateKey,
        contact: serviceProvider,
        kwh: 0,
        count: 0,
      };
      result.push(dateData);
    }
    dateData.kwh += record.Volume;
    dateData.count += 1;
  }
  return result;
}

export default async function handle(req: NextApiRequest, res: NextApiResponse) {
  const session = await getSession({ req });
  // if (!session) {
  //   res.send({
  //     error: 'You must be sign in to view the protected content on this page.',
  //   });
  //   return;
  // }

  const cdrs = await prisma.cdr.findMany({
    where: {},
  });

  const msp = calculateVolumePerDayPerServiceProviderArray(cdrs);

  res.status(200).json({
    msp,
  });
}
