import type { NextApiRequest, NextApiResponse } from "next";
import { getSession } from "next-auth/react";
import { env } from "../../../env.js";
import { getAllData } from "../../../utils/longship";
import { endpoints } from "../../../server/api/longship/constants";

export interface MspProps {
  [key: string]: {
    sessions: number;
    kwh: number;
  };
}

export interface KwhProps {
  location: {
    [key: string]: number;
  };
  evse: {
    [key: string]: number;
  };
}

// POST /api/user
// Required fields in body: name, email
export default async function handle(req: NextApiRequest, res: NextApiResponse) {
  const session = await getSession({ req });
  // if (!session) {
  //   res.send({
  //     error: 'You must be sign in to view the protected content on this page.',
  //   });
  //   return;
  // }
  const { ou } = req.body;

  const yesterday_start = new Date();
  yesterday_start.setDate(yesterday_start.getDate() - 1);
  yesterday_start.setHours(0, 0, 0, 0);

  const yesterday_end = new Date();
  yesterday_end.setDate(yesterday_end.getDate() - 1);
  yesterday_end.setHours(23, 59, 59, 0);

  const today_end = new Date();
  today_end.setHours(23, 59, 59, 0);

  const params = {
    from: yesterday_start.toISOString(),
    to: today_end.toISOString(),
  };

  const cdr_today = await getAllData(endpoints.CDRS, { params });

  let yesterday_cdr = 0;
  let yesterday_invalid_cdr = 0;
  let today_cdr = 0;
  let today_invalid_cdr = 0;
  let yesterday_valid_kWh = 0;
  let yesterday_invalid_kWh = 0;
  let today_valid_kWh = 0;
  let today_invalid_kWh = 0;
  let yesterday_msp: MspProps = {};
  let today_msp: MspProps = {};
  let today_kWh_by_evse: KwhProps = {
    evse: {},
    location: {},
  };
  Object.entries(cdr_today).map(([key, value], i) => {
    const cdr: any = value;
    const providerPartyId = cdr?.startedByInfo?.tokenInfo?.providerPartyId || "Unknown";
    if (
      new Date(cdr.endDateTime).getTime() > yesterday_start.getTime() &&
      new Date(cdr.endDateTime).getTime() < yesterday_end.getTime()
    ) {
      if (cdr.totalEnergyInKwh > 0.2) {
        yesterday_cdr++;
        yesterday_valid_kWh += cdr.totalEnergyInKwh;
      } else {
        yesterday_invalid_cdr++;
        yesterday_invalid_kWh += cdr.totalEnergyInKwh;
      }
      if (yesterday_msp?.providerPartyId) {
        yesterday_msp = {
          ...yesterday_msp,
          [providerPartyId]: {
            sessions: yesterday_msp.providerPartyId.sessions + 1,
            kwh: yesterday_msp.providerPartyId.kwh + cdr.totalEnergyInKwh,
          },
        };
      } else {
        yesterday_msp = {
          ...yesterday_msp,
          [providerPartyId]: {
            sessions: 1,
            kwh: cdr.totalEnergyInKwh,
          },
        };
      }
    }

    if (
      new Date(cdr.endDateTime).getTime() > yesterday_end.getTime() &&
      new Date(cdr.endDateTime).getTime() < today_end.getTime()
    ) {
      if (cdr.totalEnergyInKwh > 0.2) {
        today_cdr++;
        today_valid_kWh += cdr.totalEnergyInKwh;
      } else {
        today_invalid_cdr++;
        today_invalid_kWh += cdr.totalEnergyInKwh;
      }
      if (today_msp.providerPartyId) {
        today_msp = {
          ...today_msp,
          [providerPartyId]: {
            sessions: today_msp.providerPartyId.sessions + 1,
            kwh: today_msp.providerPartyId.kwh + cdr.totalEnergyInKwh,
          },
        };
      } else {
        today_msp = {
          ...today_msp,
          [providerPartyId]: {
            sessions: 1,
            kwh: cdr.totalEnergyInKwh,
          },
        };
      }

      if (cdr.totalEnergyInKwh !== null) {
        if (cdr.chargePointId in today_kWh_by_evse.evse) {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            evse: {
              ...today_kWh_by_evse.evse,
              [cdr.chargePointId]: today_kWh_by_evse.evse[cdr.chargePointId] + cdr.totalEnergyInKwh,
            },
          };
        } else {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            evse: {
              ...today_kWh_by_evse.evse,
              [cdr.chargePointId]: cdr.totalEnergyInKwh,
            },
          };
        }
        if (cdr.cdrLocation.id in today_kWh_by_evse.location) {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            location: {
              ...today_kWh_by_evse.location,
              [cdr.cdrLocation.id]:
                today_kWh_by_evse.location[cdr?.cdrLocation?.id] + cdr.totalEnergyInKwh,
            },
          };
        } else {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            location: {
              ...today_kWh_by_evse.location,
              [cdr.cdrLocation.id]: cdr.totalEnergyInKwh,
            },
          };
        }
      }
    }
  });

  res.status(200).json({
    yesterday_valid_kWh,
    yesterday_invalid_kWh,
    yesterday_cdr,
    yesterday_invalid_cdr,
    today_cdr,
    yesterday_msp,
    today_msp,
    today_invalid_cdr,
    today_valid_kWh,
    today_invalid_kWh,
    today_kWh_by_evse,
  });
}
