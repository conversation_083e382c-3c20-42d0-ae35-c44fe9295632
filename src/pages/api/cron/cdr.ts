import { <PERSON>ronJob } from "quirrel/next";
import { importCdr, COPY_MODE } from "~/server/task/importCdr";
import { checkAdhocPaymentIntents } from "~/utils/monitoring/chargepointErrors";

export default CronJob(
  "api/cron/cdr", // 👈 the route that it's reachable on
  "*/5 * * * *", // same as @monthly (see https://crontab.guru/)
  async () => {
    await importCdr(COPY_MODE.DELTA);
    await checkAdhocPaymentIntents();
  },
);
