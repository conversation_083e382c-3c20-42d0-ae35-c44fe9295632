import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next";
import prisma from "../../../server/db/prisma";
import { ComputeEnergyCostForCdrs } from "../../../server/task/cost";
import Logger from "../../../server/logger/logger";
import { LogType } from "@prisma/client";

export default CronJob(
  "api/cron/cost", // 👈 the route that it's reachable on
  "*/5 * * * *", // same as @monthly (see https://crontab.guru/)
  async () => {
    Logger("Start task", "Start calc CDR Cost", "cron", LogType.DEBUG);
    const cdrs = await prisma.cdr.findMany({
      where: {
        cost: null,
        End_datetime: {
          gt: new Date("09.14.2022"),
        },
      },
    });
    if (cdrs) {
      await ComputeEnergyCostForCdrs(cdrs);
    }
  },
);
