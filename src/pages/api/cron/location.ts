import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next";
import getLocation from "../../../server/task/location";
import Logger from "../../../server/logger/logger";
import { LogType } from "@prisma/client";

export default CronJob(
  "api/cron/location", // 👈 the route that it's reachable on
  "0 * * * *", // same as @monthly (see https://crontab.guru/)
  async () => {
    Logger("Import Locations from Longship", "Start Location import", "cron", LogType.DEBUG);
    await getLocation();
  },
);
