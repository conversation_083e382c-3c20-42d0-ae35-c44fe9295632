import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next";
import sessionData from "../../../server/task/sessionData";
import Logger from "../../../server/logger/logger";
import { LogType } from "@prisma/client";

export default CronJob(
  "api/cron/sessionData", // 👈 the route that it's reachable on
  "*/5 * * * *", // same as @monthly (see https://crontab.guru/)
  async () => {
    Logger(
      "fetch all running session data form Longship",
      "Fetch sessionData from Longship",
      "cron",
      LogType.DEBUG,
    );
    const result = await sessionData();
  },
);
