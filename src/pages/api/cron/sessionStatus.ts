import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next";
import getChargepointstatus from "../../../server/task/chargepointstatus";
import Logger from "../../../server/logger/logger";
import { LogType } from "@prisma/client";

export default CronJob(
  "api/cron/sessionStatus", // 👈 the route that it's reachable on
  "*/5 * * * *", // same as @monthly (see https://crontab.guru/)
  async () => {
    Logger(
      "fetch all running session status data form Longship",
      "Fetch sessionStatus from Longship",
      "cron",
      LogType.DEBUG,
    );

    const result = await getChargepointstatus();
  },
);
