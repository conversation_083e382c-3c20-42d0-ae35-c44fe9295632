import { type NextApiRequest, type NextApiResponse } from "next";
import RedisClient from "../../../utils/redis/redisClient";

const chargepointstatus = async (req: NextApiRequest, res: NextApiResponse) => {
  const redisClient = RedisClient.getInstance();
  const result = await redisClient.get("chargepointstatus");

  if (result) {
    res.status(200).json(result);
  } else {
    res.status(200).json("empty");
  }
};

export default chargepointstatus;
