import prisma from "~/server/db/prisma";

import type { NextApiResponse } from "next";
import type { NextApiRequest } from "next";

export const revalidate = 30; // revalidate this page every 60 seconds

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { start, end } = JSON.parse(req.body);
    const result = await prisma.historyPowerByOu.findMany({
      where: {
        timestamp: { gt: new Date(start) },
      },
      orderBy: {
        timestamp: "asc",
      },
      select: {
        timestamp: true,
        power: true,
      },
    });

    const stockDate = new Date(start);
    stockDate.setDate(stockDate.getDate() + 1);
    stockDate.setHours(24, 59, 59);
    const stock_result = await prisma.energyStockPrice.findMany({
      where: {
        timestamp: { gt: stockDate },
      },
      orderBy: {
        timestamp: "asc",
      },
    });

    const data = {
      power: result.map((x) => {
        return [x.timestamp.getTime(), x.power / 1000];
      }),
      price: stock_result.map((x) => {
        return [x.timestamp.getTime(), x.amount];
      }),
    };
    res.status(200).json(data);
  } catch (e) {
    res.status(500).json({ message: "missing minDate" });
  }
}
