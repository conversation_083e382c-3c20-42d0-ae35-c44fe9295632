"use client";
import React, { useEffect, useState } from "react";
import Headline from "~/component/Headline";
import Loading from "~/app/(app)/loading";

import { Contact } from "@prisma/client";
import { CompanyPaymentWrapper } from "~/app/(sepaSetup)/createCompanySepa/CompanyPaymentWrapper";
import Image from "next/image";
import { ouLogos } from "~/styles/oucolors/oucolors";
import { ContactsWithAddress } from "~/types/prisma/contact";
import { FaTriangleExclamation } from "react-icons/fa6";

interface Props {
  searchParams: {
    sepaHash?: string;
  };
}
const Page = (props: Props) => {
  const [clientSecret, setClientSecret] = useState<string>();
  const [contact, setContact] = useState<ContactsWithAddress>();
  const sepaHash = props?.searchParams?.sepaHash;
  const [setupError, setSetupError] = useState<string>("");

  const createSetupIntent = async () => {
    const result = await fetch(`/api/stripe/setupCompanySepaDebit?sepaHash=${sepaHash}`, {
      method: "GET",
    });
    if (result.ok) {
      const { clientSecret, contact } = await result.json();
      setClientSecret(clientSecret);
      setContact(contact);
    } else {
      const data = await result.json();
      setSetupError(data?.message ?? "No specific error message from server");
    }
  };
  useEffect(() => {
    void createSetupIntent();
  }, []);

  return (
    <>
      <div className={"flex flex-col items-center bg-stone-200"}>
        {!sepaHash && (
          <div className={"flex flex-col gap-5 rounded-xl bg-clip-border p-4 shadow-2xl"}>
            <span>Ungültiger Einladungslink - bitte kontaktieren Sie ihren Ansprechpartner</span>
          </div>
        )}
        <div
          className={
            "mb-5 mt-5 flex flex-col gap-5 rounded-xl bg-clip-border p-4 shadow-2xl  lg:w-2/3 "
          }
        >
          <div className={"flex flex-col-reverse gap-1 sm:flex-row sm:justify-between"}>
            <h3 className={"py-4 text-primary"}> Lastschriftmandat hinzufügen </h3>
            <div className={"px-0 py-4 "}>
              <Image
                src={`${ouLogos["Eulektro GmbH"]?.logo ?? ""}`}
                priority={false}
                width={200}
                height={150}
                alt={`Eulektro GmbH Logo`}
                className={"max-h-12"}
              />
            </div>
          </div>
          <h5 className={"text-primary"}>
            Hier können Sie der Eulektro GmbH ein elektronisches Lastschriftmandat erteilen, sodass
            ihre monatlichen Betriebskosten per Lastschrift von Ihrem Konto eingezogen werden
            können.
          </h5>
          <h4 className={"mt-5 text-lg"}>
            Die Adressdaten Ihres Unternehmens werden aus unserem System übernommen und können nicht
            geändert werden. Bitte geben Sie Ihren Namen sowie IBAN an.
          </h4>
          {!clientSecret && !setupError && <Loading />}

          {!setupError && (
            <CompanyPaymentWrapper clientSecret={clientSecret ?? ""} contact={contact} />
          )}
          {setupError && (
            <>
              <div className={"flex gap-1 text-red-500"}>
                <FaTriangleExclamation size={20} />
                <span>Fehler beim Vorbereiten des Mandates ({setupError})!</span>
              </div>
              <span>Support bei Fehlern: <EMAIL></span>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Page;
