"use client";
import { Elements } from "@stripe/react-stripe-js";
import getStripe from "~/stripe/get-stripejs";
import { Contact } from "@prisma/client";
import { CreateCompanySepaForm } from "~/app/(sepaSetup)/createCompanySepa/CreateCompanySepaForm";
import { ContactsWithAddress } from "~/types/prisma/contact";

interface Props {
  clientSecret: string;
  contact?: ContactsWithAddress;
}

const stripePromise = getStripe();
export const CompanyPaymentWrapper = ({ clientSecret: clientSecret, contact: contact }: Props) => {
  return (
    <>
      {clientSecret && stripePromise && (
        <Elements
          stripe={stripePromise}
          options={{
            locale: "de",
            clientSecret: clientSecret,
            appearance: {
              theme: "flat",
              variables: {
                colorText: "#21697C",
                fontFamily: "Source Sans Pro",
              },
            },
          }}
        >
          <CreateCompanySepaForm clientSecret={clientSecret} contact={contact} />
        </Elements>
      )}
    </>
  );
};
