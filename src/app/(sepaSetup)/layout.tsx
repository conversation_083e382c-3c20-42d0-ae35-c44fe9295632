import "~/styles/globals.css";
import { ouColors } from "~/styles/oucolors/oucolors";
import { Source_Sans_3 } from "next/font/google";

export const metadata = {
  title: "Eulektro SEPA-Lastschrift",
  icons: {
    icon: "/favicon/favicon-32x32.png",
    apple: "favicon/favicon-32x32.png",
  },
  description: "Eulektro",
};
const sansnew = Source_Sans_3({ display: "swap", variable: "--font-sansnew", subsets: ["latin"] });

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="de" className={`${sansnew.variable}`}>
      <body
        style={ouColors["Eulektro_Public"]}
        className={
          "m-0 bg-eul-main text-left font-sansnew text-base font-normal leading-default text-primary antialiased dark:bg-slate-950 dark:text-white"
        }
      >
        {children}
      </body>
    </html>
  );
}
