import { Suspense } from "react";
import Card from "../../../component/card";
import Loading from "../loading";
import type { INode } from "./component/Tree";

import Detail from "./component/Detail";
import SelectOU from "../users/compontent/SelectOu";
import prisma from "~/server/db/prisma";
import type { Ou } from "@prisma/client";

const getChildren = (parent: Ou, ous: Ou[]): INode[] => {
  const children = ous.filter((ou) => ou.parentId === parent.id);

  return children.map((ou) => {
    return {
      id: ou.id,
      name: ou.name,
      children: getC<PERSON>dren(ou, ous),
    };
  });
};

const getTenantsTree = async (): Promise<INode[]> => {
  const ous = await prisma.ou.findMany();

  const rootOus = ous.filter((ou) => !ou.parentId);

  const nodes = rootOus.map((ou) => {
    return {
      id: ou.id,
      name: ou.name,
      children: get<PERSON><PERSON><PERSON><PERSON>(ou, ous),
    };
  });

  return nodes;
};

const Page = async () => {
  const tenantsTree = await getTenantsTree();

  return (
    <div className="mx-auto w-full p-6">
      <h2 className={"mb-0 mb-2 font-bold dark:text-white"}>Tenant configuration</h2>
      <div className={"flex gap-5"}>
        <Suspense fallback={<Loading />}>
          <Card className={"w-6/12"} header_left={"Organization Unit"}>
            <SelectOU nodes={tenantsTree} />
          </Card>
          <Card className={"w-6/12"} header_left={"Basic"}>
            <Detail />
          </Card>
        </Suspense>
      </div>
    </div>
  );
};

export default Page;
