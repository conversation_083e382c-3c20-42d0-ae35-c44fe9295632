import type { <PERSON> } from "react";
import React from "react";
import { FaFolderOpen } from "react-icons/fa";
import { SlOrganization } from "react-icons/sl";

export interface INode {
  id: string;
  name: string;
  children?: INode[];
}

interface ITreeNodeProps {
  node: INode;
  onSelect: (node: INode) => void; // Hinzufügen der onSelect Funktion
}

const TreeNode: FC<ITreeNodeProps> = ({ node, onSelect }) => {
  // onSelect Funktion hinzufügen
  return (
    <div className="pl-4">
      <p
        className="mb-2 cursor-pointer hover:bg-gray-200"
        onClick={() => onSelect(node)} // onSelect Funktion wird aufgerufen, wenn auf einen Knoten geklickt wird
      >
        <SlOrganization className={"inline text-primary"} /> {node.name}
      </p>
      {node.children && <Tree nodes={node.children} onSelect={onSelect} />}
    </div>
  );
};

interface ITreeProps {
  nodes: INode[];
  onSelect: (node: INode) => void; // Hinzufügen der onSelect Funktion
}

const Tree: FC<ITreeProps> = ({ nodes, onSelect }) => {
  // onSelect Funktion hinzufügen
  return (
    <div>
      {nodes.map((node) => (
        <TreeNode key={node.id} node={node} onSelect={onSelect} /> // onSelect Funktion an TreeNode weitergeben
      ))}
    </div>
  );
};

export default Tree;
