"use client";
import { SessionProvider } from "next-auth/react";
import React from "react";
import { SoftUIControllerProvider } from "~/context";

export default function ProvidersWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SoftUIControllerProvider>
      <SessionProvider>
        {children} {/* Our entire app -> has access to NextAuth */}
      </SessionProvider>
    </SoftUIControllerProvider>
  );
}
