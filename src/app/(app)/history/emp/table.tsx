"use client";

import { AgGridReact } from "ag-grid-react";
import React, { useEffect, useState } from "react";
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import type { GridOptions } from "ag-grid-community";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import { LicenseManager } from "ag-grid-enterprise";
import type { BaseWithValueColDefParams } from "ag-grid-community/dist/lib/entities/colDef";
import { dayComparator } from "~/utils/comperator/comperators";

LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

const Table = () => {
  const [rowData, setRowData] = useState([]);

  const loadData = () => {
    fetch("/api/cdr/groupByContact")
      .then((res) => res.json())
      .then((data) => {
        setRowData(data.msp);
      });
  };

  useEffect(() => {
    loadData();
    const intervalId = setInterval(() => {
      loadData();
    }, 30000);
    return () => clearInterval(intervalId);
  }, []);

  const [columnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    {
      field: "date",
      headerName: "Tag",
      sort: "desc",
      enableRowGroup: true,
      rowGroupIndex: 0,
      hide: true,
      rowGroup: true,

      comparator: dayComparator,
    },
    {
      field: "contact",
      headerName: "EMP",
      sort: "asc",
      enableRowGroup: true,
      rowGroup: true,
      rowGroupIndex: 1,
      hide: true,
    },
    {
      field: "kwh",
      headerName: "kWh",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: (params: BaseWithValueColDefParams) => {
        if (params.value) {
          return params.value.toFixed(2);
        }
        return "";
      },
    },
    { field: "count", headerName: "session", aggFunc: "sum" },
  ]);

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    groupDefaultExpanded: 1,
    autoGroupColumnDef: { sort: "desc" },
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    groupIncludeFooter: true,
    // includes grand total
    groupIncludeTotalFooter: true,
    rowHeight: 30,
    sideBar: {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          minWidth: 225,
          maxWidth: 225,
          width: 225,
        },
        {
          id: "filters",
          labelDefault: "Filters",
          labelKey: "filters",
          iconKey: "filter",
          toolPanel: "agFiltersToolPanel",
          minWidth: 180,
          maxWidth: 400,
          width: 250,
        },
      ],
      position: "right",
      defaultToolPanel: "",
    },
  };

  return (
    <div className="ag-theme-alpine" style={{ width: "100%", height: 500 }}>
      <AgGridReact
        gridOptions={defaultBackendGridOptions}
        onRowDataChanged={(api) => {
          api.api.sizeColumnsToFit();
          return api;
        }}
        enableCharts={true}
        enableRangeSelection={true}
        rowData={rowData}
        columnDefs={columnDefs}
        groupIncludeFooter={true}
        groupIncludeTotalFooter={true}
        animateRows={true}
        onGridSizeChanged={(params) => {
          params.api.sizeColumnsToFit();
        }}
      />
    </div>
  );
};

export default Table;
