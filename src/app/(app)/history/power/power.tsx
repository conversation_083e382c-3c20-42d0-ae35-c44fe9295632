"use client";
import React, { useEffect, useState } from "react";
import ReactECharts from "echarts-for-react";

const PowerChart = () => {
  const [chartData, setChartData] = useState({
    power: [],
    price: [],
    timestamps: [],
  });

  const [dateRange, setDateRange] = useState({
    start: new Date("2023-01-01").getTime(),
    end: new Date().getTime(),
  });

  const fetchDeltaData = async () => {
    const rawData = await fetch("api/history/powerHistory", {
      next: { revalidate: 0 },
      method: "POST",
      body: JSON.stringify(dateRange),
    });
    const data = await rawData.json();
    return data;
  };

  const processData = (powerData, priceData) => {
    const unifiedTimestamps = new Set([
      ...powerData.map((item) => item[0]),
      ...priceData.map((item) => item[0]),
    ]);

    const processedPower = Array.from(unifiedTimestamps).map((timestamp) => {
      const foundItem = powerData.find((item) => item[0] === timestamp);
      return foundItem ? foundItem[1] : null; // oder 0, je nachdem, was Sie bevorzugen
    });

    const processedPrice = Array.from(unifiedTimestamps).map((timestamp) => {
      const foundItem = priceData.find((item) => item[0] === timestamp);
      return foundItem ? foundItem[1] : null; // oder 0, je nachdem, was Sie bevorzugen
    });

    return {
      timestamps: Array.from(unifiedTimestamps).map((ts) => new Date(ts).toLocaleString()),
      power: processedPower,
      price: processedPrice,
    };
  };

  useEffect(() => {
    fetchDeltaData().then((data) => {
      const processed = processData(data.power, data.price);
      setChartData({
        power: processed.power,
        price: processed.price,
        timestamps: processed.timestamps,
      });
    });

    const intervalId = setInterval(() => {
      fetchDeltaData().then((data) => {
        const processed = processData(data.power, data.price);
        setChartData({
          power: processed.power,
          price: processed.price,
          timestamps: processed.timestamps,
        });
      });
    }, 120000);

    return () => clearInterval(intervalId);
  }, [dateRange]);

  if (!chartData.power.length || !chartData.price.length) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const getOptions = () => {
    return {
      dataZoom: [
        {
          type: "slider",
          startValue: new Date("2023-01-01").getTime(),
          endValue: dateRange.end,
          realtime: true,
          filterMode: "filter",
          xAxisIndex: 0,
          handleSize: 20,
          bottom: 10,
          onEnd: (params) => {
            const startDate = new Date(params.startValue).getTime();
            const endDate = new Date(params.endValue).getTime();
            setDateRange({
              start: startDate,
              end: endDate,
            });
          },
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
      },
      legend: {
        data: ["kW", "ct/kWh"],
      },
      grid: {
        bottom: 80,
      },
      xAxis: {
        type: "category",
        data: chartData.timestamps,
      },
      yAxis: [
        {
          type: "value",
          name: "kW",
        },
        {
          type: "value",
          name: "ct/kWh",
          position: "right",
        },
      ],
      series: [
        {
          name: "kW",
          type: "line",
          data: chartData.power,
        },
        {
          name: "ct/kWh",
          type: "bar",
          yAxisIndex: 1,
          data: chartData.price,
        },
      ],
    };
  };

  return (
    <div>
      <ReactECharts
        option={getOptions()}
        style={{ height: "400px", width: "100%" }}
        className="h-full"
      />
    </div>
  );
};

export default PowerChart;
