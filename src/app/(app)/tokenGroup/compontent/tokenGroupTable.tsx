"use client";
import Table from "../../../../utils/table/table";
import type { TokenGroup } from "@prisma/client";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import type { ICellRendererParams, IsRowSelectable } from "ag-grid-community";
import Link from "next/link";
import { HiMagnifyingGlass } from "react-icons/hi2";
import React, { useCallback, useMemo } from "react";
import { Token } from "@prisma/client";

interface Props {
  data: TokenGroup[];
}

const TokenGroupTable = ({ data }: Props) => {
  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <Link href={`/tokenGroup/${params?.data?.id}`}>
          <HiMagnifyingGlass />
        </Link>
      </>
    );
  };

  const columnDefs: (ColDef | ColGroupDef)[] = [
    { field: "id", headerName: "ID" },
    { field: "name", headerName: "Name" },
    { field: "action", headerName: "Aktion", cellRenderer: ActionCellRenderer },
  ];

  return (
    <div className={"h-[80vh] min-h-[500px]"}>
      <Table gridId={"tokengroup"} columnDefs={columnDefs} rowData={data} />
    </div>
  );
};

export default TokenGroupTable;
