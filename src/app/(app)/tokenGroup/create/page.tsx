"use client";
import Card from "../../../../component/card";
import type { SubmitHand<PERSON> } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { Tarif, TokenGroup } from "@prisma/client";
import React, { startTransition } from "react";
import { useRouter } from "next/navigation";

const CreateToken = () => {
  const router = useRouter();

  const {
    getValues,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<TokenGroup>();

  const onSubmit: SubmitHandler<TokenGroup> = async (data) => {
    // Mutate external data source
    const response = await fetch(`/api/tokenGroup`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    if (response.status == 200) {
      reset(data);
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push(`/tokenGroup`);
      });
    } else {
      console.log(response.statusText);
      alert(`${response.statusText}`);
    }
  };

  return (
    <Card header_left={"Create TokenGroup"}>
      <form className={"flex flex-col"} onSubmit={handleSubmit(onSubmit)}>
        <input className={"input"} type={"text"} placeholder={"Name"} {...register("name")} />
        <button type={"submit"} className="btn mt-5 max-w-46 self-end">
          Speichern
        </button>
      </form>
    </Card>
  );
};

export default CreateToken;
