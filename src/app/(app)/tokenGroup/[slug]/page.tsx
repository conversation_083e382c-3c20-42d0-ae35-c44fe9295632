"use client";
import Card from "../../../../component/card";

import type { Contact, Token } from "@prisma/client";
import useSWR from "swr";
import type { Prisma } from "@prisma/client";
import { MdOutlineEdit } from "react-icons/md";
import React, { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { IoIosSave } from "react-icons/io";

import { useRouter } from "next/navigation";
import Table from "../../../../utils/table/table";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import type { ICellRendererParams } from "ag-grid-community";
import { BsFillTrashFill } from "react-icons/bs";
import { AiFillCloseCircle } from "react-icons/ai";
import { IoAddSharp } from "react-icons/io5";
import GroupName from "./component/groupName";

const fetcher = async (url: string) =>
  fetch(url, { method: "GET", headers: { "Content-Type": "application/json" } }).then((res) =>
    res.json(),
  );

export type TokenGroupWithIncludes = Prisma.TokenGroupGetPayload<{
  include: { contact: true; tokens: true; billingOUs: true };
}>;

const TokenGroupPage = ({ params }: { params: { slug: string } }) => {
  const { slug } = params;

  const router = useRouter();
  const {
    getValues,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<Token>();

  const [editContact, setEditContact] = useState(false);
  const [addToken, setAddToken] = useState(false);

  const {
    data: tokenGroup,
    isLoading: isLoadingTokenGroup,
    mutate: mutateTokenGroup,
  } = useSWR<TokenGroupWithIncludes>(`/api/tokenGroup/${slug}`, fetcher);

  const { data: contacts, isLoading: isLoadingContacts } = useSWR<Contact[]>(
    `/api/contact`,
    fetcher,
  );

  const changeContact = async (event: React.ChangeEvent<HTMLSelectElement>) => {
    await fetch(`/api/tokenGroup/${slug}/connectContact`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        ...tokenGroup,
        contact: {
          connect: {
            id: event.target.value,
          },
        },
      }),
    });
    await mutateTokenGroup();
    setEditContact(false);
    return;
  };

  const onSubmit: SubmitHandler<Token> = async (data) => {
    const newToken = {
      ...data,
      tokenGroup: {
        connect: {
          id: tokenGroup?.id,
        },
      },
    };
    const response = await fetch(`/api/token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(newToken),
    });
    if (response.status == 200) {
      reset(data);
      setAddToken(false);
      await mutateTokenGroup();
    } else {
      alert(`${response.statusText}`);
    }

    return;
  };

  const deleteToken = async (token: Token) => {
    await fetch(`/api/token`, {
      method: "DELETE",
      body: JSON.stringify(token),
    });
    mutateTokenGroup();
  };

  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <BsFillTrashFill
          onClick={() => {
            deleteToken(params.data);
          }}
          className={"cursor-pointer"}
        />
      </>
    );
  };

  const columnDefs: (ColDef | ColGroupDef)[] = [
    { field: "name", headerName: "Name" },
    {
      field: "authenticationId",
      headerName: "Uid",
    },
    { field: "plateNumber", headerName: "PlateNumber" },
    { field: "description", headerName: "Description" },
    { field: "", headerName: "Action", cellRenderer: ActionCellRenderer },
  ];

  if (isLoadingTokenGroup || isLoadingContacts) {
    return <div>Loading</div>;
  }

  return (
    <>
      <Card header_left={`TokenGroup`}>
        <div className={"flex flex-row justify-around gap-5"}>
          <div className={"w-full"}>
            <h3>Group Name</h3>
            {tokenGroup && (
              <GroupName tokenGroup={tokenGroup} mutateTokenGroup={mutateTokenGroup} />
            )}
            <div className={"mt-5 flex flex-col"}>
              {addToken ? (
                <button className={"btn ml-auto w-52"} onClick={() => setAddToken(false)}>
                  <AiFillCloseCircle size={18} />
                  Close
                </button>
              ) : (
                <button className={"btn ml-auto w-52"} onClick={() => setAddToken(true)}>
                  <IoAddSharp size={18} /> Add Token
                </button>
              )}
              {addToken && (
                <form className={"mt-5 flex flex-col gap-2"} onSubmit={handleSubmit(onSubmit)}>
                  <div className={"flex flex-row gap-5"}>
                    <input
                      className={"input"}
                      type={"text"}
                      placeholder={"Name"}
                      {...register("name")}
                    />
                    <input
                      className={"input"}
                      type={"text"}
                      placeholder={"Uid"}
                      {...register("authenticationId")}
                    />
                    <input
                      className={"input"}
                      type={"text"}
                      placeholder={"plateNumber"}
                      {...register("plateNumber")}
                    />
                  </div>
                  <div className={"flex"}>
                    <textarea
                      cols={20}
                      className={"input"}
                      placeholder={"Description"}
                      {...register("description")}
                    />
                  </div>
                  <div className={"flex flex-row-reverse"}>
                    <button className={"btn"} type={"submit"}>
                      <IoIosSave className={"mr-2"} size={18} />
                      Add
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
          <div className={"w-full"}>
            <h3>Contact for invoicing</h3>
            <span>
              Contact: {tokenGroup?.contact?.name}{" "}
              <MdOutlineEdit
                className={"cursor-pointer"}
                onClick={() => setEditContact(!editContact)}
              />
            </span>
            {editContact && (
              <div className={"mt-2"}>
                <label
                  htmlFor="countries"
                  className="bhtmlFork text-sclassNamet-medium mb-2 text-gray-900 dark:text-white"
                >
                  Choose an Contact for Invoicing
                </label>
                <select
                  onChange={(event: React.ChangeEvent<HTMLSelectElement>) => changeContact(event)}
                  id="countries"
                  className="borderclassNameer-gray-300 block w-full rounded-lg bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                >
                  <option key={0}>Kein EMP</option>
                  {contacts &&
                    contacts.map((contact) => {
                      return (
                        <option key={contact.id} value={contact.id}>
                          {contact.name}
                        </option>
                      );
                    })}
                </select>
              </div>
            )}
          </div>
          <div className={"w-full"}>
            <h3>Billings OUs</h3>

            <ul className={"ml-4 list-disc"}>
              {tokenGroup?.billingOUs.map((ou, index) => <li key={index}>{ou.name}</li>)}
            </ul>
          </div>
        </div>
        <hr />
      </Card>
      <Card className={"mt-5"} header_left={`Tokens`}>
        <Table gridId={"tokens"} columnDefs={columnDefs} rowData={tokenGroup?.tokens} />
      </Card>
    </>
  );
};

export default TokenGroupPage;
