import React from "react";
import type { TokenGroup } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { TokenGroupWithIncludes } from "../page";
import { Token } from "@prisma/client";

interface Props {
  tokenGroup: TokenGroupWithIncludes;
  mutateTokenGroup: any;
}
const GroupName = ({ tokenGroup, mutateTokenGroup }: Props) => {
  const {
    getValues,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<TokenGroupWithIncludes>({ defaultValues: tokenGroup });

  const handleSave: SubmitHandler<TokenGroupWithIncludes> = async (
    tokenGroup: TokenGroupWithIncludes,
  ) => {
    const response = await fetch(`/api/tokenGroup/${tokenGroup.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(tokenGroup),
    });
    if (response.status == 200) {
      reset(tokenGroup);
      await mutateTokenGroup();
    } else {
      alert(`${response.statusText}`);
    }

    return;
  };

  return (
    <form onSubmit={handleSubmit(handleSave)}>
      <input
        className={"input  content-end justify-end self-end"}
        {...register("name", { onBlur: handleSubmit(handleSave) })}
      />
    </form>
  );
};

export default GroupName;
