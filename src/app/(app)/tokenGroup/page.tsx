import Link from "next/link";
import { IoIosAdd } from "react-icons/io";
import Card from "../../../component/card";
import TokenGroupTable from "./compontent/tokenGroupTable";
import prisma from "../../../server/db/prisma";
export const revalidate = 0;

const Token = async () => {
  const tokenGroups = await prisma.tokenGroup.findMany();

  return (
    <Card header_left={"TokenGroup"}>
      <div className={"mb-10 flex flex-row-reverse"}>
        <Link href={"/tokenGroup/create"}>
          <button className={"btn text-right"}>
            <IoIosAdd size={20} />
            Neue Token Group
          </button>
        </Link>
      </div>
      <TokenGroupTable data={tokenGroups} />
    </Card>
  );
};

export default Token;
