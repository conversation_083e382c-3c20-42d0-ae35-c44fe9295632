const handleDownload = async (
  contactTarifList: any,
  startDate: Date,
  endDate: Date,
  setLoading: any,
) => {
  setLoading(true);

  for (const { contact, tarifName } of contactTarifList) {
    try {
      // Send the POST request to the API
      const res = await fetch("/api/cdr/csvDownload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          startDate: startDate.toLocaleString("sv-SE").slice(0, 10),
          endDate: endDate.toLocaleString("sv-SE").slice(0, 10),
          contact,
          tarifName,
        }),
      });
      if (res.ok) {
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        const csv_file_name = `${
          env.NEXT_PUBLIC_DEBUG == "true" ? "_DEV_" : ""
        }CDR_FROM_EULEKTRO_FOR_${contact}_TARIF_${tarifName}_RANGE_${startDate.toLocaleDateString(
          "de-DE",
        )}-${endDate.toLocaleDateString("de-DE")}.csv`;
        link.setAttribute("download", csv_file_name);
        document.body.appendChild(link);
        link.click();
      } else {
        //handle error
        console.error(res.statusText);
      }
    } catch (err) {
      console.error(err);
    } finally {
    }
    setLoading(false);
  }
};

export default handleDownload;
