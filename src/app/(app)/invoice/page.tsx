import React from "react";
import Card from "../../../component/card";

import { RoamingInvoiceSelect } from "~/app/(app)/invoice/component/roamingInvoiceSelect";
import DateContextWrapper from "~/component/dateRangePicker/dateContextWrapper";
import { InvoiceDataWrapper } from "~/app/(app)/invoice/component/InvoiceDataWrapper";

export const revalidate = 0;

const Page = () => {
  return (
    <DateContextWrapper>
      <h1 className={"mb-0 font-bold dark:text-white"}>
        <>Rechnungen</>
      </h1>
      <div className="mb-3 flex justify-end">
        <RoamingInvoiceSelect />
      </div>
      <Card header_left={``}>
        <InvoiceDataWrapper />
      </Card>
    </DateContextWrapper>
  );
};

export default Page;
