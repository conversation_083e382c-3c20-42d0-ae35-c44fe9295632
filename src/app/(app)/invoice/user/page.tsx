import Card from "~/component/card";
import { InvoiceDataWrapper } from "~/app/(app)/invoice/component/InvoiceDataWrapper";
import React from "react";
import { UserInvoiceSelect } from "~/app/(app)/invoice/component/roamingInvoiceSelect";

const Page = () => {
  return (
    <>
      <div className="mb-3 flex justify-end">
        <UserInvoiceSelect />
      </div>
      <Card header_left={"Erstelle Rechnung für Endkunden"}>
        <InvoiceDataWrapper />
      </Card>
    </>
  );
};

export default Page;
