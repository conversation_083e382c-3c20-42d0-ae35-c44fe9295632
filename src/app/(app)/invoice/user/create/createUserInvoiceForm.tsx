"use client";

import type { SubmitHand<PERSON> } from "react-hook-form";
import { Controller, useForm } from "react-hook-form";
import { getFirstDayOfYear, getLastDayOfYear } from "~/utils/date/date";
import React, { startTransition, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import DatePicker, { registerLocale, setDefaultLocale } from "react-datepicker";

import "react-datepicker/dist/react-datepicker.css";

import de from "date-fns/locale/de";
import useSWRMutation from "swr/mutation";
import Button from "~/component/button";

import { FiLoader } from "react-icons/fi";
import Loading from "~/app/(app)/loading";

import { UserWithOu } from "~/types/prisma/user";

registerLocale("de", de);
setDefaultLocale("de");

const sendRequest = async (
  url: string,
  { arg }: { arg: { startDate: Date; endDate: Date } },
): Promise<UserWithOu[]> => {
  const res = await fetch(url, {
    method: "POST",
    body: JSON.stringify(arg),
  });
  return (await res.json()) as UserWithOu[];
};

interface UserInvoiceFormValues {
  userId: string;
  dateRange: { 0: Date; 1: Date };
}
const CreateUserInvoiceForm = () => {
  const router = useRouter();

  const intialFormData = {
    defaultValues: {
      dateRange: {
        0: getFirstDayOfYear(),
        1: getLastDayOfYear(),
      },
    },
  };
  const dateToFormat = new Date();
  const lastMonth = new Date();
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  const [previewLoading, setPreviewLoading] = useState<boolean>(false);
  const [apiError, setApiError] = useState<string>("");
  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<UserInvoiceFormValues>(intialFormData);

  const {
    data: users,
    trigger: triggerUpdate,
    isMutating: requestLoading,
  } = useSWRMutation("/api/user/withCdrs", sendRequest);

  const changeDateRange = async (startDate: Date | null, endDate: Date | null) => {
    setApiError("");
    if (startDate == null || endDate == null) return;

    endDate.setDate(endDate.getDate() + 1);

    await triggerUpdate({
      startDate: startDate,
      endDate: endDate,
    });
  };

  const submitAction: SubmitHandler<UserInvoiceFormValues> = async (formData) => {
    setApiError("");
    setPreviewLoading(true);
    const res = await fetch("/api/invoice/user/cardHolder/prepare", {
      method: "POST",
      body: JSON.stringify({
        userId: formData.userId,
        startDate: formData.dateRange[0],
        endDate: formData.dateRange[1],
      }),
    });
    if (res.status == 200) {
      const { invoiceId } = await res.json();
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push(`/invoice/${invoiceId}`);
      });
    } else {
      const data = await res.json();
      setApiError(`Fehler beim generieren der Rechnung: ${data}`);
      setPreviewLoading(false);
    }
    return;
  };

  useEffect(() => {
    setLastMonth();
  }, []);

  const setThisYear = () => {
    setApiError("");
    const startDate = new Date(new Date().getFullYear(), 0, 1);
    const endDate = new Date(new Date().getFullYear(), 11, 31);
    setValue("dateRange", [startDate, endDate]); // Füge diese Zeile hinzu

    void changeDateRange(startDate, endDate);
  };

  const setLastYear = () => {
    setApiError("");
    const startDate = new Date(new Date().getFullYear() - 1, 0, 1);
    const endDate = new Date(new Date().getFullYear() - 1, 11, 31);
    setValue("dateRange", [startDate, endDate]); // Füge diese Zeile hinzu

    void changeDateRange(startDate, endDate);
  };

  const setLastMonth = () => {
    setApiError("");
    const startDate = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
    const endDate = new Date(new Date().getFullYear(), new Date().getMonth(), 0);
    setValue("dateRange", [startDate, endDate]); // Füge diese Zeile hinzu

    void changeDateRange(startDate, endDate);
  };

  return (
    <div className="flex flex-col gap-4 sm:flex-row">
      <div className="sm:w-4/6">
        {users && users.length == 0 && (
          <span className={"text-orange-400"}>
            Keine User mit passendem Tarif für den Zeitraum gefunden
          </span>
        )}
        {Object.keys(errors).length > 0 && (
          <ul className="text-red-500">
            {Object.keys(errors).map((fieldName, index) => (
              <li key={index}>{errors[fieldName as keyof UserInvoiceFormValues]?.message}</li>
            ))}
          </ul>
        )}
        {apiError && <span className="text-red-500">{apiError}</span>}
        <form
          className={"flex flex-col gap-4 sm:w-80 sm:pr-4 md:w-120"}
          onSubmit={handleSubmit(submitAction)}
        >
          <div className={"w-full "}>
            <div className={"mb-1 flex flex-row items-center gap-1"}>
              <label className="ml-1 text-xs font-bold text-primary" htmlFor="">
                User ({users ? users.length : 0} gefunden)
              </label>
              {requestLoading && <FiLoader className=" animate-spin text-primary" />}
            </div>
            <select
              {...register("userId", {
                validate: (value) => {
                  if (value == "-1") {
                    return "Bitte einen User auswählen";
                  }
                  if (users?.length == 0) {
                    return "Keinen User gefunden mit CDRs, bitte Zeitfenster ändern";
                  }
                },
              })}
              className="form-select m-0 block w-full  appearance-none
                rounded
                rounded-lg border  border-solid border-gray-300
                bg-white  bg-clip-padding bg-no-repeat px-3
                py-1.5 text-base font-normal  text-gray-700
                    focus:shadow-lg
                focus:outline-none sm:max-w-120"
            >
              <option key={"placeholder"} value={"-1"}>
                {!users ? "Keine User für den Zeitraum gefunden" : "Bitte wählen"}
              </option>

              {users &&
                users.map((user) => {
                  return (
                    <option key={user.id} value={user.id}>
                      {user.name} {user.lastName} ({user.ou.name})
                    </option>
                  );
                })}
            </select>
          </div>
          <div className={"w-full "}>
            <label className="ml-1 w-full text-xs font-bold text-primary " htmlFor="Language">
              Zeitraum festlegen (von bis einschließlich)
            </label>
            <div className={"flex flex-row "}>
              <Controller
                control={control}
                name={"dateRange"}
                render={({ field: { onChange, value } }) => {
                  return (
                    <DatePicker
                      wrapperClassName={"w-full"}
                      className={
                        "w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm  font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-lg focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      }
                      clearButtonClassName={""}
                      selectsRange={true}
                      startDate={value[0]}
                      endDate={value[1]}
                      onChange={(dates: [Date | null, Date | null]): void => {
                        void changeDateRange(dates[0], dates[1]);
                        onChange(dates);
                      }}
                      isClearable={true}
                      locale="de"
                      dateFormat={["P", "PP", "PPP", "MMM dd yyyy", "dd MMMM yyyy"]}
                    />
                  );
                }}
              />
            </div>
          </div>

          <div className={"flex justify-between gap-1 sm:w-80 sm:pr-4 md:w-120"}>
            <Button type={"button"} onClick={setLastYear} className="w-full">
              {(dateToFormat.getFullYear() - 1).toString()}
            </Button>
            <Button type={"button"} onClick={setThisYear} className="w-full">
              {dateToFormat.getFullYear().toString()}
            </Button>

            <Button type={"button"} onClick={setLastMonth} className="w-full">
              {lastMonth.toLocaleString("de-De", { month: "long" })}
            </Button>
          </div>
          <hr className={"my-2 h-px border-1 bg-gray-950 dark:bg-gray-700"} />
          <Button
            disabled={previewLoading || users?.length == 0}
            type="submit"
            className={"mb-6 w-full"}
          >
            User Rechnung erstellen
          </Button>
          {previewLoading && <Loading />}
        </form>
      </div>
    </div>
  );
};
export default CreateUserInvoiceForm;
