"use client";

import type { <PERSON>, Prisma } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { Controller, useForm } from "react-hook-form";

import { getFirstDayOfYear, getLastDayOfYear } from "~/utils/date/date";
import React, { startTransition, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import DatePicker, { registerLocale, setDefaultLocale } from "react-datepicker";

import "react-datepicker/dist/react-datepicker.css";

import de from "date-fns/locale/de";
import useSWRMutation from "swr/mutation";
import { ContactType } from "~/server/types/types";
import Button from "~/component/button";
import { ContactWithNumTarifs } from "~/app/api/contact/withCdrs/route";

import { FiLoader } from "react-icons/fi";
import Loading from "~/app/(app)/loading";

registerLocale("de", de);
setDefaultLocale("de");

const sendRequest = async (
  url: string,
  { arg }: { arg: { startDate: Date; endDate: Date } },
): Promise<ContactWithNumTarifs[]> => {
  const res = await fetch(url, {
    method: "POST",
    body: JSON.stringify({ ...arg, kindOfContact: ContactType.CPO }),
  });
  return (await res.json()) as ContactWithNumTarifs[];
};

type CreditFormValues = {
  contactIds: string[];
  contactType: ContactType;
  dateRange: {
    0: Date;
    1: Date;
  };
  onlyPaid?: boolean;
};

const CreateCreditForm = () => {
  const router = useRouter();

  const intialFormData = {
    defaultValues: {
      dateRange: {
        0: getFirstDayOfYear(),
        1: getLastDayOfYear(),
      },
      contactIds: [],
      onlyPaid: false,
    },
  };
  const dateToFormat = new Date();
  const lastMonth = new Date();
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  const [previewLoading, setPreviewLoading] = useState<boolean>(false);
  const [apiError, setApiError] = useState<string>("");
  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<CreditFormValues>(intialFormData);

  const {
    data: contacts,
    trigger: triggerContact,
    isMutating: cpoLoading,
  } = useSWRMutation("/api/contact/withCdrs", sendRequest);

  const changeDateRange = async (dates: [Date | null, Date | null]) => {
    setApiError("");
    if (dates[0] == null || dates[1] == null) return;

    const endDate = new Date(dates[1]);
    endDate.setDate(endDate.getDate() + 1);

    await triggerContact({
      startDate: dates[0],
      endDate: endDate,
    });
  };

  const submitAction: SubmitHandler<CreditFormValues> = async (formData) => {
    setApiError("");
    setPreviewLoading(true);
    const res = await fetch("/api/credit/createPreview", {
      method: "POST",
      body: JSON.stringify({ ...formData, kindOfContact: ContactType.CPO }),
    });
    if (res.status == 200) {
      const { invoiceId } = await res.json();
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push(`/invoice`);
      });
    } else {
      const data = await res.json();
      setApiError(`Fehler beim generieren der Gutschrift: ${data.errorMessages}`);
      setPreviewLoading(false);
    }
    return;
  };

  useEffect(() => {
    setLastMonth();
  }, []);

  const setThisYear = () => {
    setApiError("");
    const startDate = new Date(new Date().getFullYear(), 0, 1);
    const endDate = new Date(new Date().getFullYear(), 11, 31);
    setValue("dateRange", [startDate, endDate]); // Füge diese Zeile hinzu
    void changeDateRange([startDate, endDate]);
  };

  const setLastYear = () => {
    setApiError("");
    const startDate = new Date(new Date().getFullYear() - 1, 0, 1);
    const endDate = new Date(new Date().getFullYear() - 1, 11, 31);
    setValue("dateRange", [startDate, endDate]); // Füge diese Zeile hinzu
    void changeDateRange([startDate, endDate]);
  };

  const setLastMonth = () => {
    setApiError("");
    const startDate = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
    const endDate = new Date(new Date().getFullYear(), new Date().getMonth(), 0);
    setValue("dateRange", [startDate, endDate]); // Füge diese Zeile hinzu
    void changeDateRange([startDate, endDate]);
  };

  return (
    <div className="flex flex-col gap-4 sm:flex-row">
      <div className="sm:w-4/6">
        {contacts && contacts.length == 0 && (
          <span className={"text-orange-400"}>
            Keine CPOs mit passendem Tarif für den Zeitraum gefunden
          </span>
        )}
        {Object.keys(errors).length > 0 && (
          <ul className="text-red-500">
            {Object.keys(errors).map((fieldName, index) => (
              <li key={index}>{errors[fieldName as keyof CreditFormValues]?.message}</li>
            ))}
          </ul>
        )}
        {apiError && <span className="text-red-500">{apiError}</span>}

        <form
          className={"flex flex-col gap-4 sm:w-80 sm:pr-4 md:w-120"}
          onSubmit={handleSubmit(submitAction)}
        >
          <div className={"w-full "}>
            <div className={"mb-1 flex flex-row items-center gap-1"}>
              <label className="ml-1 text-xs font-bold text-primary" htmlFor="">
                CPOs ({contacts ? contacts.length : 0})
              </label>
              {cpoLoading && <FiLoader className=" animate-spin text-primary" />}
            </div>

            <select
              {...register("contactIds", {
                validate: (value) => {
                  if (contacts?.length == 0) {
                    return "Keinen CPO gefunden, bitte Zeitfenster ändern";
                  }
                },
              })}
              multiple={true}
              className="form-select m-0 block w-full  appearance-none
                rounded
                rounded-lg border  border-solid border-gray-300
                bg-white  bg-clip-padding bg-no-repeat px-3
                py-1.5 text-base font-normal  text-gray-700
                    focus:shadow-lg
                focus:outline-none sm:max-w-120"
            >
              {contacts &&
                contacts.map((contact) => {
                  return (
                    <option key={contact.id} value={contact.id}>
                      {contact.name} {contact?.numTarifs == 0 && "(Kein Tarif hinterlegt)"}
                    </option>
                  );
                })}
            </select>
          </div>
          <div className={"w-full "}>
            <label className="ml-1 w-full text-xs font-bold text-primary " htmlFor="Language">
              Zeitraum festlegen (von bis einschließlich)
            </label>
            <div className={"flex flex-row "}>
              <Controller
                control={control}
                name={"dateRange"}
                render={({ field: { onChange, value } }) => {
                  return (
                    <DatePicker
                      wrapperClassName={"w-full"}
                      className={
                        "w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm  font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-lg focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      }
                      clearButtonClassName={""}
                      selectsRange={true}
                      startDate={value[0]}
                      endDate={value[1]}
                      onChange={(dates: [Date | null, Date | null]): void => {
                        void changeDateRange(dates);
                        onChange(dates);
                      }}
                      isClearable={true}
                      locale="de"
                      dateFormat={["P", "PP", "PPP", "MMM dd yyyy", "dd MMMM yyyy"]}
                    />
                  );
                }}
              />
            </div>
          </div>
          <div className={"flex w-full flex-row items-center justify-start"}>
            <label
              className="mr-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor={"onlyPaidId"}
            >
              Nur Bezahlte CDRs hinzufügen
            </label>
            <div className="flex rounded-lg">
              <input
                {...register("onlyPaid")}
                id={"onlyPaidId"}
                type="checkbox"
                className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid
                border-gray-200 bg-slate-800/10  bg-none bg-contain bg-left bg-no-repeat align-top font-bold
                transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4
                after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250
                after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none
                checked:bg-right checked:after:translate-x-5.3"
              />
            </div>
          </div>
          <div className={"flex justify-between gap-1 sm:w-80 sm:pr-4 md:w-120"}>
            <Button type={"button"} onClick={setLastYear} className="w-full">
              {(dateToFormat.getFullYear() - 1).toString()}
            </Button>
            <Button type={"button"} onClick={setThisYear} className="w-full">
              {dateToFormat.getFullYear().toString()}
            </Button>

            <Button type={"button"} onClick={setLastMonth} className="w-full">
              {lastMonth.toLocaleString("de-De", { month: "long" })}
            </Button>
          </div>
          <hr className={"my-2 h-px border-1 bg-gray-950 dark:bg-gray-700"} />
          <Button
            disabled={previewLoading || contacts?.length == 0}
            type="submit"
            className={"mb-6 w-full"}
          >
            Roaming & Adhoc Gutschrift erstellen
          </Button>
          {previewLoading && <Loading />}
        </form>
      </div>
    </div>
  );
};
export default CreateCreditForm;
