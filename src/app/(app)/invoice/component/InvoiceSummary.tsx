import React, { useContext, useMemo } from "react";
import { InvoiceDataContext } from "~/app/(app)/invoice/component/InvoiceDataWrapper";
import { KindOfInvoice, StateOfInvoice } from "@prisma/client";
import { FaSackDollar } from "react-icons/fa6";
import Loading from "~/app/(app)/loading";
import { getMonthNameInLocale } from "~/utils/date/date";

export const InvoiceSummary = () => {
  const { invoiceData } = useContext(InvoiceDataContext);

  const { sumOpenInvoices, sumPaidInvoices, sumOpenCurrentMonth } = useMemo(() => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    const invoicesToSum = invoiceData.filter(
      (invoice) =>
        invoice.kindOfInvoice == KindOfInvoice.INVOICE &&
        invoice.stateOfInvoice != StateOfInvoice.CANCEL,
    );
    const sums = invoicesToSum.reduce(
      (
        accumulator: {
          sumPaidInvoices: null | number;
          sumOpenInvoices: null | number;
          sumOpenCurrentMonth: number;
        },
        currentInvoice,
      ) => {
        if (currentInvoice.paidOnDate) {
          accumulator.sumPaidInvoices = accumulator.sumPaidInvoices
            ? (accumulator.sumPaidInvoices += currentInvoice?.paidAmount ?? 0)
            : currentInvoice?.paidAmount;
        } else {
          accumulator.sumOpenInvoices = accumulator.sumOpenInvoices
            ? (accumulator.sumOpenInvoices += currentInvoice.sumGross)
            : currentInvoice.sumGross;

          if (
            currentInvoice?.invoiceDate &&
            new Date(currentInvoice.invoiceDate).getMonth() == currentMonth &&
            new Date(currentInvoice.invoiceDate).getFullYear() == currentYear
          ) {
            accumulator.sumOpenCurrentMonth += currentInvoice.sumGross;
          }
        }
        return accumulator;
      },
      { sumPaidInvoices: null, sumOpenInvoices: null, sumOpenCurrentMonth: 0 },
    );
    return sums;
  }, [invoiceData]);
  return (
    <>
      {sumOpenInvoices && sumPaidInvoices ? (
        <div className={"flex flex-col  gap-1 "}>
          <div className={"flex items-center justify-items-start"}>
            <FaSackDollar size={16} className={"mr-1 text-red-500"} />
            <span className={"hide sm:show"}>Offen: -{sumOpenInvoices.toLocaleString("de")}</span>
            <span className={"ml-1"}>
              ({new Date().toLocaleString("de-De", { month: "long" })} -
              {sumOpenCurrentMonth.toLocaleString("de")})
            </span>
          </div>
          <span></span>
        </div>
      ) : (
        <div className={"h-30 w-100"}>
          <Loading />
        </div>
      )}
    </>
  );
};
