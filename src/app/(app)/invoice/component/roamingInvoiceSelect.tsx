"use client";
import Link from "next/link";
import React, { useState } from "react";
import Button from "~/component/button";

export const RoamingInvoiceSelect = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  return (
    <div className="group relative min-w-30">
      <Button className={"w-full"} onClick={() => setDropdownOpen(!dropdownOpen)}>
        <span>E<PERSON>ellen</span>
        <svg
          className="ml-2 h-4 w-4 fill-current"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
        >
          <path d="M10 12l-6-6h12l-6 6z" />
        </svg>
      </Button>
      {dropdownOpen && (
        <div className="absolute right-0 z-10 mt-2 origin-top-right rounded-md shadow-lg">
          <div className="divide-y divide-gray-100 rounded-md bg-white ring-1 ring-black ring-opacity-5">
            <div className="whitespace-nowrap py-1">
              <Link href="/invoice/credit/create">
                <div className="block cursor-pointer px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                  Neue Gutschrift
                </div>
              </Link>
            </div>
            <div className="whitespace-nowrap py-1">
              <Link href="/invoice/create">
                <div className="block cursor-pointer px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                  Neue Rechnung
                </div>
              </Link>
            </div>
            <div className="whitespace-nowrap py-1">
              <Link href="/invoice/create/customInvoice">
                <div className="block cursor-pointer px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                  Custom Rechnung
                </div>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const UserInvoiceSelect = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  return (
    <div className="group relative min-w-30">
      <Button className={"w-full"} onClick={() => setDropdownOpen(!dropdownOpen)}>
        <span>Erstellen</span>
        <svg
          className="ml-2 h-4 w-4 fill-current"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
        >
          <path d="M10 12l-6-6h12l-6 6z" />
        </svg>
      </Button>
      {dropdownOpen && (
        <div className="absolute right-0 z-10 mt-2 origin-top-right rounded-md shadow-lg">
          <div className="divide-y divide-gray-100 rounded-md bg-white ring-1 ring-black ring-opacity-5">
            <div className="whitespace-nowrap py-1">
              <Link href="/invoice/user/create">
                <div className="block cursor-pointer px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                  Neue Mitarbeiter/Clubmitglieder Rechnung
                </div>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
