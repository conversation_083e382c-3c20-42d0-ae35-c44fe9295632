"use client";
import React, { createContext, Dispatch, SetStateAction, useEffect, useState } from "react";
import { InvoiceSummary } from "~/app/(app)/invoice/component/InvoiceSummary";
import { InvoiceEndpointType } from "~/app/api/invoice/route";
import { InvoiceTableWrapper } from "~/app/(app)/invoice/component/InvoiceTableWrapper";
import { Invoice } from "@prisma/client";

// Schritt 1: Definieren von TypeScript-Interfaces
export interface InvoiceDataContextType {
  invoiceData: InvoiceEndpointType[];
  setInvoiceData: React.Dispatch<React.SetStateAction<never[]>>;
}

const defaultSetInvoiceData: Dispatch<SetStateAction<never[]>> = () => {
  // Diese Funktion ist absichtlich leer und wird nur als Platzhalter verwendet
};
export const InvoiceDataContext = createContext<InvoiceDataContextType>({
  setInvoiceData: defaultSetInvoiceData,
  invoiceData: [],
});

export const InvoiceDataWrapper = ({
  userInvoices,
  filterCardHolder,
}: {
  userInvoices?: boolean;
  filterCardHolder?: boolean;
}) => {
  const [invoiceData, setInvoiceData] = useState([]);

  const loadInvoiceData = async () => {
    const response = await fetch(
      `${userInvoices ? "/api/invoice/user/cardHolder" : "/api/invoice"}`,
    );
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    try {
      let invoiceData = await response.json();
      if (filterCardHolder) {
        invoiceData = invoiceData.filter((invoice: Invoice) => invoice.userId);
      }
      setInvoiceData(invoiceData);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    void loadInvoiceData();
  }, []);

  return (
    <div className={"flex flex-col gap-3"}>
      <InvoiceDataContext.Provider value={{ invoiceData, setInvoiceData }}>
        {!userInvoices && !filterCardHolder && <InvoiceSummary />}
        <InvoiceTableWrapper />
      </InvoiceDataContext.Provider>
    </div>
  );
};
