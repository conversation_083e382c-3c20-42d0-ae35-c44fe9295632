"use client";

import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>ontext, useEffect, useState } from "react";

import type {
  ColGroupDef,
  ColDef,
  GridReadyEvent,
  SelectionChangedEvent,
  CellClickedEvent,
} from "ag-grid-community";

import Table, { filterParams } from "../../../../utils/table/table";
import Link from "next/link";
import type { ICellRendererParams } from "ag-grid-community";
import { HiMagnifyingGlass } from "react-icons/hi2";
import {
  dateRenderer,
  monthYearComparator,
  monthYearRenderer,
  twoDecimalPlacesFormatter,
  twoDecimalPlacesFormatterWithCurrency,
} from "~/utils/table/formatter";

import type { GridOptions } from "ag-grid-community";
import { FaCheckCircle, FaDownload } from "react-icons/fa";
import type { QontoTransaction } from ".prisma/client";
import type { GridApi } from "ag-grid-community";
import { MdAccountBalance } from "react-icons/md";
import { PaymentModal } from "~/app/(app)/qonto-transactions/component/PaymentModal";
import { InvoiceDataContext } from "~/app/(app)/invoice/component/InvoiceDataWrapper";
import { SiSepa } from "react-icons/si";
import { FaGears } from "react-icons/fa6";
import type {
  BaseWithValueColDefParams,
  ValueFormatterParams,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import Loading from "~/app/(app)/loading";
import { ImCross } from "react-icons/im";
import { TbMailCheck } from "react-icons/tb";
import Modal from "~/component/modal/modal";
import { InvoiceEndpointType } from "~/app/api/invoice/route";
import Button from "~/component/button";
import { useRouter } from "next/navigation";
import { Cdr, KindOfInvoice, StateOfInvoice } from "@prisma/client";
import { env } from "~/env";
import { Chip } from "~/app/(app)/component/chip";

interface InvoiceResult {
  id: string;
  message: string;
  errorMessages?: string;
}

export const InvoiceTableWrapper = () => {
  const { invoiceData, setInvoiceData } = useContext(InvoiceDataContext);
  const router = useRouter();

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isActionModalOpen, setIsActionModalOpen] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [transactions, setTransactions] = useState<QontoTransaction[]>([]);
  const [gridApi, setGridApi] = useState<GridApi | undefined>(undefined);
  const [rowData, setRowData] = useState<any[]>([]);
  const [invoiceResults, setInvoiceResults] = useState<InvoiceResult[]>([]);
  const [showActionChoices, setShowActionChoices] = useState<boolean>(false);

  const [actionType, setActionType] = useState<string>("");

  const handleGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    gridApi?.hideOverlay();
    setIsModalOpen(false);
  };

  const handleSave = async (invoiceId: string, paymentDate: Date, paymentAmount: number) => {
    // Hier können Sie die Logik zum Speichern der Zahlungsinformationen hinzufügen
    const res = await fetch(`api/invoice/${invoiceId}`, {
      method: "PATCH",
      body: JSON.stringify({
        paidOnDate: paymentDate,
        paidAmount: paymentAmount,
      }),
    });
    if (res.status === 200) {
      //void loadInvoiceData();
    }
    //todo
  };

  // remove the loadInvoiceData function since invoiceData is passed as prop now

  // Modify the useEffect function that calls loadInvoiceData to now update rowData when invoiceData prop changes
  useEffect(() => {
    const formattedData = invoiceData.map((row: any) => ({
      ...row,
      paidOnDateFilter: row.paidOnDate ? "Bezahlt" : "Nicht bezahlt",
      isRoaming: row?.contact ? "Ja" : "Nein",
    }));
    setRowData(formattedData);
    gridApi?.hideOverlay();
  }, [invoiceData]);

  const loadTransactionData = async () => {
    const fetchedData = await fetch("/api/qonto?onlyForMapping");
    try {
      const transactions = (await fetchedData.json()) as QontoTransaction[];
      const sorted = transactions.sort((a, b) => a.amount - b.amount);

      setTransactions(sorted);
    } catch (error) {
      console.log(error);
    }
  };

  const downloadInvoice = async (data: any) => {
    // Finden Sie die erste PDF-Datei
    const pdfFile = data.files.find((file: any) => file.contentType === "application/pdf");

    if (!pdfFile) {
      alert("Keine PDF-Datei gefunden");
      return;
    }

    // URL zu Ihrem API-Endpunkt
    const url = `/api/download/${pdfFile.id}`;

    // Laden Sie die Datei herunter
    const response = await fetch(url);
    if (response.ok) {
      const blob = await response.blob();

      // Erstellen Sie einen Link und klicken Sie darauf, um den Download zu starten
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${env.NEXT_PUBLIC_DEBUG == "true" ? "_DEV_" : ""}${pdfFile.name}`;
      link.click();

      // URL freigeben
      URL.revokeObjectURL(downloadUrl);
    } else {
      alert(`Fehler beim Download -> ${response.statusText}`);
    }
  };

  const ActionCellRenderer = (params: ICellRendererParams) => {
    if (params.node.group) {
      return <></>;
    }

    return (
      <>
        <Link
          title={"Rechnung in neuem Tab öffnen"}
          legacyBehavior={true}
          href={`/invoice/${params?.data?.id}`}
        >
          <a title={"Rechnung in neuem Tab öffnen"} target={"_blank"} rel={"noopener noreferer"}>
            <HiMagnifyingGlass title={"Rechnung in neuem Tab öffnen"} />
          </a>
        </Link>

        {!params?.data?.paidOnDate && (
          <>
            <MdAccountBalance
              className={"ml-5 cursor-pointer"}
              onClick={() => {
                openModal();
                setSelectedRow(params.data);
              }}
            />
            {(params.data?.user || params.data?.contact?.stripeCustomerId) &&
              params.data.kindOfInvoice == KindOfInvoice.INVOICE &&
              !params?.data?.paymentIntent && (
                <SiSepa
                  size={17}
                  className={"ml-5 cursor-pointer"}
                  onClick={async () => {
                    const result = await fetch(`/api/stripe/capture/sepa`, {
                      method: "POST",
                      body: JSON.stringify({ invoiceId: params?.data?.id }),
                    });
                    router.refresh();
                  }}
                />
              )}
          </>
        )}
        <FaDownload
          className={"ml-5 cursor-pointer"}
          onClick={() => {
            return downloadInvoice(params?.data);
          }}
        />
      </>
    );
  };

  const sendEmails = async (invoices: InvoiceEndpointType[]) => {
    const promises = invoices.map((invoice) =>
      fetch("/api/invoice/sendMail", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ invoiceId: invoice.id }),
        cache: "no-store",
      }).then((response) => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        companyName: invoice.contact?.companyName,
        invoiceEmail: invoice.contact?.invoiceMail,
        status: response.status,
      })),
    );

    try {
      const results = await Promise.all(promises);
      const formattedResults = results.map((result) => ({
        id: result.id,
        message:
          result.status === 200
            ? `Rechnungsnummer: Invoi${result.invoiceNumber} | ${result.companyName} gesendet an ${result.invoiceEmail}.`
            : `Fehler beim Senden der E-Mail für Rechnung ${result.invoiceNumber} an ${result.invoiceEmail}.`,
      }));
      setInvoiceResults(formattedResults);
      setIsActionModalOpen(true); // Öffne das Modal, um die Ergebnisse anzuzeigen
    } catch (error) {
      console.error("Fehler beim Senden der E-Mails:", error);
      setInvoiceResults([{ id: "N/A", message: "Ein Fehler ist aufgetreten." }]);
      setIsActionModalOpen(true);
    }
  };

  const prepareCPOInvoices = async (invoices: InvoiceEndpointType[]) => {
    if (invoices.find((invoice) => invoice.kindOfInvoice !== KindOfInvoice.CREDIT)) {
      setInvoiceResults([
        { id: "N/A", message: "Nur aus Gutschriften können CPO Rechnungen generiert werden." },
      ]);
      setIsActionModalOpen(true);
    } else {
      const creditInvoiceIds = invoices.map((invoice) => invoice.id);

      const result = await fetch("/api/invoice/prepare/customInvoice/multiple", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ creditInvoiceIds: creditInvoiceIds }),
        cache: "no-store",
      });

      const data = await result.json();

      setInvoiceResults([{ ...data }]);
      setIsActionModalOpen(true); // Modal öffnen, um die Ergebnisse anzuzeigen
    }
  };
  const finalizeInvoices = async (invoices: InvoiceEndpointType[]) => {
    const results = [];

    for (const invoice of invoices) {
      try {
        const response = await fetch(`/api/invoice/finish/${invoice.id}`, {
          method: "PUT",
          cache: "no-store",
        });

        const data = await response.json();

        results.push({
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          status: response.status,
          response: data, // Speichern der Antwortdaten für den Zugriff im nächsten Schritt
        });
      } catch (error) {
        console.error(`Fehler beim Abschließen der Rechnung ${invoice.id}:`, error);
        results.push({
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          status: "error",
          response: { invoiceNumber: invoice.invoiceNumber }, // Fehlerdetails können hier hinzugefügt werden
        });
      }
    }

    const formattedResults = results.map((result) => {
      return {
        id: result.id,
        message:
          result.status === 200
            ? `Rechnung ${result.response.invoiceNumber} festgeschrieben.`
            : `Fehler beim Abschließen der Rechnung ${result.response.invoiceNumber}.`,
      };
    });

    setInvoiceResults(formattedResults);
    setIsActionModalOpen(true); // Modal öffnen, um die Ergebnisse anzuzeigen
  };

  const performAction = async () => {
    const selectedNodes = gridApi?.getSelectedNodes();
    const selectedData = selectedNodes?.map((node) => node.data);

    if (!selectedData || selectedData.length === 0) {
      alert("Bitte wählen Sie mindestens eine Rechnung aus.");
      return;
    }

    switch (actionType) {
      case "finalizeDraft":
        // Logik, um Entwürfe abzuschließen
        await finalizeInvoices(selectedData.map((data) => data));
        break;
      case "sendEmail":
        // Logik, um Rechnungen per E-Mail zu senden
        await sendEmails(selectedData.map((data) => data));
        break;
      case "prepareCPOInvoice":
        await prepareCPOInvoices(selectedData.map((data) => data));
        break;
      default:
        alert("Bitte wählen Sie eine gültige Aktion.");
    }
  };

  const [columnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    {
      headerCheckboxSelection: true,
      checkboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true, // Optional: Wählt nur gefilterte Reihen aus
      width: 50,
      lockPosition: true,
      suppressMenu: true,
      suppressMovable: true,
      pinned: "left", // Optional: Spalte fixieren, wenn horizontal gescrollt wird
    },
    {
      field: "id",
      headerName: "Validierung",
      cellRenderer: (params: BaseWithValueColDefParams) => {
        if (params?.node?.group) {
          // Wenn es sich um eine Gruppe handelt
          const allLeafChildren = params.node.allLeafChildren;

          let hasDifference = false;

          // Iteriere durch alle Gruppenelemente
          allLeafChildren.forEach((childNode) => {
            const data = childNode.data;
            const cdrVolumeSum = data?.creditCdrs?.reduce(
              (sum: number, creditCdr: Cdr) => sum + (creditCdr.Volume || 0),
              0,
            );

            if (
              data?.kindOfInvoice === KindOfInvoice.CREDIT &&
              (data?.stateOfInvoice == StateOfInvoice.CREATED ||
                data?.stateOfInvoice == StateOfInvoice.PREVIEW)
            ) {
              if (Math.floor(cdrVolumeSum) !== Math.floor(data?.sumKwh)) {
                hasDifference = true;
              }
            }
          });

          if (hasDifference) {
            return (
              <Chip
                title={"Mindestens eine Differenz in den Gruppenelementen vorhanden"}
                label={"Problem"}
                className={"bg-red-300"}
              />
            );
          }

          return <Chip label={"OK"} className={"bg-green-300"} />;
        } else {
          const cdrVolumeSum = params?.data?.creditCdrs?.reduce(
            (sum: number, creditCdr: Cdr) => sum + (creditCdr.Volume || 0),
            0,
          );

          if (
            params?.data?.kindOfInvoice === KindOfInvoice.CREDIT &&
            (params?.data?.stateOfInvoice == StateOfInvoice.CREATED ||
              params?.data?.stateOfInvoice == StateOfInvoice.PREVIEW)
          ) {
            if (Math.floor(cdrVolumeSum) == Math.floor(params?.data?.sumKwh)) {
              return <Chip label={"OK"} className={"bg-green-300"} />;
            } else {
              return (
                <Chip
                  title={"Summe kWh der CDRs passt nicht zur Summe in der Gutschrift"}
                  label={`${Math.floor(params?.data?.sumKwh) - Math.floor(cdrVolumeSum)} kWh`}
                  className={"bg-red-300"}
                />
              );
            }
          }
          return "";
        }
      },
    },
    {
      headerName: "Stripe Status",
      field: "paymentIntent.status",
      filter: "agSetColumnFilter",
      cellRenderer: (params: BaseWithValueColDefParams) => {
        if (params?.data?.paymentIntent) {
          if (params?.data?.paymentIntent.status == "succeeded") {
            return (
              <span>
                <FaCheckCircle size={18} className={"mr-1 text-green-500"} />
                Erfolgreich
              </span>
            );
          }
          if (params?.data?.paymentIntent.status == "processing") {
            return (
              <span>
                <FaGears size={18} className={"mr-1 text-gray-500"} />
                In Bearbeitung
              </span>
            );
          }
          if (params?.data?.paymentIntent.status == "failed") {
            return (
              <span>
                <ImCross size={18} className={"mr-1 text-red-500"} /> Fehlgeschlagen
              </span>
            );
          }
        } else {
          return "";
        }
      },
    },
    { field: "invoiceNumber", headerName: "Rechnungsnummer", initialWidth: 250 },
    { field: "invoiceNumber", headerName: "reference", initialWidth: 250, hide: true },
    {
      field: "contact.name",
      headerName: "Kunde",
      valueFormatter: (params: ValueFormatterParams) => {
        if (params?.data?.contact) {
          return params.data.contact.name;
        } else if (params?.data?.user && !params?.data?.user?.companyName) {
          return params.data.user.name + " " + params.data.user.lastName;
        } else if (params?.data?.user && params?.data?.user?.companyName) {
          return `${params.data.user.name} ${params.data.user.lastName} (${params.data.user.companyName})`;
        }
      },
    },
    { field: "kindOfInvoice", headerName: "Rechnungsart", hide: false },
    { field: "stateOfInvoice", headerName: "Status", hide: false },

    {
      field: "invoiceDate",
      headerName: "Rechnungsdatum",
      cellRenderer: dateRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
    },
    {
      headerName: "E-Mail",
      initialWidth: 90,
      field: "sendAsMail",
      cellRenderer: (params: BaseWithValueColDefParams) => {
        if (params?.data?.sendAsMail) {
          return (
            <TbMailCheck
              size={16}
              className={"mr-1 text-green-300"}
              title={params?.data?.history ?? "No history"}
            />
          );
        }
        return;
      },
    },
    {
      field: "paidOnDate",
      headerName: "Bezahlt am",
      cellRenderer: dateRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
      hide: true,
    },
    {
      field: "sumNet",
      headerName: "Netto Summe",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      hide: true,
    },
    {
      field: "sumGross",
      headerName: "Saldoausgleich",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      hide: false,
      valueGetter: (params: ValueGetterParams) => {
        switch (params.data.kindOfInvoice) {
          case KindOfInvoice.INVOICE:
            return params.data.sumGross;
          case KindOfInvoice.STORNO:
            return params.data.sumGross;
          case KindOfInvoice.CREDIT:
            return params.data.sumGross * -1;
          case KindOfInvoice.CREDIT_STORNO:
            return params.data.sumGross * -1;
        }
      },
    },
    { field: "contact.iban", headerName: "IBAN" },
    {
      field: "contact.cpo",
      headerName: "Vertragsverhältnis",
      valueGetter: (params) => {
        if (params.data?.contact?.cpo) {
          return "CPO";
        }
        if (!params.data?.contact && params.data?.user) {
          return "Mitarbeiter/Club";
        }
        if (!params.data?.contact.cpo && !params.data?.user) {
          return "EMP";
        }
        return "Unbekannt";
      },
    },
    {
      field: "sumTax",
      headerName: "Mwst.",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      hide: true,
    },
    {
      field: "sumKwh",
      headerName: "kWh",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatter,
    },
    {
      field: "sumSession",
      headerName: "Session",
      aggFunc: "sum",
      enableValue: true,
    },

    {
      headerName: "Bezahlt",
      field: "paidOnDateFilter",
      filter: "agSetColumnFilter",
      filterParams: {
        values: ["Bezahlt", "Nicht bezahlt"],
      },
      hide: true,
    },
    {
      headerName: "Roaming",
      field: "isRoaming",
      filter: "agSetColumnFilter",
      filterParams: {
        values: ["Ja", "Nein"],
      },
    },
    {
      field: "MonthYear",
      headerName: "Monat/Jahr",
      enableRowGroup: true,
      valueGetter: (params) => {
        const date = new Date(params?.data?.invoiceDate);
        const month = date.getMonth() + 1; // Da Monate von 0 bis 11 indexiert sind, fügen wir 1 hinzu
        const year = date.getFullYear();
        return `${month}/${year}`; // Dies gibt z.B. "5/2023" für Mai 2023 zurück
      },
      comparator: monthYearComparator,
      cellRenderer: monthYearRenderer,
    },

    { headerName: "Action", cellRenderer: ActionCellRenderer },
    {
      field: "contact.name",
      hide: true,
      headerName: "beneficiary_name",
      valueFormatter: (params: ValueFormatterParams) => {
        if (params?.data?.contact) {
          return params.data.contact.name;
        } else if (params?.data?.user) {
          return params.data.user.name + " " + params.data.user.lastName;
        }
      },
    },
    {
      field: "sumGross",
      headerName: "amount",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      hide: true,
    },
    {
      field: "sumGross",
      headerName: "currency",
      valueGetter: () => "EUR",
      hide: true,
    },
  ]);

  const gridOptions: GridOptions = {
    overlayNoRowsTemplate: "Keine Rechnungen vorhanden",
    rowModelType: "clientSide",
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    groupIncludeFooter: true,
    // includes grand total
    groupIncludeTotalFooter: true,
    rowHeight: 30,
    sideBar: {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          minWidth: 225,
          maxWidth: 225,
          width: 225,
        },
        {
          id: "filters",
          labelDefault: "Filters",
          labelKey: "filters",
          iconKey: "filter",
          toolPanel: "agFiltersToolPanel",
          minWidth: 180,
          maxWidth: 400,
          width: 250,
        },
      ],
      position: "right",
      defaultToolPanel: "",
    },
  };

  useEffect(() => {
    if (isModalOpen) {
      loadTransactionData();
    } else {
      if (gridApi) {
        gridApi.showLoadingOverlay();
      }
      // TODOloadInvoiceData();
    }
  }, [isModalOpen]);

  const onSelectionChanged = () => {
    if (gridApi) {
      setShowActionChoices(gridApi?.getSelectedRows()?.length > 0 ?? false);
    }
  };

  return (
    <>
      <div className={"h-[800px] pb-[50px]"}>
        {!invoiceData && <Loading />}

        <Table
          headerLeft={
            showActionChoices ? (
              <div className="mb-2 flex w-1/2 flex-col justify-end gap-1 sm:mb-0 sm:mr-2 sm:w-full sm:flex-row sm:items-center ">
                <select
                  className={"btn"}
                  value={actionType}
                  onChange={(e) => setActionType(e.target.value)}
                >
                  <option value="">Wählen Sie eine Aktion</option>
                  <option value="finalizeDraft">Entwurf abschließen</option>
                  <option value="sendEmail">Rechnungen per Mail versenden</option>
                  <option value="prepareCPOInvoice">CPO Rechnungen entwerfen</option>
                </select>
                <Button onClick={performAction}>Aktion ausführen</Button>
              </div>
            ) : (
              <></>
            )
          }
          gridId={"invoice"}
          groupIncludeFooter={true}
          onGridReady={handleGridReady}
          groupIncludeTotalFooter={true}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={onSelectionChanged}
          gridOptions={gridOptions}
          onFirstDataRendered={(params: GridReadyEvent) => {
            const bezahltFilterComponent = params.api.getFilterInstance("paidOnDateFilter");
            const kindOfInvoiceFilterComponent = params.api.getFilterInstance("kindOfInvoice");
            const stateOfInvoiceFilterComponent = params.api.getFilterInstance("stateOfInvoice");
            if (bezahltFilterComponent) {
              bezahltFilterComponent.setModel({
                values: ["Nicht bezahlt"],
              });
            }
            if (kindOfInvoiceFilterComponent) {
              kindOfInvoiceFilterComponent.setModel({
                values: ["INVOICE", "CREDIT"],
              });
            }
            if (stateOfInvoiceFilterComponent) {
              stateOfInvoiceFilterComponent.setModel({
                values: ["CREATED"],
              });
            }
            params.api.onFilterChanged();
          }}
        />
      </div>

      <Modal title="Status" isOpen={isActionModalOpen} onClose={() => setIsActionModalOpen(false)}>
        <div className="p-4">
          <h3 className="mb-4"></h3>
          {invoiceResults.map((result: InvoiceResult) => (
            <>
              <p key={result.id}>{`${result.message}`}</p>
              {result?.errorMessages && <p key={result.id + "e"}>{`${result.errorMessages}`}</p>}
            </>
          ))}
        </div>
      </Modal>

      <PaymentModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSave={handleSave}
        selectedRow={selectedRow}
        transactions={transactions}
      />
    </>
  );
};
