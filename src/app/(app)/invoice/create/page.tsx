"use client";
import Card from "~/component/card";
import React, { startTransition, useEffect, useState } from "react";
import type { Contact } from "@prisma/client";

import useSWRMutation from "swr/mutation";
import "react-datepicker/dist/react-datepicker.css";

import de from "date-fns/locale/de";

registerLocale("de", de);
setDefaultLocale("de");

import DatePicker, { registerLocale, setDefaultLocale } from "react-datepicker";
import type { SubmitHandler } from "react-hook-form";
import { Controller, useForm } from "react-hook-form";
import { getFirstDayOfLastMonth, getLastDayOfLastMonth } from "~/utils/date/date";
import { useRouter } from "next/navigation";
import { ContactType, RangeMode } from "~/server/types/types";
import Button from "~/component/button";

const sendRequest = async (
  url: string,
  { arg }: { arg: { startDate: Date; endDate: Date; kindOfContact: ContactType } },
): Promise<Contact[]> => {
  const res = await fetch(url, {
    method: "POST",
    body: JSON.stringify(arg),
  });
  return (await res.json()) as Contact[];
};

export type InvoiceFormValues = {
  rangeMod: RangeMode;
  contactIds: string[];
  contactType: ContactType;
  dateRange: {
    0: Date;
    1: Date;
  };
};

const Page = () => {
  const router = useRouter();
  const intialFormData = {
    defaultValues: {
      rangeMod: RangeMode.LAST_MONTH,
      kindOfContact: ContactType.EMP,
      dateRange: {
        0: getFirstDayOfLastMonth(),
        1: getLastDayOfLastMonth(),
      },
      contactIds: [],
    },
  };

  const {
    register,
    handleSubmit,
    watch,
    control,
    getValues,
    formState: {},
  } = useForm<InvoiceFormValues>(intialFormData);

  const [, setContactType] = useState<ContactType>(ContactType.EMP);

  const rangeMod = watch("rangeMod"); // Watch auf das Feld "rangeMod"

  const { data: contacts, trigger: triggerContact } = useSWRMutation(
    "/api/contact/withCdrs",
    sendRequest,
  );

  const selectContactType = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    setContactType(e.target.value as ContactType);
    const formValues = getValues();

    await triggerContact({
      startDate: new Date(formValues.dateRange[0]),
      endDate: new Date(formValues.dateRange[1]),
      kindOfContact: e.target.value as ContactType, // Dieser Parameter wird jetzt mitgesendet
    });
  };

  const changeDateMode = async (mode: RangeMode) => {
    if (mode === RangeMode.ALL) {
      await triggerContact({
        startDate: new Date("01.01.2024"),
        endDate: new Date(),
        kindOfContact: getValues().contactType,
      });
    } else if (mode === RangeMode.LAST_MONTH) {
      await triggerContact({
        startDate: new Date("01.01.2024"),
        endDate: getLastDayOfLastMonth(),
        kindOfContact: getValues().contactType,
      });
    } else {
      const formValues = getValues();
      await triggerContact({
        startDate: new Date(formValues.dateRange[0]),
        endDate: new Date(formValues.dateRange[1]),
        kindOfContact: getValues().contactType,
      });
    }
  };

  useEffect(() => {
    // Trigger Kontaktanfragen oder andere Aktionen basierend auf rangeMod
    void changeDateMode(rangeMod);
  }, [rangeMod]); // Abhängigkeit von rangeMod, um Änderungen zu überwachen

  const changeDateRange = async (dates: [Date | null, Date | null]) => {
    if (dates[0] == null || dates[1] == null) return;

    const endDate = new Date(dates[1]);
    endDate.setDate(endDate.getDate() + 1);

    await triggerContact({
      startDate: dates[0],
      endDate: endDate,
      kindOfContact: getValues().contactType,
    });
  };

  const generatePreview: SubmitHandler<InvoiceFormValues> = async (formData: InvoiceFormValues) => {
    const res = await fetch("/api/invoice/prepare", {
      method: "POST",
      body: JSON.stringify(formData),
    });
    if (res.status == 200) {
      const { invoiceId } = await res.json();
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push(`/invoice`);
      });
    }
    return;
  };

  const lastMonth = new Date();

  // Einen Monat abziehen
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  return (
    <Card>
      <div className={"flex-auto p-6 pt-0"}>
        <h2 className={"h2"}>Rechnung erstellen</h2>
        <form className={"-mx-3 flex flex-wrap"} onSubmit={handleSubmit(generatePreview)}>
          <div className={"w-full sm:w-1/2"}>
            <div className={"max-w-full flex-0"}>
              <label
                className="mb-2 ml-1 mt-6 w-full text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="cpo-select"
              >
                Daten eingrenzen
              </label>
              <br />
              <label htmlFor="range_mod_all">
                <input
                  {...register("rangeMod")}
                  type="radio"
                  id="range_mod_all"
                  name="rangeMod"
                  className={"mr-2"}
                  value={RangeMode.ALL}
                />
                Alle Daten
              </label>
              <br />
              <label htmlFor="range_mod_range">
                <input
                  {...register("rangeMod")}
                  type="radio"
                  id="range_mod_range"
                  name="rangeMod"
                  className={"mr-2"}
                  value={RangeMode.LAST_MONTH}
                />
                Alle Daten bis Ende {lastMonth.toLocaleString("de-De", { month: "long" })}
              </label>
              <br />
              <label htmlFor="range_mod_daterange">
                <input
                  {...register("rangeMod")}
                  type="radio"
                  id="range_mod_daterange"
                  name="rangeMod"
                  className={"mr-2"}
                  value={RangeMode.DATE_RANGE}
                />
                Zeitraum auswählen
              </label>
              <br />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Language"
              >
                Vertragsverhältnis
              </label>
              <select
                {...register("contactType")}
                onChange={selectContactType}
                className="form-select focus:border-primary m-0 block w-full
                          appearance-none rounded border border-solid
                          border-gray-300 bg-white bg-clip-padding
                          bg-no-repeat px-3 py-1.5 text-base
                          font-normal text-gray-700
                          transition ease-in-out
                          focus:bg-white focus:text-gray-700 focus:outline-none"
                aria-label="Default select example"
              >
                <option key={ContactType.EMP} value={ContactType.EMP}>
                  {ContactType.EMP}
                </option>
                <option key={ContactType.TOKEN_GROUP} value={ContactType.TOKEN_GROUP}>
                  {ContactType.TOKEN_GROUP}
                </option>
              </select>

              {rangeMod === RangeMode.DATE_RANGE && (
                <>
                  <label
                    className="mb-2 ml-1 mt-6 block w-full text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="datepicker-invoice"
                  >
                    Zeitraum festlegen
                  </label>
                  <Controller
                    control={control}
                    name={"dateRange"}
                    render={({ field: { onChange, value } }) => {
                      return (
                        <DatePicker
                          className={
                            "mb-4 block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 pr-8 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                          }
                          wrapperClassName={"w-50"}
                          id={"datepicker-invoice"}
                          clearButtonClassName={"mt-[-8px]"}
                          selectsRange={true}
                          startDate={value[0]}
                          endDate={value[1]}
                          onChange={(dates: [Date | null, Date | null]): void => {
                            void changeDateRange(dates);
                            onChange(dates);
                          }}
                          isClearable={true}
                          locale="de"
                          dateFormat={["P", "PP", "PPP", "MMM dd yyyy", "dd MMMM yyyy"]}
                        />
                      );
                    }}
                  />
                </>
              )}
            </div>
          </div>
          <div className={"w-full sm:w-1/2"}>
            <>
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="cpo-select"
              >
                Kontakt
              </label>
              <select
                {...register("contactIds")}
                multiple
                className="form-select m-0 block h-60 w-full appearance-none
      rounded border border-solid
      border-gray-300 bg-white bg-clip-padding
      bg-no-repeat px-3 py-1.5
      text-base font-normal
      text-gray-700 transition ease-in-out
      focus:border-blue-600 focus:bg-white focus:text-gray-700 focus:outline-none"
                aria-label="Default select example"
              >
                {contacts &&
                  contacts.map((contact) => (
                    <option key={contact.id} value={contact.id}>
                      {contact.name}
                    </option>
                  ))}
              </select>
            </>
            <div className={"mt-5 block text-right"}>
              <Button>Rechnung erstellen</Button>
            </div>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default Page;
