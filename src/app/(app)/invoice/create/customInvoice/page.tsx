import Card from "~/component/card";

import prisma from "~/server/db/prisma";
import { CustomInvoiceForm } from "~/app/(app)/invoice/component/CustomInvoiceForm";

const getContacts = async () => {
  const contacts = await prisma.contact.findMany({
    include: { contactAddress: true },
  });
  return contacts;
};

const OperationInvoicePage = async () => {
  const contacts = await getContacts();
  return (
    <Card>
      <CustomInvoiceForm contacts={contacts} />
    </Card>
  );
};

export default OperationInvoicePage;
