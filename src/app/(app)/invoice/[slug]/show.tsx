"use client";
import React, { startTransition, useEffect, useState } from "react";

import { Prisma, FileRef, Invoice, Role } from "@prisma/client";
import Card from "~/component/card";
import { getContactAdressByDate } from "~/utils/contact/getContactAdressByDate";
import type { InvoicePosition, ContactAddress } from "@prisma/client";
import Table from "~/utils/table/table";
import type { GridOptions } from "ag-grid-community";

import Link from "next/link";
import { KindOfInvoice, StateOfInvoice } from "@prisma/client";
import Download from "~/component/download/download";

import { FaDownload, FaFileCsv, FaPaperPlane } from "react-icons/fa";
import {
  MdOutlineCancel,
  MdPriceCheck,
  MdAccountBalance,
  MdOutlineDeleteForever,
} from "react-icons/md";

import { convertUtcToCet } from "~/utils/date/date";
import { GiArchiveResearch } from "react-icons/gi";
import { FiLoader } from "react-icons/fi";
import { RiMailCheckLine } from "react-icons/ri";
import { useRouter } from "next/navigation";
import Loading from "~/app/(app)/loading";
import Modal from "~/component/modal/modal";
import { TbLinkPlus, TbMailExclamation } from "react-icons/tb";

export type InvoiceWithIncludes = Prisma.InvoiceGetPayload<{
  include: {
    invoicePositions: {
      include: {
        tarif: true;
      };
    };
    invoiceChilds: true;
    invoiceParent: true;
    contact: {
      include: {
        contactAddress: true;
      };
    };
    user: { include: { address: true } };
    cdrs: true;
    creditCdrs: true;
    files: true;
    bankTransactions: true;
    contract: true;
  };
}>;

interface Props {
  slug: string;
}

const Show = ({ slug }: Props) => {
  const router = useRouter();

  const [invoice, setInvoice] = useState<InvoiceWithIncludes>();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [contactAddress, setContactAdress] = useState<ContactAddress>();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [customMailText, setCustomMailText] = useState<string>("");

  const [showModal, setShowModal] = React.useState(false);

  const [sendingEmail, setSendingEmail] = useState(false);
  const [finishLoading, setFinishLoading] = useState<boolean>(false);
  const getInvoice = async () => {
    const rowData = await fetch(`/api/invoice/${slug}`, {
      method: "GET",
      cache: "no-store",
    });
    const invoice = (await rowData.json()) as InvoiceWithIncludes;
    setFinishLoading(false);
    setInvoice(invoice);
    if (invoice && invoice.invoiceDate) {
      //if contact, set adddress from contact if user use user address
      let address;
      if (invoice.contact) {
        address = getContactAdressByDate(invoice?.contact?.contactAddress, invoice.invoiceDate);
      } else if (invoice?.user) {
        address = getContactAdressByDate(invoice.user?.address, invoice.invoiceDate);
      }
      if (address) {
        setContactAdress(address);
      }
    }
  };

  useEffect(() => {
    getInvoice();
  }, []);

  if (!slug) {
    return <>Missing params</>;
  }

  const finishInvoice = async () => {
    if (!invoice) {
      // ToDo wrote some error
      return;
    }
    setFinishLoading(true);
    const res = await fetch(`/api/invoice/finish/${invoice.id}`, {
      method: "PUT",
      cache: "no-store",
    });
    if (res.status == 200) {
      await getInvoice();
    }
  };

  const deleteInvoice = async () => {
    if (!invoice) {
      return;
    }
    const res = await fetch(`/api/invoice/${invoice.id}`, {
      method: "DELETE",
      cache: "no-store",
    });
    if (res.status == 200) {
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push(`/invoice/`);
        router.refresh();
      });
    }
  };

  const invoiceDate = new Date(invoice?.invoiceDate || new Date());

  const columnDefs = [
    {
      field: "",
      headerCheckboxSelection: true,
      checkboxSelection: true,
    },
    { field: "CDR_ID", headerName: "CDR_ID", aggFunc: "count" },
    { field: "Start_datetime", headerName: "Start_datetime" },
    { field: "End_datetime", headerName: "End_datetime" },
    {
      field: "Authentication_ID",
      headerName: "Authentication_ID",
      enableRowGroup: true,
    },
    { field: "Volume", headerName: "Volume", aggFunc: "sum" },
    { field: "Duration", headerName: "Duration" },
    { field: "Service_Provider_ID", headerName: "Service_Provider_ID" },
    { field: "Calculated_Cost", headerName: "Calculated Cost", aggFunc: "sum" },
    { field: "EnergyCosts", headerName: "Energy Cost", aggFunc: "sum" },
    { field: "Parking_Time_Cost", headerName: "Blocking Fee", aggFunc: "sum" },
    {
      field: "Charge_Point_Address",
      headerName: "Address",
      enableRowGroup: true,
    },
    {
      field: "Charge_Point_ID",
      headerName: "Charge_Point_ID",
      enableRowGroup: true,
    },
    { field: "billable", headerName: "Abrechenbar", enableRowGroup: true, aggFunc: "count" },
    { field: "creditPayout.payoutSum", headerName: "Payout Sum", aggFunc: "sum" },
    { field: "creditPayout.stripeFee", headerName: "Gebühr", aggFunc: "sum" },
    { field: "transactionId", headerName: "OCPP TransactionsID", aggFunc: "count" },
  ];

  const gridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      enableRowGroup: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 30,
  };

  const servicePeriodFrom = new Date(invoice?.servicePeriodFrom || "");
  const servicePeriodTo = new Date(invoice?.servicePeriodTo || "");

  const cancelInvoice = async () => {
    const res = await fetch(`/api/invoice/cancel/${slug}`, {
      method: "GET",
      cache: "no-store",
    });
    if (res.status == 200) {
      await getInvoice();
    }
  };

  const generateCsvFile = async () => {
    const res = await fetch(`/api/invoice/${slug}/generateCSV`, {
      method: "GET",
      cache: "no-store",
    });
    if (res.status == 200) {
      await getInvoice();
    }
  };

  const setReadFlag = async () => {
    setSendingEmail(true);
    const response = await fetch("/api/invoice/sendMail", {
      method: "POST",
      body: JSON.stringify({ invoiceId: slug, customMailText: "", onlySetSendFlag: true }),
    });
    if (response.status == 200) {
      await getInvoice();
    }
    setSendingEmail(false);
  };

  const sendInvoice = async () => {
    setSendingEmail(true);
    const sendMailUrl =
      invoice?.user?.role == Role.CARD_HOLDER
        ? "/api/invoice/sendMail/user"
        : "/api/invoice/sendMail";
    void (await fetch(sendMailUrl, {
      method: "POST",
      body: JSON.stringify({ invoiceId: slug, customMailText: customMailText }),
      cache: "no-store",
    }));
    setSendingEmail(false);
    setDialogOpen(false);
    await getInvoice();
  };

  const nettoSum = invoice?.sumNet ?? 0;
  const taxAmount = invoice?.sumTax ?? 0;
  const nettoWithTax = invoice?.sumGross ?? 0;
  let taxRate = contactAddress?.isNetInvoice ? "0" : contactAddress?.invoiceTaxRate;
  if (invoice?.sumTax != 0) {
    taxRate = "19";
  }
  if (!invoice) {
    return <Loading />;
  }
  return (
    <>
      <h1>
        {invoice?.kindOfInvoice == KindOfInvoice.INVOICE && "Rechnung"}
        {invoice?.kindOfInvoice == KindOfInvoice.STORNO && "Stornorechnung"}
        {invoice?.kindOfInvoice == KindOfInvoice.CREDIT && "Gutschrift"}
      </h1>
      <div className={"flex"}>
        <Card header_left={""} className={"mt-5 max-w-[1000px] p-10"}>
          <div className={"flex justify-between"}>
            {invoice?.stateOfInvoice == StateOfInvoice.CANCEL ? (
              <div
                className={
                  "mb-10 ml-[-63px] flex flex-col bg-red-700 px-16 py-2 text-center text-xl text-white "
                }
              >
                <span>Storniert</span>
                {invoice?.invoiceChilds.map((x: Invoice) => {
                  if (x.kindOfInvoice == KindOfInvoice.STORNO) {
                    return (
                      <Link className={"text-sm"} key={x.id} href={`/invoice/${x.id}`}>
                        Stornorechnung: {x.invoiceNumber}
                      </Link>
                    );
                  }
                  return <></>;
                })}
              </div>
            ) : (
              <div></div>
            )}
            {invoice?.kindOfInvoice == KindOfInvoice.STORNO ? (
              <div
                className={
                  "mb-10 flex flex-col bg-stone-800 px-16 py-2 text-center text-lg text-white "
                }
              >
                <span>Storno</span>
                <span>für</span>
                {invoice.kindOfInvoice == KindOfInvoice.STORNO ? (
                  <Link className={"text-sm"} href={`/invoice/${invoice?.invoiceParent?.id}`}>
                    {invoice?.invoiceParent?.invoiceNumber}
                  </Link>
                ) : (
                  <></>
                )}
              </div>
            ) : (
              <div></div>
            )}

            <div className={"text-2xl font-bold"}>
              {invoice?.kindOfInvoice == KindOfInvoice.INVOICE && "Rechnung"}
              {invoice?.kindOfInvoice == KindOfInvoice.STORNO && "Stornorechnung"}
              {invoice?.kindOfInvoice == KindOfInvoice.CREDIT && "Gutschrift"}
            </div>
          </div>
          <div>
            <span className={"font-bold"}>Eulektro GmbH</span> | Werderstraße 69 | 28199 Bremen
          </div>

          <div className={"mt-5 flex flex-col"}>
            {invoice?.contact && <span>{invoice?.contact.companyName}</span>}
            {invoice?.user && !invoice?.user?.companyName &&(
              <span>
                {invoice?.user.name} {invoice?.user.lastName}
              </span>
            )}
              {invoice?.user && invoice?.user?.companyName && (
                  <span>
                {invoice?.user?.companyName}
              </span>
              )}

            {!contactAddress && (
              <span className={"text-red-500"}>Warning: no contact address found</span>
            )}
            <span>
              {contactAddress?.street} {contactAddress?.streetNr}
            </span>
            <span>
              {contactAddress?.zip} {contactAddress?.city}
            </span>
            <span>{contactAddress?.country}</span>
          </div>

          <div className={"mt-10 flex flex-row "}>
            <div className={"basis-1/5"}>
              <div className={"font-bold"}>Betreff</div>
              <div>{invoice?.subject}</div>
            </div>
            <div className={"basis-1/5"}>
              <div className={"font-bold"}>
                {invoice?.kindOfInvoice == KindOfInvoice.CREDIT
                  ? "Gutschrift-Nr.:"
                  : "Rechnungsnummer"}
              </div>
              <div>{invoice?.invoiceNumber ? invoice.invoiceNumber : "Preview"}</div>
            </div>
            <div className={"basis-1/5"}>
              <div className={"font-bold"}>
                {invoice?.kindOfInvoice === KindOfInvoice.CREDIT
                  ? "Lieferantennummer"
                  : "Kundennummer"}
              </div>
              {invoice?.contact && (
                <div>
                  {invoice?.kindOfInvoice === KindOfInvoice.CREDIT
                    ? invoice?.contact?.supplierNumber
                    : invoice?.contact?.customerNumber}
                </div>
              )}
              {invoice.user && <div>{invoice.user?.id}</div>}
            </div>
            <div className={"basis-1/5"}>
              <div className={"font-bold"}>Leistungszeitraum</div>
              <div>
                {convertUtcToCet(servicePeriodFrom)} - {convertUtcToCet(servicePeriodTo)}
              </div>
            </div>
            <div className={"basis-1/5"}>
              <div className={"font-bold"}>Datum</div>
              <div>{invoiceDate.toLocaleDateString("de-DE")}</div>
            </div>
          </div>
          <div className={"mt-10 table  min-w-full"}>
            <div className={"table-row bg-gray-200 font-bold"}>
              <div className={"table-cell p-2"}>Pos.</div>
              <div className={"table-cell min-w-48 p-2"}>Beschreibung/Tarif</div>
              <div className={"table-cell p-2"}>Menge</div>
              <div className={"table-cell p-2"}>Einheit</div>
              <div className={"table-cell p-2"}>Mwst.</div>
              <div className={"table-cell p-2"}>Einzelpreis in EUR</div>
              <div className={"table-cell p-2"}>Gesamtpreis</div>
            </div>
            {invoice?.invoicePositions.map((pos, index) => {
              return (
                <div key={pos.id} className={"table-row"}>
                  <div className={"table-cell p-2"}>{pos.pos}</div>
                  <div className={"table-cell p-2"}>{pos.description}</div>
                  <div className={"table-cell p-2"}>{pos.amount.toLocaleString("de-DE")}</div>
                  <div className={"table-cell p-2"}>{pos.unit}</div>
                  <div className={"table-cell p-2"}>{pos.taxRate}%</div>
                  <div className={"table-cell p-2 text-right"}>
                    {pos.unitPrice.toLocaleString("de-DE", {
                      style: "currency",
                      currency: "EUR",
                    })}
                  </div>
                  <div className={"table-cell p-2 text-right"}>
                    {pos.sumNet.toLocaleString("de-DE", {
                      style: "currency",
                      currency: "EUR",
                    })}
                  </div>
                </div>
              );
            })}
            <div className={"table-row "}>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
              <div className={"table-cell bg-gray-200 p-[1px]"}></div>
            </div>

            <div className={"table-row"}>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}>Netto</div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2 text-right"}>
                {nettoSum.toLocaleString("de-DE", {
                  style: "currency",
                  currency: "EUR",
                })}
              </div>
            </div>
            <div className={"table-row"}>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}>zzgl. {taxRate}% MwSt.</div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2 text-right"}>
                {taxAmount.toLocaleString("de-DE", {
                  style: "currency",
                  currency: "EUR",
                })}
              </div>
            </div>
            <div className={"table-row"}>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2 font-bold"}>Summe</div>
              <div className={"table-cell p-2"}></div>
              <div className={"table-cell p-2 text-right"}>
                {nettoWithTax.toLocaleString("de-DE", {
                  style: "currency",
                  currency: "EUR",
                })}
              </div>
            </div>
          </div>

          {contactAddress?.country != "Deutschland" && (
            <div className={"mt-10 flex w-full"}>
              „Steuerschuldnerschaft des Leistungsempfängers” gemäß § 13b UStG” (Reverse Charge).
            </div>
          )}
          <div className={"mt-10 flex w-full"}>
            {invoice?.kindOfInvoice === KindOfInvoice.INVOICE &&
              invoice.contact &&
              !invoice.contact.stripeCustomerId &&
              "Bitte überweisen Sie den Betrag innerhalb der nächsten 2 Wochen auf\n" +
                "            das unten genannte Konto."}
          </div>

          {invoice?.kindOfInvoice === KindOfInvoice.CREDIT && (
            <div className={"mt-10 flex w-full"}>
              Der geannnte Betrag wird in den nächsten Tagen auf das das bei uns hinterlegte Konto
              gutgeschrieben
            </div>
          )}

          {(invoice.user ||
            (invoice?.kindOfInvoice === KindOfInvoice.INVOICE &&
              invoice?.contact?.stripeCustomerId)) && (
            <div className={"mt-10 flex w-full"}>
              Der geannnte Betrag wird in den nächsten Tagen von ihrem hinterlegten Konto per
              Lastschrift abgebucht.
            </div>
          )}

          <div className={"mt-10 flex w-full"}>
            {contactAddress?.isNetInvoice && invoice.sumTax == 0 ? (
              <>
                Nach §13b UStG sind Sie als Leistungsempfänger Schuldner der Umsatzsteuer. The
                recipient of the service is liable for VAT according reverse charge mechanism. UStID
                des Kunden: {contactAddress?.ustId}
              </>
            ) : (
              ""
            )}
          </div>
        </Card>
        <div className={"mt-5 w-full max-w-full shrink-0 px-3 lg:w-3/12 lg:flex-0"}>
          <div
            className={
              "sticky top-1/10 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
            }
          >
            <ul className={"mb-0 flex list-none flex-col gap-1 rounded-xl p-2"}>
              {invoice?.stateOfInvoice == StateOfInvoice.CREATED && (
                <>
                  <li className={"block rounded-lg px-4 font-bold"}>E-Mail</li>
                  <li
                    className={
                      "block flex flex-row truncate text-ellipsis rounded-lg px-4 transition-colors ease-soft-in-out hover:bg-gray-200"
                    }
                  >
                    {!invoice.sendAsMail && (
                      <div className={"flex flex-row items-center gap-2"}>
                        <FaPaperPlane className={"text-black"} />
                        <button
                          title={`Rechnung an ${
                            invoice?.contact ? invoice?.contact?.invoiceMail : invoice?.user?.email
                          } senden`}
                          onClick={() => setDialogOpen(true)}
                          className={"leading-normal dark:text-white"}
                        >
                          {invoice.kindOfInvoice === KindOfInvoice.CREDIT
                            ? "Gutschrift senden"
                            : "Rechnung senden"}
                        </button>
                      </div>
                    )}
                    {invoice.sendAsMail && (
                      <div className={"flex flex-row items-center gap-2"}>
                        <RiMailCheckLine size={18} className={"text-green-600"} />
                        <span className={"truncate"}>Rechnung wurde versendet</span>
                      </div>
                    )}
                    {sendingEmail && (
                      <div className={"mr-2 inline-block  w-4 fill-current stroke-none text-black"}>
                        <FiLoader className="animate-spin text-primary" />
                      </div>
                    )}

                    <Modal
                      isOpen={dialogOpen}
                      onClose={() => setDialogOpen(false)}
                      title={"Bestätigung"}
                    >
                      <p>
                        Möchten Sie die
                        {invoice.kindOfInvoice == KindOfInvoice.CREDIT && " Gutschrift "}
                        {invoice.kindOfInvoice == KindOfInvoice.CREDIT_STORNO &&
                          " Storno Gutschrift "}
                        {invoice.kindOfInvoice == KindOfInvoice.INVOICE && " Rechnung "}
                        {invoice.kindOfInvoice == KindOfInvoice.STORNO && " Storno Rechnung "}
                        wirklich senden?
                      </p>

                      <div>
                        <label className="block text-sm font-medium text-black">
                          Nachrichten-Inhalt
                        </label>
                        <textarea
                          onChange={(e) => setCustomMailText(e.target.value)}
                          className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                        />
                      </div>
                      <div className="mt-6 flex justify-end">
                        <button
                          className="mr-2 rounded bg-gray-300 px-4 py-2 text-black hover:brightness-90"
                          onClick={() => setDialogOpen(false)}
                        >
                          Abbrechen
                        </button>
                        <button
                          className="rounded bg-primary px-4 py-2 font-semibold text-white hover:brightness-90"
                          onClick={sendInvoice}
                        >
                          Senden
                        </button>
                      </div>
                    </Modal>
                  </li>
                </>
              )}
              {!invoice.sendAsMail &&
                invoice &&
                invoice?.files?.length > 0 && (
                    <li
                      className={"block truncate text-ellipsis rounded-lg px-4 hover:bg-gray-200"}
                    >
                      <div className={"flex flex-row items-center gap-2"}>
                        <TbMailExclamation className={"text-black"} color={"orange"} />
                        <button onClick={() => setReadFlag()}>Als gesendet markieren</button>
                      </div>
                    </li>,
                  )}
              {invoice && invoice?.files?.length > 0 && (
                <>
                  <li
                    className={
                      "block rounded-lg px-4 pt-1 font-bold transition-colors ease-soft-in-out "
                    }
                  >
                    Downloads
                  </li>
                  {invoice?.files?.map((file: FileRef) => {
                    return (
                      <li
                        key={file.id}
                        className={"block truncate text-ellipsis rounded-lg px-4 hover:bg-gray-200"}
                      >
                        <div
                          className={"mr-2 inline-block  w-4 fill-current stroke-none text-black"}
                        >
                          <FaDownload color={""} className={""} />
                        </div>
                        <Download
                          className={
                            "max-w-[150px] truncate text-ellipsis  leading-normal dark:text-white"
                          }
                          fileRef={file}
                        />
                      </li>
                    );
                  })}
                  {(invoice.cdrs.length > 0  || invoice.creditCdrs.length > 0) && !invoice?.files?.find((fileRef) => fileRef.contentType == "text/csv") &&
                  <li
                    className={
                      "block rounded-lg px-4 pt-1 transition-colors ease-soft-in-out "
                    }
                  >
                    <div className={"flex flex-row items-center gap-2"}>
                      <FaFileCsv className={"text-black"} />
                      <button onClick={() => generateCsvFile()}>CSV generieren</button>
                    </div>
                  </li>}
                </>
              )}
              <li className={"block rounded-lg px-4 pt-1 font-bold "}>Bearbeiten</li>
              {invoice &&
                (invoice.kindOfInvoice === KindOfInvoice.INVOICE ||
                  invoice.kindOfInvoice === KindOfInvoice.CREDIT) &&
                invoice.stateOfInvoice == StateOfInvoice.CREATED && (
                  <li className={"block truncate text-ellipsis rounded-lg px-4 hover:bg-gray-200"}>
                    <div className={"mr-2 inline-block  w-4 fill-current stroke-none text-black"}>
                      <MdOutlineCancel color={"red"} className={""} />
                    </div>
                    <button onClick={() => setShowModal(true)}>
                      {invoice.kindOfInvoice === KindOfInvoice.CREDIT
                        ? "Gutschrift stornieren "
                        : "Rechnung stornieren"}
                    </button>
                  </li>
                )}
              {invoice &&
                (invoice.kindOfInvoice === KindOfInvoice.INVOICE ||
                  invoice.kindOfInvoice === KindOfInvoice.CREDIT) &&
                invoice.stateOfInvoice == StateOfInvoice.PREVIEW && (
                  <>
                    <li
                      className={
                        "block rounded-lg px-4 py-2 transition-colors ease-soft-in-out hover:bg-gray-200"
                      }
                    >
                      <div className={"mr-4 inline-block  w-4 fill-current stroke-none text-black"}>
                        <MdPriceCheck color={"green"} size={26} className={""} />
                      </div>
                      <button
                        onClick={() => finishInvoice()}
                        className={"leading-normal dark:text-white"}
                      >
                        {invoice.kindOfInvoice === KindOfInvoice.CREDIT
                          ? "Gutschrift abschließen"
                          : "Rechnung abschließen"}
                      </button>
                      {finishLoading && <FiLoader className=" animate-spin text-primary" />}
                    </li>

                    <li
                      className={
                        "block rounded-lg px-4 py-2 transition-colors ease-soft-in-out hover:bg-gray-200"
                      }
                    >
                      <div className={"mr-4 inline-block  w-4 fill-current stroke-none text-black"}>
                        <MdOutlineDeleteForever color={"red"} size={26} className={""} />
                      </div>
                      <button
                        onClick={() => deleteInvoice()}
                        className={"leading-normal dark:text-white"}
                      >
                        {invoice.kindOfInvoice === KindOfInvoice.CREDIT
                          ? "Gutschrift Löschen"
                          : "Rechnung löschen"}
                      </button>
                    </li>
                  </>
                )}
              <li
                className={
                  "block rounded-lg px-4 pt-1 font-bold transition-colors ease-soft-in-out "
                }
              >
                Sonstiges
              </li>
              <li className={"block rounded-lg px-4  hover:bg-gray-200"}>
                <div className={"mr-4 inline-block  w-4 fill-current stroke-none text-black"}>
                  <GiArchiveResearch color={""} size={20} className={""} />
                </div>

                <button
                  className="leading-normal dark:text-white"
                  onClick={() => setIsModalOpen(true)}
                >
                  History
                </button>
              </li>
              <li
                className={
                  "block rounded-lg px-4 py-2 transition-colors ease-soft-in-out hover:bg-gray-200"
                }
              >
                <div className={"mr-4 inline-block  flex  w-full fill-current stroke-none"}>
                  <MdAccountBalance className={""} color={""} size={20} />
                  {invoice?.bankTransactions.length === 0 && (
                    <div className={"ml-2 block"}>-Keine Verknüpfung-</div>
                  )}
                  {invoice?.bankTransactions?.map((transaction, index) => (
                    <div className={"ml-2 truncate"} key={index}>
                      {transaction.qontoTransaction_id}
                    </div>
                  ))}
                </div>
              </li>
              {invoice?.kindOfInvoice == KindOfInvoice.CREDIT && !invoice?.contract && (
                <li
                  className={
                    "block rounded-lg px-4 py-2 transition-colors ease-soft-in-out hover:bg-gray-200"
                  }
                >
                  <Link
                    href={`/invoice/${invoice?.id}/cpo`}
                    className={
                      "mr-4 inline-block  flex w-full  fill-current stroke-none hover:cursor-pointer"
                    }
                  >
                    <TbLinkPlus size={20} />
                    <div className={"ml-2 truncate"} key={"cpoinvoice"}>
                      CPO Rechnung generieren
                    </div>
                  </Link>
                </li>
              )}
            </ul>
          </div>
        </div>
      </div>
      {(invoice?.kindOfInvoice === KindOfInvoice.INVOICE ||
        invoice?.kindOfInvoice === KindOfInvoice.CREDIT) &&
        (invoice?.stateOfInvoice !== StateOfInvoice.CANCEL ? (
          <Card header_left={"CDRs"} className={"mt-20 "}>
            <Table
              columnDefs={columnDefs}
              rowData={
                invoice.kindOfInvoice == KindOfInvoice.INVOICE ? invoice.cdrs : invoice.creditCdrs
              }
              gridOptions={gridOptions}
              groupIncludeTotalFooter={true}
              groupIncludeFooter={true}
            />
          </Card>
        ) : (
          <Card header_left={"CDRs"} className={"mt-20"}>
            Stornierte Rechnungen/Gutschriften haben keine CDRs
          </Card>
        ))}

      {showModal ? (
        <>
          <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto overflow-x-hidden outline-none focus:outline-none">
            <div className="relative mx-auto my-6 w-auto max-w-3xl">
              {/*content*/}
              <div className="relative flex w-full flex-col rounded-lg border-0 bg-white shadow-lg outline-none focus:outline-none">
                {/*header*/}
                <div className="flex items-start justify-between rounded-t border-b border-solid border-slate-200 p-5">
                  <h3 className="text-3xl font-semibold">
                    {invoice?.kindOfInvoice === KindOfInvoice.CREDIT
                      ? "Gutschrift stornieren"
                      : "Rechnung stornieren"}
                  </h3>
                  <button
                    className="float-right ml-auto border-0 bg-transparent p-1 text-3xl font-semibold leading-none text-black opacity-5 outline-none focus:outline-none"
                    onClick={() => setShowModal(false)}
                  >
                    <span className="block h-6 w-6 bg-transparent text-2xl text-black opacity-5 outline-none focus:outline-none">
                      ×
                    </span>
                  </button>
                </div>
                {/*body*/}
                <div className="relative flex-auto p-6">
                  <p className="my-4 text-lg leading-relaxed text-slate-500">
                    Wollen sie die{" "}
                    {invoice?.kindOfInvoice === KindOfInvoice.CREDIT ? "Gutschrift " : "Rechnung "}
                    wirklich stornieren? Dieser Schritt kann nicht rückgängig gemacht werden.
                  </p>
                </div>
                {/*footer*/}
                <div className="flex items-center justify-end rounded-b border-t border-solid border-slate-200 p-6">
                  <button
                    className=" background-transparent mb-1 mr-1 px-6 py-2 text-sm font-bold uppercase outline-none transition-all duration-150 ease-linear focus:outline-none"
                    type="button"
                    onClick={() => setShowModal(false)}
                  >
                    Doch nicht stornieren
                  </button>
                  <button
                    className="mb-1 mr-1 rounded bg-red-500 px-6 py-3 text-sm font-bold uppercase text-white shadow outline-none transition-all duration-150 ease-linear hover:shadow-lg focus:outline-none active:bg-emerald-600"
                    type="button"
                    onClick={() => {
                      setShowModal(false);
                      cancelInvoice();
                    }}
                  >
                    {invoice?.kindOfInvoice === KindOfInvoice.CREDIT ? "Gutschrift " : "Rechnung "}{" "}
                    stornieren
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : null}
      <Modal title="Invoice History" isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <p className="mb-4">
          {invoice?.history
            ?.split("\n")
            .filter((item) => item !== "null")
            .map((item, index) => (
              <React.Fragment key={index}>
                {item}
                <br />
              </React.Fragment>
            ))}
        </p>
        {/* Weitere Inhalte hier */}
      </Modal>
    </>
  );
};

export default Show;
