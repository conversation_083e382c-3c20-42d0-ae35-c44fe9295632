import React from "react";

import { type Prisma } from "@prisma/client";

import Card from "~/component/card";

import { CustomInvoiceForm } from "~/app/(app)/invoice/component/CustomInvoiceForm";
import { getCPOInvoicePositions } from "~/server/invoice/invoiceUtils";

export type InvoiceWithContactsWithAddress = Prisma.InvoiceGetPayload<{
  include: { contact: { include: { contactAddress: true } } };
}>;

const Page = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;
  const { positions, invoice } = await getCPOInvoicePositions(slug);

  const monthName = new Date().toLocaleString("de-De", { month: "long" });
  const startDate = invoice?.servicePeriodFrom
    ? invoice?.servicePeriodFrom?.toISOString().split("T")[0]
    : "";
  const endDate = invoice?.servicePeriodTo
    ? invoice?.servicePeriodTo?.toISOString().split("T")[0]
    : "";

  return (
    <>
      <h1 className={"mb-0 font-bold dark:text-white"}>
        <>
          Rechnung {monthName} für {invoice?.contact?.name}
        </>
      </h1>
      <Card>
        <CustomInvoiceForm
          startDate={startDate}
          endDate={endDate}
          invoicePositions={positions}
          contacts={invoice?.contact ? [invoice?.contact] : []}
        />
      </Card>
    </>
  );
};

export default Page;
