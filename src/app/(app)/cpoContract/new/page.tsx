import Card from "~/component/card";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import CPOContractForm from "~/app/(app)/cpoContract/CPOContractForm";
import prisma from "~/server/db/prisma";

const getContacts = async () => {
  return await prisma.contact.findMany({ where: { cpo: true } });
};

const NewCPOContractPage = async () => {
  const session = await getServerSession(authOptions);
  if (session && session?.user?.role == Role.ADMIN) {
    return (
      <Card>
        <CPOContractForm contacts={await getContacts()} />
      </Card>
    );
  }
  return "Keine Berechtigung";
};

export default NewCPOContractPage;
