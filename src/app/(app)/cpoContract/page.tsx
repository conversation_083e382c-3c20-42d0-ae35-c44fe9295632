"use server";
import React from "react";
import Card from "../../../component/card";

import DateContextWrapper from "~/component/dateRangePicker/dateContextWrapper";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { Role } from "@prisma/client";
import CPOContractTable from "~/app/(app)/cpoContract/CPOContractTable";
import Button from "~/component/button";
import { AiOutlinePlusCircle } from "react-icons/ai";
import Link from "next/link";

async function getCPOContracts() {
  const session = await getServerSession(authOptions);
  if (!session || !(session?.user?.role == Role.ADMIN)) {
    return [];
  }
  const contracts = await prisma.cPOContract.findMany({
    include: {
      contact: true,
    },
  });

  return contracts;
}

const Page = async () => {
  const contracts = await getCPOContracts();
  return (
    <>
      <h1 className={"mb-0 font-bold dark:text-white"}>
        <>CPO Verträge</>
      </h1>
      <div className="mb-3 flex justify-end">
        <Link href={"/cpoContract/new"}>
          <Button type={"button"}>
            <AiOutlinePlusCircle className={"mr-2"} size={"1.5rem"} />
            Neuer CPO Vertrag
          </Button>
        </Link>
      </div>

      <Card header_left={``}>
        <CPOContractTable data={contracts} />
      </Card>
    </>
  );
};

export default Page;
