"use client";
import { useForm, SubmitHandler } from "react-hook-form";
import React from "react";
import Button from "~/component/button";
import { FaSave } from "react-icons/fa";
import { Contact } from "@prisma/client";
import { useRouter } from "next/navigation";

type CPOContractType = {
  id: string;
  name: string;
  numACCharger: number;
  numDCCharger: number;
  numACHotline: number;
  numDCHotline: number;
  priceACCharger: number;
  priceDCCharger: number;
  priceACHotline: number;
  priceDCHotline: number;
  start: string;
  end: string;
  contactId?: string;
  kwhFeeCent: number;
  kwhFeeCentAC: number;
  kwhFeeCentDC: number;
  sessionFeeCent: number;
  sessionFeeCentAC: number;
  sessionFeeCentDC: number;
  monthlyEmployeeClubCharging: number;
  directPaymentFeePercent: number;
  adhocPaymentFeePercent: number;
  serviceFeePerAC: number;
  serviceFeePerDC: number;
  directPaymentToken: string;
};

type CPOContractTypeErrorType = {
  [K in keyof CPOContractType]?: {
    message: string;
  };
};

interface Props {
  contract?: CPOContractType;
  contacts: Contact[];
}

const CPOContractForm = ({ contract, contacts }: Props) => {
  console.log(contract);
  const defaultValues = contract
    ? {
        ...contract,
        start: contract.start.substring(0, 10),
        end: contract.end.substring(0, 10),
      }
    : {
        name: "Standard",
        numACCharger: 0,
        numDCCharger: 0,
        numACHotline: 0,
        numDCHotline: 0,
        priceACCharger: 5,
        priceDCCharger: 10,
        priceACHotline: 5,
        priceDCHotline: 5,
        start: "",
        end: "",
        kwhFeeCent: 3,
        kwhFeeCentAC: 3,
        kwhFeeCentDC: 3,
        sessionFeeCent: 20,
        sessionFeeCentAC: 20,
        sessionFeeCentDC: 20,
        monthlyEmployeeClubCharging: 0,
        directPaymentFeePercent: 3.5,
        adhocPaymentFeePercent: 3.5,
        directPaymentToken: "none",
        serviceFeePerAC: 0,
        serviceFeePerDC: 0,
      };

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CPOContractType, CPOContractTypeErrorType>({
    defaultValues: defaultValues,
  });
  const router = useRouter();

  const onSubmit: SubmitHandler<CPOContractType> = async (data) => {
    const response = await fetch(`/api/cpoContract`, {
      method: "POST",
      body: JSON.stringify({ data }),
    });

    if (response.ok) {
      router.push("/cpoContract");
      router.refresh();
    } else {
      const message = await response.json();
      //setApiErrorMessage(message?.message ?? "Fehler beim Speichern");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <input
        disabled={contract?.id ? false : true}
        type={"text"}
        {...register("id")}
        className={"hidden"}
      />

      <div className="mb-0 flex justify-between rounded-t-2xl px-6 ">
        <h5 id={"basic-data"} className="flex flex-row gap-1 dark:text-white">
          <span>CPO Vertrag</span>
        </h5>
        <div>
          {Object.keys(errors).length > 0 && (
            <ul className={"text-red-500"}>
              {Object.keys(errors).map((fieldName, index) => (
                <li key={index}>{errors[fieldName as keyof CPOContractTypeErrorType]?.message}</li>
              ))}
            </ul>
          )}
        </div>
        <Button className={"hidden items-center sm:flex"} type={"submit"}>
          <FaSave className={"mr-1"} />
          {contract?.id ? "Vertrag aktualisieren" : "Neuen Vertrag speichern"}
        </Button>
      </div>
      <div className="grid grid-cols-4 gap-4 p-6">
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Name
          </label>
          <input
            {...register("name")}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.name && <span className="text-red-500">{errors.name.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Anzahl AC Ladepunkte
          </label>
          <input
            type="number"
            {...register("numACCharger", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.numACCharger && (
            <span className="text-red-500">{errors.numACCharger.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Anzahl DC Ladepunkte
          </label>
          <input
            type="number"
            {...register("numDCCharger", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.numDCCharger && (
            <span className="text-red-500">{errors.numDCCharger.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Anzahl AC Hotline
          </label>
          <input
            type="number"
            {...register("numACHotline", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.numACHotline && (
            <span className="text-red-500">{errors.numACHotline.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Anzahl DC Hotline
          </label>
          <input
            type="number"
            {...register("numDCHotline", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.numDCHotline && (
            <span className="text-red-500">{errors.numDCHotline.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Kosten pro AC Ladepunkt
          </label>
          <input
            type="number"
            step="0.01"
            {...register("priceACCharger", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.priceACCharger && (
            <span className="text-red-500">{errors.priceACCharger.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Kosten pro DC Ladepunkt
          </label>
          <input
            type="number"
            step="0.01"
            {...register("priceDCCharger", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.priceDCCharger && (
            <span className="text-red-500">{errors.priceDCCharger.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Kosten Hotline AC Ladepunkt
          </label>
          <input
            type="number"
            step="0.01"
            {...register("priceACHotline", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.priceACHotline && (
            <span className="text-red-500">{errors.priceACHotline.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Kosten Hotline DC Ladepunkt
          </label>
          <input
            type="number"
            step="0.01"
            {...register("priceDCHotline", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.priceDCHotline && (
            <span className="text-red-500">{errors.priceDCHotline.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Gültig ab
          </label>
          <input
            type="date"
            {...register("start")}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.start && <span className="text-red-500">{errors.start.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Gültig bis
          </label>
          <input
            type="date"
            {...register("end")}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.end && <span className="text-red-500">{errors.end.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Verknüpfter CPO
          </label>
          <select
            {...register("contactId")}
            className="form-select m-0 block w-full appearance-none
      rounded border border-solid
      border-gray-300 bg-white bg-clip-padding
      bg-no-repeat px-3 py-1.5
      text-base font-normal
      text-gray-700 transition ease-in-out
      focus:border-blue-600 focus:bg-white focus:text-gray-700 focus:outline-none"
            aria-label="Default select example"
          >
            <option key={"placeholder"} value={""}>
              Bitte wählen
            </option>
            {contacts &&
              contacts.map((contact) => (
                <option key={contact.id} value={contact.id}>
                  {contact.name}
                </option>
              ))}
          </select>
          {errors.contactId && <span className="text-red-500">{errors.contactId.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            kWh Fee (cent) - Legacy
          </label>
          <input
            type="number"
            step="0.01"
            {...register("kwhFeeCent", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.kwhFeeCent && <span className="text-red-500">{errors.kwhFeeCent.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            kWh Fee AC (cent)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("kwhFeeCentAC", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.kwhFeeCentAC && <span className="text-red-500">{errors.kwhFeeCentAC.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            kWh Fee DC (cent)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("kwhFeeCentDC", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.kwhFeeCentDC && <span className="text-red-500">{errors.kwhFeeCentDC.message}</span>}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Session Fee (cent) - Legacy
          </label>
          <input
            type="number"
            step="0.01"
            {...register("sessionFeeCent", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.sessionFeeCent && (
            <span className="text-red-500">{errors.sessionFeeCent.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Session Fee AC (cent)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("sessionFeeCentAC", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.sessionFeeCentAC && (
            <span className="text-red-500">{errors.sessionFeeCentAC.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Session Fee DC (cent)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("sessionFeeCentDC", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.sessionFeeCentDC && (
            <span className="text-red-500">{errors.sessionFeeCentDC.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Mitarbeiterladen/Clubladen Gebühr (€)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("monthlyEmployeeClubCharging", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.monthlyEmployeeClubCharging && (
            <span className="text-red-500">{errors.monthlyEmployeeClubCharging.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Direct Payment Fee (%)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("directPaymentFeePercent", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.directPaymentFeePercent && (
            <span className="text-red-500">{errors.directPaymentFeePercent.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Direct Payment Token
          </label>
          <input
            {...register("directPaymentToken")}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.directPaymentToken && (
            <span className="text-red-500">{errors.directPaymentToken.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Adhoc Payment Fee (%)
          </label>
          <input
            type="number"
            step="0.01"
            {...register("adhocPaymentFeePercent", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.adhocPaymentFeePercent && (
            <span className="text-red-500">{errors.adhocPaymentFeePercent.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Servicegebühr AC Ladepunkt
          </label>
          <input
            type="number"
            step="0.01"
            {...register("serviceFeePerAC", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.serviceFeePerAC && (
            <span className="text-red-500">{errors.serviceFeePerAC.message}</span>
          )}
        </div>
        <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
          <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            Servicegebühr DC Ladepunkt
          </label>
          <input
            type="number"
            step="0.01"
            {...register("serviceFeePerDC", { valueAsNumber: true })}
            className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
          />
          {errors.serviceFeePerDC && (
            <span className="text-red-500">{errors.serviceFeePerDC.message}</span>
          )}
        </div>
      </div>
    </form>
  );
};

export default CPOContractForm;
