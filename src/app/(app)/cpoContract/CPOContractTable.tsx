"use client";

import React, { useState } from "react";

import type { ICellRendererParams } from "ag-grid-community";
import type { ValueGetterParams } from "ag-grid-community/dist/lib/entities/colDef";
import Table from "~/utils/table/table";
import Link from "next/link";
import { HiMagnifyingGlass } from "react-icons/hi2";
import { dateRenderer, twoDecimalPlacesFormatterWithCurrency } from "~/utils/table/formatter";

export const CPOContractTable = ({ data }: any) => {
  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <Link href={`cpoContract/update/${params?.data?.id}`}>
          <HiMagnifyingGlass />
        </Link>
      </>
    );
  };

  const columnDefs = [
    {
      field: "contact.name",
      headerName: "Verknüpfter CPO",
      minWidth: 300,
    },
    {
      field: "id",
      headerName: "Grundkosten",
      aggFunc: "sum",
      valueGetter: (params: ValueGetterParams) => {
        const ac =
          params.data.numACCharger * (params.data.priceACCharger + params.data.serviceFeePerAC) +
          params.data.priceACHotline * params.data.numACHotline;
        const dc =
          params.data.numDCCharger * (params.data.priceDCCharger + params.data.serviceFeePerDC) +
          params.data.priceDCHotline * params.data.numDCHotline;

        return ac + dc;
      },
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "start",
      headerName: "Gültig ab",
      cellRenderer: dateRenderer,
    },
    {
      field: "end",
      headerName: "Gültig bis",
      cellRenderer: dateRenderer,
    },
    { field: "numACCharger", headerName: "Anzahl AC Ladepunkte" },
    { field: "numDCCharger", headerName: "Anzahl DC Ladepunkte" },
    { field: "numACHotline", headerName: "Anzahl AC Hotline" },
    { field: "numDCHotline", headerName: "Anzahl DC Hotline" },
    { field: "priceACCharger", headerName: "Kosten pro AC Ladepunkt" },
    { field: "priceDCCharger", headerName: "Kosten pro DC Ladepunkt" },
    { field: "priceACHotline", headerName: "Kosten Hotline AC Ladepunkt" },
    { field: "priceDCHotline", headerName: "Kosten Hotline DC Ladepunkt" },
    { field: "kwhFeeCent", headerName: "kWh Fee (cent) - Legacy" },
    { field: "kwhFeeCentAC", headerName: "kWh Fee AC (cent)" },
    { field: "kwhFeeCentDC", headerName: "kWh Fee DC (cent)" },
    { field: "sessionFeeCent", headerName: "Session Fee (cent) - Legacy" },
    { field: "sessionFeeCentAC", headerName: "Session Fee AC (cent)" },
    { field: "sessionFeeCentDC", headerName: "Session Fee DC (cent)" },
    { field: "monthlyEmployeeClubCharging", headerName: "Mitarbeiterladen/Clubladen Gebühr (€)" },
    { field: "directPaymentFeePercent", headerName: "Direct Payment Fee (%)" },
    { field: "adhocPaymentFeePercent", headerName: "Adhoc Payment Fee (%)" },
    { field: "directPaymentToken", headerName: "Direct Payment Token" },
    { field: "serviceFeePerAC", headerName: "Servicegebühr AC" },
    { field: "serviceFeePerDC", headerName: "Servicegebühr DC" },
    { field: "action", headerName: "Aktion", cellRenderer: CellRenderer, minWidth: 100 },
    { field: "id", width: 100 },
  ];

  return (
    <Table
      gridId={"cpocontracts"}
      groupIncludeTotalFooter={true}
      columnDefs={columnDefs}
      rowData={data}
    />
  );
};

export default CPOContractTable;
