import prisma from "~/server/db/prisma";
import CPOContractForm from "~/app/(app)/cpoContract/CPOContractForm";

export const revalidate = 0;

const getContract = async (slug: string) => {
  if (!slug) {
    return {};
  }
  const companyTariffs = await prisma.cPOContract.findUniqueOrThrow({
    where: {
      id: slug,
    },
  });
  return JSON.parse(JSON.stringify(companyTariffs));
};

const getContacts = async () => {
  return await prisma.contact.findMany({ where: { cpo: true } });
};

const Page = async ({ params }: { params: { slug: string } }) => {
  if (!params.slug) {
    return <>Missing Params</>;
  }

  const contract = await getContract(params.slug);
  const contacts = await getContacts();

  return (
    <>
      <CPOContractForm contract={contract} contacts={contacts}></CPOContractForm>
    </>
  );
};

export default Page;
