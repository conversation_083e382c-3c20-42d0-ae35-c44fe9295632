"use client";

import type { INode } from "../../tenantconfiguration/component/Tree";
import Tree from "../../tenantconfiguration/component/Tree";
import React, { forwardRef, useState } from "react";
import { FaFolder } from "react-icons/fa";
import { IoIosArrowDropdownCircle } from "react-icons/io";

interface SelectOUProps {
  nodes: INode[];
  onChange?: (node: INode | string) => void;
  defaultSelected?: string;
  setObject: boolean;
  disabled?: boolean;
}

// Find a node in the tree based on its id
function findNode(nodes: INode[], id: string): INode | null {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    } else if (node.children) {
      const result = findNode(node.children, id);
      if (result) {
        return result;
      }
    }
  }
  return null;
}

const SelectOU = forwardRef<HTMLDivElement, SelectOUProps>(
  ({ disabled, nodes, onChange, defaultSelected, setObject }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const defaultNode = defaultSelected ? findNode(nodes, defaultSelected) : null;
    const [selectedOU, setSelectedOU] = useState<INode | null>(defaultNode);

    const handleSelect = (node: INode) => {
      setSelectedOU(node);
      if (onChange) {
        if (setObject) {
          onChange(node);
        } else {
          onChange(node.id);
        }
      }
      setIsOpen(false);
    };

    return (
      <div ref={ref} className="relative w-full">
        <span
          className="flex items-center justify-between rounded-md border border-gray-300 bg-white px-2 py-2 text-left font-medium text-primary hover:bg-gray-200 dark:text-white"
          onClick={() => setIsOpen(!isOpen)}
        >
          <FaFolder className="mr-2" />
          {selectedOU ? selectedOU.name : "Select OU"} {/* Display selected OU name */}
          <IoIosArrowDropdownCircle className="ml-2" />
        </span>

        {isOpen && (
          <div className="absolute left-0 z-10 mt-1 w-auto rounded-md bg-white shadow-lg">
            <div className="rounded-md ring-1 ring-black ring-opacity-5">
              <Tree nodes={nodes} onSelect={handleSelect} /> {/* Pass handleSelect to Tree */}
            </div>
          </div>
        )}
      </div>
    );
  },
);
SelectOU.displayName = "SelectOU"; // Fügen Sie diese Zeile hinzu, um den Anzeigenamen explizit festzulegen

export default SelectOU;
