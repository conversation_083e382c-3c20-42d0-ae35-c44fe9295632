"use client";

import SelectOU from "../compontent/SelectOu";
import { useForm } from "react-hook-form";
import type { INode } from "../../tenantconfiguration/component/Tree";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import type { UserWithOu } from "~/types/prisma/user";
import Button from "~/component/button";

interface Props {
  tenantsTree: INode[];
  user: UserWithOu;
}

interface FormInputs {
  id: string;
  server?: string;
  name: string;
  email: string;
  ouId: string;
  remoteStartToken: string;
}

const Form = ({ tenantsTree, user }: Props) => {
  const router = useRouter();
  const [formSuccess, setFormSuccess] = useState(false);
  const [createdUserName, setCreatedUserName] = useState("");
  const [formVisible, setFormVisible] = useState(true);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    setError,
  } = useForm<FormInputs>({
    defaultValues: {
      id: user.id,
      name: user.name || undefined,
      email: user.email || undefined,
      ouId: user.ouId || undefined,
      remoteStartToken: "",
    },
  });

  useEffect(() => {
    if (formSuccess) {
      setTimeout(() => {
        router.push("/users");
        router.refresh();
      }, 3000);
    }
  }, [formSuccess, router]);

  const onSubmit = async (data: FormInputs) => {
    try {
      const response = await fetch("/api/user", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error(await response.text());
      }
      setCreatedUserName(data.name);
      setFormVisible(false);
      setFormSuccess(true);
    } catch (error: unknown) {
      if (error instanceof Error) {
        setError("server", { message: error.message });
      } else {
        setError("server", { message: "An unexpected error occurred." });
      }
    }
  };

  return (
    <div className={"sm:w3/4 md:w-1/2"}>
      {formVisible ? (
        <form onSubmit={handleSubmit(onSubmit)}>
          <input type="hidden" {...register("id")} name="id" />
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">
              Name:
              <input
                {...register("name", { required: "Name is required." })}
                className="mt-1 block w-full rounded-md border border-gray-300 px-2 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                type="text"
              />
              {errors.name && <p>{errors.name.message}</p>}
            </label>
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">
              Email:
              <input
                {...register("email", { required: "Email is required." })}
                className="mt-1 block w-full rounded-md border border-gray-300 px-2 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                type="email"
              />
              {errors.email && <p>{errors.email.message}</p>}
            </label>
          </div>
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700">
              OU ID:
              <SelectOU
                nodes={tenantsTree}
                defaultSelected={user.ouId}
                onChange={(node) => {
                  setValue("ouId", node.id);
                }}
              />
              <input
                type="hidden"
                {...register("ouId", { required: "Ou is missing." })}
                name="ouId"
              />
              {errors.ouId && <p>{errors.ouId.message}</p>}
            </label>
          </div>

          <Button type="submit">Save</Button>
          {errors.server && <p>{errors.server.message}</p>}
        </form>
      ) : null}

      {formSuccess ? (
        <div
          className="relative rounded border border-green-400 bg-green-100 px-4 py-3 text-green-700"
          role="alert"
        >
          <strong className="font-bold">Erfolg! </strong>
          <span className="block sm:inline">
            Der Benutzer {createdUserName} wurde erfolgreich gespeichert.
          </span>
        </div>
      ) : null}
    </div>
  );
};

export default Form;
