import Card from "~/component/card";
import prisma from "~/server/db/prisma";
import Link from "next/link";
import Form from "./form";
import type { UserWithOu } from "~/types/prisma/user";

import GeneratePassword from "./generatePassword";
import { getOUTree } from "~/server/model/tenants/func";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import NotFound from "~/app/(app)/not-found";
import { Role } from "@prisma/client";
import Button from "~/component/button";

const getUser = async (id: string): Promise<UserWithOu | null> => {
  const user = await prisma.user.findFirst({
    where: {
      id: id,
    },
    include: {
      ou: true,
    },
  });
  return user;
};

const Page = async ({ params }: { params: { id: string } }) => {
  const session = await getServerSession(authOptions);
  if (!session || !(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }
  const user = await getUser(params.id);
  const tenantsTree = await getOUTree();

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <Card
        header_left={
          <div className="flex items-center">
            <h6>{user.name}</h6>
          </div>
        }
        header_right={
          <Link href={"/users"}>
            <Button>Zurück</Button>
          </Link>
        }
      >
        <Form user={user} tenantsTree={tenantsTree}></Form>
      </Card>
      <Card
        className={"mt-10"}
        header_left={
          <div className="flex items-center">
            <h6>Actions</h6>
          </div>
        }
      >
        <GeneratePassword userId={user.id} />
      </Card>
    </div>
  );
};

export default Page;
