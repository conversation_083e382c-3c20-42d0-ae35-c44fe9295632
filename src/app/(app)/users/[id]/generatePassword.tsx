"use client";

import { AiOut<PERSON>Key } from "react-icons/ai";
import { useState } from "react";
import Button from "~/component/button";

interface Props {
  userId: string;
}

const GeneratePassword = ({ userId }: Props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState<boolean | null>(null);

  const handleClick = async () => {
    setIsLoading(true);
    setStatusMessage(null); // clear previous message

    try {
      const response = await fetch(`/api/user/generatePassword`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id: userId }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      setStatusMessage("Password has been successfully reset and an email has been sent.");
      setIsSuccess(true);
    } catch (error: unknown) {
      if (error instanceof Error) {
        setStatusMessage(`Failed to reset password: ${error.message}`);
        setIsSuccess(false);
      } else {
        setStatusMessage("An unexpected error occurred.");
        setIsSuccess(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Button disabled={isLoading} onClick={handleClick}>
        <AiOutlineKey className="mr-2" />
        Generate new password and send email
      </Button>
      {statusMessage && (
        <p
          className={`mt-2 transform text-sm transition-all duration-500 ease-in-out ${
            isSuccess
              ? "translate-y-0 text-green-500 opacity-100"
              : "translate-y-1/2 text-red-500 opacity-0"
          }`}
        >
          {statusMessage}
        </p>
      )}
    </div>
  );
};

export default GeneratePassword;
