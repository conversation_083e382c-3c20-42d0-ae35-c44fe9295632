import Card from "~/component/card";
import type { INode } from "../../tenantconfiguration/component/Tree";
import prisma from "~/server/db/prisma";
import { Ou, Role } from "@prisma/client";
import Form from "./form";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import NotFound from "~/app/(app)/not-found";
import React from "react";

const getChildren = (parent: Ou, ous: Ou[]): INode[] => {
  const children = ous.filter((ou) => ou.parentId === parent.id);

  return children.map((ou) => {
    return {
      id: ou.id,
      name: ou.name,
      children: getChildren(ou, ous),
    };
  });
};

const getTenantsTree = async (): Promise<INode[]> => {
  const ous = await prisma.ou.findMany({ where: { deleted: { equals: null } } });

  const rootOus = ous.filter((ou) => !ou.parentId);

  return rootOus.map((ou) => {
    return {
      id: ou.id,
      name: ou.name,
      children: getC<PERSON>dre<PERSON>(ou, ous),
    };
  });
};

const NewUserPage = async () => {
  const tenantsTree = await getTenantsTree();
  const session = await getServerSession(authOptions);
  if (!session) {
    return <>No login</>;
  }
  if (session?.user?.role !== Role.ADMIN) {
    return <NotFound />;
  }
  return (
    <Card className="rounded-md bg-white p-8 shadow-sm">
      <Form tenantsTree={tenantsTree} />
    </Card>
  );
};

export default NewUserPage;
