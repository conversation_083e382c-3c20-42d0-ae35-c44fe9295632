"use client";

import SelectOU from "../compontent/SelectOu";
import { Controller, useForm } from "react-hook-form";
import type { INode } from "../../tenantconfiguration/component/Tree";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";

interface Props {
  tenantsTree: INode[];
}

interface FormInputs {
  server?: string;
  name: string;
  lastName: string;
  email: string;
  ouId: string;
  role: string;
}

const Form = ({ tenantsTree }: Props) => {
  const router = useRouter();
  const [formSuccess, setFormSuccess] = useState(false);
  const [createdUserName, setCreatedUserName] = useState("");
  const [formVisible, setFormVisible] = useState(true);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
    setError,
  } = useForm<FormInputs>();

  useEffect(() => {
    if (formSuccess) {
      setTimeout(() => {
        router.push("/users");
        router.refresh();
      }, 4000);
    }
  }, [formSuccess, router]);

  const onSubmit = async (data: FormInputs) => {
    try {
      const response = await fetch("/api/user", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error(await response.text());
      }
      setCreatedUserName(data.name);
      setFormVisible(false);
      setFormSuccess(true);
    } catch (error: unknown) {
      if (error instanceof Error) {
        setError("server", { message: error.message });
      } else {
        setError("server", { message: "An unexpected error occurred." });
      }
    }
  };

  return (
    <>
      {formVisible ? (
        <form className={"flex flex-col md:w-1/2"} onSubmit={handleSubmit(onSubmit)}>
          <div className={"flex flex-col gap-1"}>
            <div className={"flex w-full flex-row gap-1"}>
              <div className="w-full">
                <label className="ml-1 w-full text-xs font-bold text-slate-700 dark:text-white/80">
                  Vorname:
                  <input
                    {...register("name", { required: "Bitte einen Vornamen eingeben" })}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-2 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                    type="text"
                  />
                  {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
                </label>
              </div>
              <div className="w-full">
                <label className="ml-1 w-full text-xs font-bold text-slate-700 dark:text-white/80">
                  Nachname:
                  <input
                    {...register("lastName", { required: "Bitte einen Nachnamen angeben" })}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-2 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                    type="text"
                  />
                  {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
                </label>
              </div>
            </div>
            <div className="w-full">
              <label className="ml-1  w-full text-xs font-bold text-slate-700 dark:text-white/80">
                Email:
                <input
                  {...register("email", { required: "Email is required." })}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-2 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  type="email"
                />
                {errors.email && <p>{errors.email.message}</p>}
              </label>
            </div>
            <div className={"flex w-full flex-row justify-between gap-1"}>
              <div className="ml-1 w-full">
                <label
                  className="ml-1 w-full text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="ouId"
                >
                  Ou
                </label>
                <Controller
                  name="ouId" // Set the name to match the field name
                  control={control} // Pass the control object from useForm
                  rules={{ required: "Bitte eine Ou auswählen" }}
                  render={({ field }) => (
                    <SelectOU {...field} nodes={tenantsTree} setObject={false} />
                  )}
                />
              </div>

              <div className="w-full">
                <label
                  className="ml-1 w-full text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="role"
                >
                  Rolle
                </label>
                <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                  <select
                    id={"role"}
                    {...register("role")}
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                  >
                    <option value="CARD_MANAGER">Mitarbeiter-Laden Manager</option>
                    <option value="USER">Normaler User</option>
                    <option value="CPO">CPO</option>
                  </select>
                </div>
              </div>
            </div>

            <Button className={"mt-3 w-52"} type={"submit"}>
              Benutzer anlegen
            </Button>
          </div>

          {errors.server && <p>{errors.server.message}</p>}
        </form>
      ) : null}

      {formSuccess ? (
        <div
          className="relative rounded border border-green-400 bg-green-100 px-4 py-3 text-green-700"
          role="alert"
        >
          <strong className="font-bold">Erfolg!</strong>
          <span className="block sm:inline">
            Der Benutzer {createdUserName} wurde erfolgreich erstellt.
          </span>
        </div>
      ) : null}
    </>
  );
};

export default Form;
