import Card from "~/component/card";
import UserTable from "./compontent/userTable";

import prisma from "~/server/db/prisma";
import { Ou, Role, User } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import NotFound from "~/app/(app)/not-found";
import React from "react";
import Headline from "~/component/Headline";
import StyledLink from "~/app/(app)/util/StyledLink";

export type UserWithOu = User & {
  ou: Ou;
  userGroup?: {
    id: string;
    name: string;
    description?: string;
  } | null;
};

export const revalidate = 0;

const getUsers = async (): Promise<UserWithOu[]> => {
  const session = await getServerSession(authOptions);
  if (!session || !session?.user?.role) {
    return [];
  }
  // return all user if admin
  if (session.user.role == Role.ADMIN) {
    return await prisma.user.findMany({
      include: {
        ou: true,
        userGroup: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });
  }
  //return only user from own ou if card manager
  if (session.user.role == Role.CARD_MANAGER) {
    const ouId = session?.user?.ou?.id;
    if (ouId) {
      return await prisma.user.findMany({
        where: { ouId: session.user.ou.id },
        include: {
          ou: true,
          userGroup: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
        },
      });
    }
    return [];
  }
  return [];
};

const Page = async () => {
  const session = await getServerSession(authOptions);
  if (!session?.user?.role) {
    return <NotFound />;
  }
  if (!(session.user.role == Role.CARD_MANAGER) && !(session.user.role == Role.ADMIN)) {
    return <NotFound />;
  }
  const isAdmin = session?.user?.role == Role.ADMIN;
  const users = await getUsers();

  return (
    <Card>
      <Headline title={"Benutzerverwaltung"} />
      <div className="flex justify-end gap-4">
        {session?.user?.selectedOu?.allowOrderEMPCards && (
          <StyledLink href={"/users/invite"}>Person einladen</StyledLink>
        )}
        {isAdmin && <StyledLink href={"/users/new"}>Benutzer anlegen</StyledLink>}
      </div>
      <UserTable users={users} />
    </Card>
  );
};

export default Page;
