"use client";
import { useSession, signIn, signOut } from "next-auth/react";
import Button from "~/component/button";
import React, { useEffect, useState } from "react";
import { FiLoader } from "react-icons/fi";

export default function Login() {
  // get session from nextAuth
  const { data: session, status } = useSession();

  // useSession uses React Context
  const [loading, setLoading] = useState<boolean>(true);
  // if the user exists -> show a Sign Out button and their information
  if (session) {
    return (
      <>
        <Button type={"button"} onClick={() => signOut()}>
          Logout (
          {!session?.user?.name
            ? session.user.email
            : `${session?.user?.name}  ${session?.user?.lastName}`}
          )
        </Button>

        {/* Pass session info to server component */}
      </>
    );
  } else {
    return (
      <>
        <button
          onClick={() => signIn()}
          type="button"
          className="btn from-gray-900 to-slate-800 bg-150 bg-x-25"
        >
          {status == "loading" ? <FiLoader className="animate-spin" /> : "Login"}
        </button>
      </>
    );
  }

  // if a user doesn't exist -> show a Sign In button
}
