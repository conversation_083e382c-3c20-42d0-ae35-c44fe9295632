import CdrTableWrapper from "./component/table";
import Card from "../../../component/card";
import DateContextWrapper from "~/component/dateRangePicker/dateContextWrapper";
import DatePickerPresets from "~/component/dateRangePicker/datePickerPresets";
import DateRangePicker from "~/component/dateRangePicker/dateRange";


export default function Page() {
  return (
      <Card header_left={"CDRs"}>
      <DateContextWrapper>
          <Card
              header_left={``}
              header_right={
                  <>
                      <DatePickerPresets />
                      <DateRangePicker />
                  </>
              }
          >
      <CdrTableWrapper />
          </Card>
      </DateContextWrapper>
    </Card>
  );
}
