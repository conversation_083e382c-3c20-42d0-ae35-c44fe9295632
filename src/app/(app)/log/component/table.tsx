"use client";
import React from "react";
import type { Log } from "@prisma/client";
import { LogType } from "@prisma/client";
import type { Fetch<PERSON> } from "swr";
import useSWR from "swr";
import { AiFillWarning, AiOutlineReload } from "react-icons/ai";
import { MdErrorOutline } from "react-icons/md";
import { BsInfoLg } from "react-icons/bs";
import { VscDebugAltSmall } from "react-icons/vsc";
import Table from "../../../../utils/table/table";
import {
  BaseWithValueColDefParams,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";

const fetcher: Fetcher<Log[], string> = async (...args) => {
  const data = await fetch(...args, {
    method: "GET",
  });
  const logs = await data.json();
  return logs.map((log: any) => {
    return {
      ...log,
      timestamp: new Date(log.timestamp),
    };
  }) as Log[];
};

const TableWrapper = () => {
  const { data, error, isLoading, mutate } = useSWR<Log[], Error>(`/api/log`, fetcher);

  if (error) return <div>failed to load</div>;
  if (isLoading) return <div>loading...</div>;

  const formatDateTime = (date: Date) => {
    const hours = date.getHours().toString().padStart(2, "0"); // Stunde mit führender Null
    const minutes = date.getMinutes().toString().padStart(2, "0"); // Minute mit führender Null
    const day = date.getDate().toString().padStart(2, "0"); // Tag mit führender Null
    const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Monat mit führender Null (Januar = 0)
    const year = date.getFullYear().toString(); // Jahr

    const formattedDate = `${hours}:${minutes} ${day}.${month}.${year}`;
    return formattedDate;
  };

  const getIcon = (type: LogType) => {
    switch (type) {
      case LogType.WARN: {
        return <AiFillWarning size={40} fill={"rgb(234 179 8)"} className={"p-2 "} />;
      }
      case LogType.ERROR: {
        return <MdErrorOutline size={40} fill={"rgb(239 68 68)"} className={"p-2 "} />;
      }
      case LogType.INFO: {
        return <BsInfoLg size={40} fill={"rgb(59 130 246"} className={"p-2 "} />;
      }
      case LogType.DEBUG: {
        return <VscDebugAltSmall size={40} fill={"rgb(22 163 74)"} className={"p-2 "} />;
      }
    }
  };

  const columnDefs = [
    {
      field: "timestamp",
      headerName: "Date",
      filter: "agDateColumnFilter",
    },
    { field: "headline", headerName: "Headline" },
    { field: "type", headerName: "type" },
    { field: "topic", headerName: "topic" },
    { field: "message", headerName: "message" },
  ];

  const onChangeHandler = () => {
    mutate();
  };
  return (
    <>
      {data && (
        <div className={"h-[80vh] min-h-[500px]"}>
          <Table
            gridId={"log"}
            headerLeft={
              <button onClick={() => onChangeHandler()} className={"btn"}>
                <AiOutlineReload className={"mr-1"} />
                Reload
              </button>
            }
            columnDefs={columnDefs}
            rowData={data}
          />
        </div>
      )}
    </>
  );
};

export default TableWrapper;
