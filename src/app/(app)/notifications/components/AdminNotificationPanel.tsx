"use client";
import React, { useState, useEffect } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import But<PERSON> from "~/component/button";
import { FaPaperPlane, FaUsers, Fa<PERSON>ser, FaUserTie, FaChevronDown, FaChevronUp } from "react-icons/fa";
import { NotificationType, Role } from "@prisma/client";

interface NotificationFormData {
  message: string;
  type: NotificationType;
  targetType: "user" | "ou" | "role";
  targetId: string;
}

interface User {
  id: string;
  name: string;
  email: string;
}

interface OU {
  id: string;
  name: string;
}

interface AdminNotificationPanelProps {}

const AdminNotificationPanel: React.FC<AdminNotificationPanelProps> = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [ous, setOus] = useState<OU[]>([]);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<NotificationFormData>({
    defaultValues: {
      type: NotificationType.INFO,
      targetType: "user",
    },
  });

  const targetType = watch("targetType");

  // Load users and OUs on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load users
        const usersResponse = await fetch("/api/admin/users");
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData);
        }

        // Load OUs
        const ousResponse = await fetch("/api/admin/ous");
        if (ousResponse.ok) {
          const ousData = await ousResponse.json();
          setOus(ousData);
        }
      } catch (err) {
        console.error("Error loading data:", err);
      }
    };

    loadData();
  }, []);

  const onSubmit: SubmitHandler<NotificationFormData> = async (data) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch("/api/admin/notifications/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        setSuccess(`Benachrichtigung erfolgreich an ${result.count} Benutzer gesendet.`);
        reset();
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Fehler beim Senden der Benachrichtigung");
      }
    } catch (err) {
      setError("Netzwerkfehler beim Senden der Benachrichtigung");
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: NotificationType) => {
    switch (type) {
      case NotificationType.INFO:
        return "🔵";
      case NotificationType.WARNING:
        return "🟡";
      case NotificationType.ERROR:
        return "🔴";
      case NotificationType.SUCCESS:
        return "🟢";
      default:
        return "🔵";
    }
  };

  const getTypeLabel = (type: NotificationType) => {
    switch (type) {
      case NotificationType.INFO:
        return "Info";
      case NotificationType.WARNING:
        return "Warnung";
      case NotificationType.ERROR:
        return "Fehler";
      case NotificationType.SUCCESS:
        return "Erfolg";
      default:
        return "Info";
    }
  };

  const getRoleLabel = (role: Role) => {
    switch (role) {
      case Role.ADMIN:
        return "Administratoren";
      case Role.CPO:
        return "CPO Benutzer";
      case Role.CARD_MANAGER:
        return "Kartenmanager";
      case Role.CARD_HOLDER:
        return "Karteninhaber";
      case Role.USER:
        return "Benutzer";
      default:
        return role;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      {/* Header - Always visible */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded-t-lg"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <FaPaperPlane className="mr-2 text-blue-500" size={20} />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Admin: Benachrichtigung senden
            </h3>
            {!isExpanded && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Klicken Sie hier, um Benachrichtigungen an Benutzer, OUs oder Rollen zu senden
              </p>
            )}
          </div>
        </div>
        <div className="text-gray-500 dark:text-gray-400">
          {isExpanded ? <FaChevronUp size={16} /> : <FaChevronDown size={16} />}
        </div>
      </div>

      {/* Collapsible Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Nachricht *
          </label>
          <textarea
            {...register("message", { required: "Nachricht ist erforderlich" })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Geben Sie Ihre Nachricht ein..."
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
          )}
        </div>

        {/* Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Typ
          </label>
          <select
            {...register("type")}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {Object.values(NotificationType).map((type) => (
              <option key={type} value={type}>
                {getTypeIcon(type)} {getTypeLabel(type)}
              </option>
            ))}
          </select>
        </div>

        {/* Target Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Zielgruppe
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                {...register("targetType")}
                type="radio"
                value="user"
                className="mr-2"
              />
              <FaUser className="mr-1" />
              Einzelner Benutzer
            </label>
            <label className="flex items-center">
              <input
                {...register("targetType")}
                type="radio"
                value="ou"
                className="mr-2"
              />
              <FaUsers className="mr-1" />
              Organisationseinheit
            </label>
            <label className="flex items-center">
              <input
                {...register("targetType")}
                type="radio"
                value="role"
                className="mr-2"
              />
              <FaUserTie className="mr-1" />
              Rolle
            </label>
          </div>
        </div>

        {/* Target Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {targetType === "user" && "Benutzer auswählen"}
            {targetType === "ou" && "Organisationseinheit auswählen"}
            {targetType === "role" && "Rolle auswählen"}
            {" *"}
          </label>

          {targetType === "user" && (
            <select
              {...register("targetId", { required: "Auswahl ist erforderlich" })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Benutzer auswählen...</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </option>
              ))}
            </select>
          )}

          {targetType === "ou" && (
            <select
              {...register("targetId", { required: "Auswahl ist erforderlich" })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Organisationseinheit auswählen...</option>
              {ous.map((ou) => (
                <option key={ou.id} value={ou.id}>
                  {ou.name}
                </option>
              ))}
            </select>
          )}

          {targetType === "role" && (
            <select
              {...register("targetId", { required: "Auswahl ist erforderlich" })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Rolle auswählen...</option>
              {Object.values(Role).map((role) => (
                <option key={role} value={role}>
                  {getRoleLabel(role)}
                </option>
              ))}
            </select>
          )}

          {errors.targetId && (
            <p className="mt-1 text-sm text-red-600">{errors.targetId.message}</p>
          )}
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <p className="text-green-700 dark:text-green-300">{success}</p>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:opacity-50"
              >
                {loading ? "Wird gesendet..." : "Benachrichtigung senden"}
              </Button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default AdminNotificationPanel;
