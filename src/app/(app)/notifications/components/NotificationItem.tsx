"use client";
import React from "react";
import { <PERSON>a<PERSON><PERSON>, FaInfoCircle, FaExclamationTriangle, FaTimesCircle, FaCheckCircle } from "react-icons/fa";
import { NotificationType } from "@prisma/client";
import { SystemNotification } from "~/component/top/NotificationDropdown";

interface NotificationItemProps {
  notification: SystemNotification;
  onMarkAsRead: (id: string) => void;
}

const getTypeIcon = (type: NotificationType) => {
  switch (type) {
    case NotificationType.INFO:
      return <FaInfoCircle className="text-blue-500" size={20} />;
    case NotificationType.WARNING:
      return <FaExclamationTriangle className="text-yellow-500" size={20} />;
    case NotificationType.ERROR:
      return <FaTimesCircle className="text-red-500" size={20} />;
    case NotificationType.SUCCESS:
      return <FaCheckCircle className="text-green-500" size={20} />;
    default:
      return <FaInfoCircle className="text-blue-500" size={20} />;
  }
};

const getTypeColor = (type: NotificationType) => {
  switch (type) {
    case NotificationType.INFO:
      return "border-l-blue-500";
    case NotificationType.WARNING:
      return "border-l-yellow-500";
    case NotificationType.ERROR:
      return "border-l-red-500";
    case NotificationType.SUCCESS:
      return "border-l-green-500";
    default:
      return "border-l-blue-500";
  }
};

const getTypeBadgeColor = (type: NotificationType) => {
  switch (type) {
    case NotificationType.INFO:
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
    case NotificationType.WARNING:
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
    case NotificationType.ERROR:
      return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
    case NotificationType.SUCCESS:
      return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
    default:
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
  }
};

const formatTime = (date: Date) => {
  const now = new Date();
  const notificationDate = new Date(date);
  const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return "Gerade eben";
  if (diffInMinutes < 60) return `vor ${diffInMinutes} Min`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `vor ${diffInHours} Std`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `vor ${diffInDays} Tag${diffInDays > 1 ? 'en' : ''}`;

  return notificationDate.toLocaleDateString('de-DE', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getTypeLabel = (type: NotificationType) => {
  switch (type) {
    case NotificationType.INFO:
      return "Info";
    case NotificationType.WARNING:
      return "Warnung";
    case NotificationType.ERROR:
      return "Fehler";
    case NotificationType.SUCCESS:
      return "Erfolg";
    default:
      return "Info";
  }
};

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
}) => {
  const handleClick = () => {
    if (!notification.gelesen) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <div
      className={`relative border-l-4 ${getTypeColor(notification.type)} ${
        !notification.gelesen 
          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
          : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
      } cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg shadow-sm`}
      onClick={handleClick}
    >
      <div className="flex items-start p-4">
        {/* Type Icon */}
        <div className="mr-4 mt-1 flex-shrink-0">
          {getTypeIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeBadgeColor(notification.type)}`}>
                {getTypeLabel(notification.type)}
              </span>
              {!notification.gelesen && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                  Neu
                </span>
              )}
            </div>
            {!notification.gelesen && (
              <div className="ml-2 h-3 w-3 rounded-full bg-blue-500 flex-shrink-0"></div>
            )}
          </div>

          <p className={`text-sm ${!notification.gelesen ? 'font-semibold' : 'font-normal'} text-gray-900 dark:text-white mb-2`}>
            {notification.nachricht}
          </p>

          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <FaClock className="mr-1" />
            {formatTime(notification.createdAt)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationItem;
