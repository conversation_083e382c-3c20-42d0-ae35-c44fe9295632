import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import Card from "~/component/card";
import NotificationsList from "./components/NotificationsList";
import AdminNotificationPanel from "./components/AdminNotificationPanel";

const NotificationsPage = async () => {
  const session = await getServerSession(authOptions);
  const isAdmin = session?.user?.role === Role.ADMIN;

  return (
    <>
      <h1 className="mb-4 text-2xl font-bold dark:text-white">
        Benachrichtigungen
      </h1>

      {isAdmin && (
        <div className="mb-6">
          <AdminNotificationPanel />
        </div>
      )}

      <Card>
        <NotificationsList />
      </Card>
    </>
  );
};

export default NotificationsPage;
