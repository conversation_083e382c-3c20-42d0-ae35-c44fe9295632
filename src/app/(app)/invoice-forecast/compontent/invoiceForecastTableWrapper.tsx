"use client";

import React, { useContext, useEffect, useState } from "react";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import type { GridApi, GridOptions, GridReadyEvent, ICellRendererParams } from "ag-grid-community";
import { LicenseManager } from "ag-grid-enterprise";
import type {
  BaseWithValueColDefParams,
  ColDef,
  ColGroupDef,
} from "ag-grid-community/dist/lib/entities/colDef";

import { FaFileCsv, FaSpinner } from "react-icons/fa";
import handleDownload from "../utile/downloadCSV";
import Table from "~/utils/table/table";
import DateContext from "~/component/dateRangePicker/dateContext";
import Button from "~/component/button";
import { userStore } from "~/server/zustand/store";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";

LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

const InvoiceForecastTableWrapper = () => {
  const { startDate, endDate } = useContext(DateContext);
  const { data: session } = useSession();
  const [rowData, setRowData] = useState([]);
  const userStoreState = userStore();
  const [gridApi, setGridApi] = useState<GridApi | undefined>();
  const handleGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };
  useEffect(() => {
    if (gridApi) {
      gridApi.showLoadingOverlay();
    }
    if (startDate && endDate) {
      // Wandle die Daten in Strings im Format "YYYY-MM-DD" um
      const start = startDate ? startDate.toLocaleDateString("sv") : undefined;
      const end = endDate ? endDate.toLocaleDateString("sv") : undefined;

      // Erstelle das URL-Objekt und füge die Query-Parameter hinzu
      const url = new URL("/api/invoice/generateForecast", window.location.origin);
      url.searchParams.set("startDate", String(start));
      url.searchParams.set("endDate", String(end));
      const fetchData = async (url: URL) => {
        fetch(url)
          .then((res) => res.json())
          .then((data) => {
            setRowData(data);
          });
      };

      setTimeout(() => {
        //add a small delay for ou change to be done on server side
        fetchData(url);
        if (gridApi) {
          gridApi.hideOverlay();
        }
      }, 500);
    }
  }, [startDate, endDate, userStoreState]);

  function twoDecimalPlacesFormatter(params: BaseWithValueColDefParams): string {
    return `${Math.round(params.value * 100) / 100}` || "";
  }

  function twoDecimalPlacesFormatterWithCurrency(params: BaseWithValueColDefParams): string {
    return `€ ${Math.round(params.value * 100) / 100}` || "";
  }

  const [loading, setLoading] = useState(false);

  const ActionCellRenderer = (params: ICellRendererParams) => {
    const { startDate, endDate, setStartDate, setEndDate } = useContext(DateContext);

    if (!params.data?.contact) {
      return <></>;
    }
    const contactTarifList = [{ contact: params.data.contact, tarifName: params.data.Tariff_Name }];
    return (
      <>
        <span
          className={"cursor-pointer"}
          onClick={() => {
            handleDownload(contactTarifList, startDate, endDate, setLoading);
            return;
          }}
        >
          <FaFileCsv />
        </span>
        {loading && <p>Loading...</p>}
      </>
    );
  };

  const columnDefs = [
    { field: "contact", headerName: "EMP" },
    { field: "Tariff_Name", headerName: "Tarif" },
    { field: "sessions", headerName: "Sessions", aggFunc: "sum" },
    {
      field: "kwh",
      headerName: "kWh",
      aggFunc: "sum",

      valueFormatter: twoDecimalPlacesFormatter,
    },
    {
      field: "price",
      headerName: "Preis",
      aggFunc: "sum",
      valueFormatter: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "invalidSessions",
      headerName: "Invalid Sessions",
      aggFunc: "sum",
    },
    {
      field: "invalidKwh",
      headerName: "Invalid kWh",
      aggFunc: "sum",
      valueFormatter: twoDecimalPlacesFormatter,
    },
    {
      field: "energyCost",
      headerName: "EnergyCost",
      aggFunc: "sum",

      valueFormatter: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "grossMargin",
      headerName: "GrossMargin",
      aggFunc: "sum",

      valueFormatter: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: (params: ICellRendererParams) => ActionCellRenderer(params),
    },
  ];

  const handleDownloadAllCSV = () => {
    const contactTarifList = rowData.map(({ contact, Tariff_Name }: any) => {
      return {
        contact,
        tarifName: Tariff_Name,
      };
    });

    handleDownload(contactTarifList, startDate, endDate, setLoading);
  };

  return (
    <>
      <Table
        headerLeft={
          session?.user?.role == Role.ADMIN ? (
            <div className={"flex hidden justify-end gap-1 lg:block"}>
              <a
                href={`/api/invoice/downloadTaxReport?startDate=${startDate
                  .toLocaleString("sv-SE")
                  .slice(0, 10)}&endDate=${endDate.toLocaleString("sv-SE").slice(0, 10)}`}
                className={"btn mr-1 bg-primary"}
              >
                <FaFileCsv size={18} className={"mr-1"} /> Export EMP Rechnungen
              </a>
              <Button disabled={loading} onClick={() => handleDownloadAllCSV()}>
                {loading ? (
                  <FaSpinner size={18} className={"mr-1"} />
                ) : (
                  <FaFileCsv size={18} className={"mr-1"} />
                )}
                Download all CSV
              </Button>
            </div>
          ) : (
            <></>
          )
        }
        gridId={"invoice_forecast"}
        rowData={rowData}
        columnDefs={columnDefs}
        groupIncludeFooter={true}
        groupIncludeTotalFooter={true}
        animateRows={true}
        onGridReady={handleGridReady}
      />
    </>
  );
};

export default InvoiceForecastTableWrapper;
