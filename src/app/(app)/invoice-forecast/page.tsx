import InvoiceForecastTableWrapper from "./compontent/invoiceForecastTableWrapper";
import React from "react";
import Card from "../../../component/card";

import prisma from "~/server/db/prisma";
import DatePickerPresets from "~/component/dateRangePicker/datePickerPresets";
import DateRangePicker from "~/component/dateRangePicker/dateRange";
import DateContextWrapper from "~/component/dateRangePicker/dateContextWrapper";
import { getServerSession } from "next-auth";
import { Role } from "@prisma/client";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import NotFound from "~/app/(app)/not-found";

export const revalidate = 0;

const getLastCdr = async () => {
  const cdr = await prisma.cdr.findFirst({
    orderBy: {
      End_datetime: "desc",
    },
  });
  return cdr;
};

const Page = async () => {
  const cdr = await getLastCdr();
  const session = await getServerSession(authOptions);

  if (session?.user?.role !== Role.ADMIN) {
    return <NotFound />;
  }
  return (
    <>
      <DateContextWrapper>
        <h1 className={"mb-0 font-bold dark:text-white"}>
          <>{session?.user?.role == Role.ADMIN ? "Rechnungsvorschau" : "Gutschriftvorschau"} </>
        </h1>
        <Card
          header_left={`Datenstand: ${cdr?.End_datetime.toLocaleString("DE")}`}
          header_right={
            <>
              <DatePickerPresets />
              <DateRangePicker />
            </>
          }
        >
          <InvoiceForecastTableWrapper />
        </Card>
      </DateContextWrapper>
    </>
  );
};

export default Page;
