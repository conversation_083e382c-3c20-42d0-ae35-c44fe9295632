import RealtimeTable from "~/app/(app)/component/realtimeTable";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { getPaymentMethods } from "~/server/stripe/paymentUtils";
import { getEMPCardForUser } from "~/utils/user/empcard";

import OnboardingOverview from "~/app/(app)/component/OnboardingOverview";
import { CardHolderDashboard } from "~/app/(app)/component/CardHolderDashboard";
import { WelcomeText } from "~/app/(app)/component/WelcomeText";

export const revalidate = 3600;

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user) {
    return <div className="mx-auto w-full p-6">Auth missing</div>;
  }

  if (
    session.user.role == Role.USER ||
    session.user.role == Role.ADMIN ||
    session.user.role == Role.CARD_MANAGER ||
    session.user.role == Role.CPO
  ) {
    return (
      <>
        <RealtimeTable />
      </>
    );
  }

  if (session.user.role == Role.CARD_HOLDER) {
    const empCards = await getEMPCardForUser();
    const paymentMethods = await getPaymentMethods();

    const cardOrdered = empCards?.length > 0;
    const cardsActive = empCards?.some((card) => card.physicalCardId);
    const preDeliveredNotActive = empCards?.some((card) => card.preDelivered && !card.active);
    const paymentMethodActive = paymentMethods?.length > 0;

    return (
      <div>
        <>
          <WelcomeText />
          <OnboardingOverview
            preDeliveredNotActive={preDeliveredNotActive}
            cardsActive={cardsActive}
            cardOrdered={cardOrdered}
            paymentMethodActive={paymentMethodActive}
          />
        </>
      </div>
    );
    /*return (
      <div>
       {(!cardOrdered || !paymentMethodActive || !cardsActive) && (
          <>
            <WelcomeText />
            <OnboardingOverview
              cardsActive={cardsActive}
              cardOrdered={cardOrdered}
              paymentMethodActive={paymentMethodActive}
            />
          </>
        )}

        {cardsActive && cardOrdered && paymentMethodActive && <CardHolderDashboard />}
      </div>
    );*/
  }
};
export default Page;
