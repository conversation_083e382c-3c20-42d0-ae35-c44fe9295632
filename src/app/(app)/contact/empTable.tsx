"use client";

import { AgGridReact } from "ag-grid-react";
import React, { useState } from "react";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type {
  ColDef,
  ColGroupDef,
  ValueFormatterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import { HiMagnifyingGlass } from "react-icons/hi2";
import Link from "next/link";
import { Provider } from "@prisma/client";

const ContactTable = ({ data }: any) => {
  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <Link href={`contact/${params.data.id}`}>
          <HiMagnifyingGlass />
        </Link>
      </>
    );
  };

    const SEPACellRendrer = (params: ICellRendererParams) => {
        return (
            <>
                <Link href={`contact/${params.data.id}`}>
                    <HiMagnifyingGlass />
                </Link>
            </>
        );
    };

  const columnDefs = [
    { field: "name" },

    {
      field: "providers",
      //hide: data?.find((contact) => contact.cpo) ? true : false
      valueFormatter: (params: ValueFormatterParams) => {
        return params.value
          .map((provider: Provider) => `${provider.providerCountryId}*${provider.providerId}`)
          .join();
      },
    },
    { field: "companyName" },
    { field: "invoiceMail" },
    { field: "cdrMail" },
    { field: "note" },
    { field: "action", headerName: "Aktion", cellRenderer: CellRenderer },
  ];

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 30,
  };

  return (
    <>
      <div className="ag-theme-alpine" style={{ width: "100%", height: 500 }}>
        <AgGridReact
          gridOptions={defaultBackendGridOptions}
          onRowDataChanged={(api) => {
            api.api.sizeColumnsToFit();
            return api;
          }}
          rowData={Object.values(data)}
          columnDefs={columnDefs}
          onGridSizeChanged={(params) => {
            params.api.sizeColumnsToFit();
          }}
        />
      </div>
    </>
  );
};

export default ContactTable;
