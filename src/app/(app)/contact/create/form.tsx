"use client";
import Card from "../../../../component/card";
import { useRouter } from "next/navigation";
import React, { useMemo, useState, useTransition } from "react";
import type { SubmitHandler } from "react-hook-form";
import { Controller, useForm } from "react-hook-form";
import type { Provider } from "@prisma/client";
import { InvoiceLanguageCode, Prisma } from "@prisma/client";
import MultiSelect from "~/component/MultiSelect";
import { GiPerspectiveDiceSixFacesRandom } from "react-icons/gi";
import crypto from "crypto";

type ContactWithProviders = Prisma.ContactGetPayload<{
  include: {
    providers: true;
  };
}>;
// providers ersetzt, damit es mit dem MultiSelect sauber definiert ist.
// sonst ist onChange etc nicht so easy
type ContactFormValues = Omit<ContactWithProviders, "providers"> & {
  providers: string[];
};

const ContactCreateFrom = ({ providers }: { providers: Provider[] }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const allOptions = useMemo(() => {
    const options = providers?.map((provider) => {
      return {
        label: `${provider.providerCountryId}*${provider.providerId}`,
        value: `${provider.providerCountryId}*${provider.providerId}`,
      };
    });
    return options;
  }, [providers]);
  const { watch, setValue, register, handleSubmit, control } = useForm<ContactFormValues>();
  const cpo = watch("cpo");
  const onSubmit: SubmitHandler<ContactFormValues> = async (contact) => {
    // Mutate external data source
    const result = await fetch(`/api/contact`, {
      method: "POST",
      body: JSON.stringify({ contact: contact }),
    });
    if (result.status == 200) {
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push("/contact");
      });
    }
  };

  return (
    <Card
      header_left={"Neuen Contact (EMP oder CPO) anlegen"}
      header_right={<button type={"button"} onClick={() => router.back()}></button>}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="-mx-3 flex flex-wrap">
          <div className="w-6/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Name"
            >
              Name
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("name")}
                type="text"
                placeholder={"Example"}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-6/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="companyName"
            >
              Firmen Name
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("companyName")}
                type="text"
                placeholder={"Example GmbH"}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
        </div>
        <div className="-mx-3 flex flex-wrap">
          <div className="w-3/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Name"
            >
              Kundennummer
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("customerNumber")}
                type="text"
                placeholder={""}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>

          <div className="w-3/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Name"
            >
              Lieferantennummer
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("supplierNumber")}
                type="text"
                placeholder={""}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>

          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Name"
            >
              E-Mail Sprache
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <select
                {...register("invoiceLanguageCode")}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              >
                {Object.values(InvoiceLanguageCode).map((lang) => {
                  return (
                    <>
                      <option key={lang} value={lang}>
                        {lang}
                      </option>
                    </>
                  );
                })}
              </select>
            </div>
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="iban"
              >
                IBAN
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("iban")}
                  type="text"
                  id={"iban"}
                  placeholder={""}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
        </div>
        <div className="-mx-3 flex flex-wrap">
          {!cpo && (
            <div className="w-1/2 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="empIds"
              >
                EMP Ids
              </label>
              <Controller
                name="providers" // Der Name des Feldes in den Formulardaten
                control={control}
                render={({ field }) => (
                  <MultiSelect
                    onChange={(
                      val: {
                        value: string[];
                      }[],
                    ) => field.onChange(val.map((v) => v.value))}
                    value={allOptions.filter((option) => field?.value?.includes(option.value))}
                    options={allOptions}

                    // Falls nötig, zusätzliche Props für das MultiSelect
                  />
                )}
              />
            </div>
          )}
          <div className="w-1/2 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="cpo"
            >
              CPO
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("cpo")}
                id={"cpo"}
                className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
                type="checkbox"
              />
            </div>
          </div>
          <div className="w-3/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="sepaHash"
            >
              Einmaliger SEPA Einladungs-Hash
            </label>
            <div className="relative flex w-full flex-row items-stretch rounded-lg">
              <input
                id={"sepaHash"}
                {...register("sepaHash")}
                type="text"
                placeholder={""}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
              <GiPerspectiveDiceSixFacesRandom
                className={`rounded-2xl`}
                title={"Hash generieren"}
                onClick={() =>
                  setValue(
                    "sepaHash",
                    crypto
                      .createHash("sha256")
                      .update(crypto.randomBytes(32).toString("hex"))
                      .digest("hex"),
                  )
                }
                size={30}
              />
            </div>
          </div>
        </div>

        <div className="-mx-3 flex flex-wrap">
          <div className="w-6/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="invoiceMail"
            >
              E-Mail für Rechnungen
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("invoiceMail")}
                type="text"
                placeholder={""}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-6/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="cdrMail"
            >
              E-Mail für CDR
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("cdrMail")}
                type="text"
                placeholder={""}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
        </div>
        <div className="-mx-3 flex flex-wrap">
          <div className="w-12/12 w-full max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="note"
            >
              Notizen
            </label>
            <div className="relative flex w-full min-w-full flex-wrap items-stretch rounded-lg">
              <textarea
                rows={8}
                {...register("note")}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
        </div>
        <div className="-mx-3 flex">
          <div className={"flex-3 w-full max-w-full px-3"}>
            <button
              type={"submit"}
              className="float-right mb-0 mt-16 inline-block cursor-pointer rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
            >
              Speichern
            </button>
          </div>
        </div>
      </form>
    </Card>
  );
};

export default ContactCreateFrom;
