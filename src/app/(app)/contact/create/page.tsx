"use server";
import ContactCreateFrom from "./form";
import prisma from "~/server/db/prisma";

const getUnmappedProviders = async () => {
  const providers = await prisma?.provider.findMany({ where: { contactId: null } });
  return providers;
};

const ContactCreate = async () => {
  const unmappedProviders = await getUnmappedProviders();
  return (
    <>
      <ContactCreateFrom providers={unmappedProviders}></ContactCreateFrom>
    </>
  );
};

export default ContactCreate;
