import React from "react";

import Card from "~/component/card";
import Link from "next/link";
import { AiOutlinePlusCircle } from "react-icons/ai";
import prisma from "../../../server/db/prisma";
import ContactTable from "~/app/(app)/contact/empTable";
import Button from "~/component/button";

import Provider from "~/app/(app)/contact/[slug]/compontent/Provider";
import { ContactWithIncludes } from "~/app/(app)/contact/[slug]/page";
import { Prisma } from "@prisma/client";

export type ContactProviders = Prisma.ContactGetPayload<{
  include: {
    providers: true;
  };
}>;
export const revalidate = 0; // revalidate every 30sec

const getContacts = async (): Promise<ContactProviders[]> => {
  return await prisma.contact.findMany({ include: { providers: true } });
};

export default async function Page() {
  const contacts = await getContacts();

  return (
    <>
      <Card
        header_left={"EMP & CPO Liste"}
        header_right={
          <Link href={"/contact/create"}>
            <Button type={"button"}>
              <AiOutlinePlusCircle className={"mr-2"} size={"1.5rem"} />
              New Contact
            </Button>
          </Link>
        }
      >
        <div className={"flex flex-col gap-5"}>
          <div>
            <h4 className={"text-primary"}>Emobility Service Provider</h4>
            <ContactTable
              data={JSON.parse(JSON.stringify(contacts.filter((contact) => !contact.cpo)))}
            />
          </div>
          <div>
            <h4 className={"text-primary"}>Charge Point Operator</h4>
            <ContactTable
              data={JSON.parse(JSON.stringify(contacts.filter((contact) => contact.cpo)))}
            />
          </div>
        </div>
        {/* <Provider contacts={contacts} /> */}
      </Card>
    </>
  );
}
