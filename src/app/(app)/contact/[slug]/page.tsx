import { IoIosRocket } from "react-icons/io";
import React from "react";

import Title from "./compontent/title";
import BasicInfo from "./compontent/basicInfo";
import FormContextWrapper from "./compontent/formContextWrapper";
import Address from "./compontent/address";
import { FaAddressCard } from "react-icons/fa";
import AcceptOffer from "./compontent/acceptOffer";
import prisma from "../../../../server/db/prisma";
import type { Prisma } from "@prisma/client";
import { getOUTree } from "~/server/model/tenants/func";

export const revalidate = 0; // revalidate every 30sec

const getContacts = async (slug: string) => {
  return prisma.contact.findUniqueOrThrow({
    where: {
      id: slug,
    },
    include: {
      contactAddress: true,
      contactEnergyResellers: true,
      providers: true,
      tarifs: {
        include: {
          tarif: true,
        },
      },
    },
  });
};

const getUnmappedProvider = async () => {
  return prisma.provider.findMany({ where: { contactId: null } });
};

export type ContactWithIncludes = Prisma.ContactGetPayload<{
  include: {
    contactAddress: true;
    contactEnergyResellers: true;
    tarifs: true;
    providers: true;
  };
}>;

const Page = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;

  const tenantsTree = await getOUTree();

  if (!slug) {
    return <>Missing params</>;
  }
  const unmappedProvider = await getUnmappedProvider();

  const contacts: ContactWithIncludes = JSON.parse(JSON.stringify(await getContacts(slug)));

  if (!contacts) {
    return <>No EMP found</>;
  }

  return (
    <div className={"-mx-3 mb-12 flex flex-wrap"}>
      <div className={"w-full max-w-full shrink-0 px-3 lg:w-9/12 lg:flex-0"}>
        <FormContextWrapper>
          <Title contact={contacts} />
          <BasicInfo
            contact={contacts}
            unmappedProvider={unmappedProvider}
            tenantsTree={tenantsTree}
          />
          <Address contact={contacts} />
          <AcceptOffer contact={contacts} />
        </FormContextWrapper>
      </div>
    </div>
  );
};

export default Page;
