"use client";

import React, { useState } from "react";
import Card from "~/component/card";

import { FaPlusCircle, FaTrash } from "react-icons/fa";
import But<PERSON> from "~/component/button";
import { ContactProviders } from "~/app/(app)/contact/page";
import MessageDialog from "~/app/(app)/util/MessageDialog";

interface Props {
  contacts: ContactProviders[];
}

const Provider = ({ contacts }: Props) => {
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [providerToBeRemoved, setProviderToBeRemoved] = useState<string>("");

  return (
    <div className={"mt-5"}>
      <h4 className={"text-primary"}>Provider Übersicht</h4>
      <div className="flex flex-wrap gap-2">
        {contacts?.map((contact, index) => (
          <Card
            className="w-1/4"
            key={index}
            header_right={
              <Button>
                <FaPlusCircle size={18} />
              </Button>
            }
            header_left={contact.companyName ?? contact.id}
          >
            <ul>
              {contact.providers?.map((provider, index) => (
                <li className={"flex items-center gap-1"} key={index}>
                  {provider.providerCountryId}*{provider.providerId}
                  <FaTrash
                    className={"hover:cursor-pointer"}
                    onClick={() => {
                      setProviderToBeRemoved(
                        `${provider.providerCountryId}*${provider.providerId}`,
                      );
                      setShowDialog(true);
                    }}
                    size={12}
                  />
                </li>
              ))}
              {contact.providers.length == 0 && <li>Keine Provider zugewiesen</li>}
            </ul>
          </Card>
        ))}
      </div>
      {showDialog && (
        <MessageDialog
          onNo={() => setShowDialog(false)}
          onYes={() => setShowDialog(false)}
          message={`${providerToBeRemoved} wirklich vom Konakt trennen?`}
          title="Zuweisung entfernen"
        ></MessageDialog>
      )}
    </div>
  );
};

export default Provider;
