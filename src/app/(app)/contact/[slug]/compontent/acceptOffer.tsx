"use client";

import React, { useState, useTransition } from "react";
import type { ContactAddress } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import type { ContactWithIncludes } from "../page";
import { AgGridReact } from "ag-grid-react";
import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type {
  ColDef,
  ColGroupDef,
  ValueFormatterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import { LicenseManager } from "ag-grid-enterprise";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";

import { TfiPencil } from "react-icons/tfi";
LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

interface Props {
  contact: ContactWithIncludes;
}

const AcceptOffer = ({ contact }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [newAddress, setNewAddress] = useState(false);

  const {
    getValues,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<ContactAddress>();

  const onSubmit: SubmitHandler<ContactAddress> = async (data) => {
    // Mutate external data source
    await fetch(`/api/contact/address`, {
      method: "POST",
      body: JSON.stringify({ contact: contact, address: data }),
    });
    reset(data);
    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
    setNewAddress(false);
  };

  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <span
          className={"cursor-pointer"}
          onClick={() => {
            setNewAddress(true);
            reset({
              ...params.node.data,
              validFrom: new Date(params.node.data.validFrom).toISOString().substring(0, 10),
              validTo: new Date(params.node.data.validTo).toISOString().substring(0, 10),
            });
            setTimeout(() => {
              document?.getElementById("address-form")?.scrollIntoView({ behavior: "smooth" });
            }, 100);
          }}
        >
          <TfiPencil />
        </span>
      </>
    );
  };

  function twoDecimalPlacesFormatter(params: ValueFormatterParams): string {
    return `${Math.round(params.value * 100) / 100}` || "";
  }

  function twoDecimalPlacesFormatterWithCurrency(params: ValueFormatterParams): string {
    return `€ ${Math.round(params.value * 100) / 100}` || "";
  }

  const [columnDefs, setColumnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: "tarif.id", width: 100 },
    { field: "tarif.name", width: 400 },
    { field: "tarif.sessionFee", headerName: "Preis pro Session" },
    { field: "tarif.kwh" },
    {
      field: "tarif.minChargingEnergy",
      headerName: "mindest Lademenge",
    },
    {
      field: "tarif.currentType",
      headerName: "LadesäulenType (AC/DC)",
    },
    {
      field: "tarif.validFrom",
      headerName: "Gültig ab",
      cellRenderer: (params: ICellRendererParams) => {
        return `${new Date(params.data.tarif.validTo).toLocaleDateString("de-DE")}`;
      },
    },
    {
      field: "tarif.validTo",
      headerName: "Gültig bis",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params.data.tarif.validTo).toLocaleDateString("de-DE")}`,
    },
  ]);

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 30,
  };

  return (
    <div
      className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
      id="basic-info"
    >
      <div className="mb-0 flex w-full justify-between rounded-t-2xl p-6">
        <h5 className="dark:text-white">Abonnierte Tarife</h5>
      </div>
      <div className="flex-auto p-6 pt-0">
        <div className="-mx-3 flex flex-wrap">
          <div className="w-12/12 w-full max-w-full flex-0 px-3">
            <div className="ag-theme-alpine" style={{ width: "100%", height: 250 }}>
              <AgGridReact
                gridOptions={defaultBackendGridOptions}
                onRowDataChanged={(api) => {
                  // api.api.sizeColumnsToFit();
                  return api;
                }}
                rowData={Object.values(contact.tarifs || [])}
                columnDefs={columnDefs}
                animateRows={true}
                onGridSizeChanged={(params) => {
                  params.api.sizeColumnsToFit();
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AcceptOffer;
