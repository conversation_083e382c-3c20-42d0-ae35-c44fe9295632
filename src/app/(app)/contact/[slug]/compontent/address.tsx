"use client";

import React, { useState, useTransition } from "react";
import type { ContactAddress } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import type { ContactWithIncludes } from "../page";
import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import { LicenseManager } from "ag-grid-enterprise";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import { AiOutlinePlusCircle } from "react-icons/ai";
import CheckboxCellRenderer from "~/component/agGrid/checkbox";
import { TfiPencil } from "react-icons/tfi";
import Table from "~/utils/table/table";
LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

interface Props {
  contact: ContactWithIncludes;
}

const Address = ({ contact }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [newAddress, setNewAddress] = useState(false);

  const {
    getValues,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<ContactAddress>();

  const onSubmit: SubmitHandler<ContactAddress> = async (data) => {
    // Mutate external data source
    await fetch(`/api/contact/address`, {
      method: "POST",
      body: JSON.stringify({ contact: contact, address: data }),
    });
    reset(data);
    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
    setNewAddress(false);
  };

  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <span
          className={"cursor-pointer"}
          onClick={() => {
            setNewAddress(true);
            reset({
              ...params.node.data,
              validFrom: new Date(params.node.data.validFrom).toISOString().substring(0, 10),
              validTo: new Date(params.node.data.validTo).toISOString().substring(0, 10),
            });
            setTimeout(() => {
              document?.getElementById("address-form")?.scrollIntoView({ behavior: "smooth" });
            }, 100);
          }}
        >
          <TfiPencil />
        </span>
      </>
    );
  };

  const [columnDefs, setColumnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    {
      field: "validFrom",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params?.data?.validFrom).toLocaleDateString("de-DE")}`,
      editable: false,
    },
    {
      field: "validTo",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params?.data?.validTo).toLocaleDateString("de-DE")}`,
      editable: false,
    },
    { field: "street", editable: false },
    { field: "streetNr", editable: false },
    { field: "city", editable: false },
    { field: "zip", editable: false },
    { field: "country", editable: false },
    { field: "ustId", editable: false },
    {
      field: "isNetInvoice",
      cellRenderer: CheckboxCellRenderer,
      editable: false,
    },
    {
      field: "ustIdValid",
      cellRenderer: CheckboxCellRenderer,
      editable: false,
    },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: CellRenderer,
      editable: false,
    },
  ]);

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 30,
  };

  return (
    <div
      className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
      id="basic-info"
    >
      <div className="mb-0 flex w-full justify-between rounded-t-2xl p-6">
        <h5 className="dark:text-white">Adress Daten</h5>
        <button
          onClick={() => {
            reset({
              validFrom: undefined,
              validTo: undefined,
              street: null,
              streetNr: null,
              ustIdValid: null,
              ustId: null,
              id: undefined,
              city: null,
              country: null,
              zip: null,
              isNetInvoice: null,
            });
            setNewAddress(true);
            setTimeout(() => {
              document?.getElementById("address-form")?.scrollIntoView({ behavior: "smooth" });
            }, 100);
          }}
          type={"button"}
          className="mb-0  cursor-pointer justify-self-end rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
        >
          <AiOutlinePlusCircle className={"mr-2"} size={"1rem"} />
          New
        </button>
      </div>
      <div className="flex-auto  p-6 pt-0">
        <div className="-mx-3 flex ">
          <div className="w-12/12 h-[500px] w-full flex-0 overflow-hidden px-3">
            <Table
              rowData={Object.values(contact.contactAddress)}
              gridOptions={defaultBackendGridOptions}
              columnDefs={columnDefs}
            />
          </div>
        </div>
        <div id={"address-form"}>
          {newAddress && (
            <form onSubmit={handleSubmit(onSubmit)}>
              <>
                <input
                  disabled={!getValues("id")}
                  type={"text"}
                  {...register("id")}
                  className={"hidden"}
                />
                <div className="mb-0 rounded-t-2xl py-6">
                  <h5 className="dark:text-white">
                    {getValues("id") ? "Adresse bearbeiten" : "Neue Adresse"}
                  </h5>
                </div>
                <div className="-mx-3 flex flex-wrap">
                  <div className="w-6/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Name"
                    >
                      Gültig ab
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("validFrom")}
                        type="date"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="w-6/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Firmen Name"
                    >
                      Gültig bis
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("validTo")}
                        type="date"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                </div>
                <div className="-mx-3 mt-6 flex flex-wrap">
                  <div className="w-3/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Name"
                    >
                      Straße
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("street")}
                        type="text"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="w-2/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Firmen Name"
                    >
                      Hausnummer
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("streetNr")}
                        type="text"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="w-2/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Firmen Name"
                    >
                      PLZ
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("zip")}
                        type="text"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="w-2/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Firmen Name"
                    >
                      Stadt
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("city")}
                        type="text"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="w-3/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="land"
                    >
                      Land
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("country")}
                        type="text"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                </div>
                <div className="-mx-3 mt-6 flex flex-wrap">
                  <div className="w-4/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Name"
                    >
                      USt-IdNr
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("ustId")}
                        type="text"
                        className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="w-2/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Firmen Name"
                    >
                      Valid Ust-ID
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("ustIdValid")}
                        type="checkbox"
                        className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200  bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
                      />
                    </div>
                  </div>
                  <div className="w-2/12 max-w-full flex-0 px-3">
                    <label
                      className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                      htmlFor="Firmen Name"
                    >
                      Netto Rechnung
                    </label>
                    <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                      <input
                        {...register("isNetInvoice")}
                        type="checkbox"
                        className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
                      />
                    </div>
                  </div>
                  <div className="w-4/12 w-full max-w-full flex-0 px-3">
                    <button
                      type={"submit"}
                      className="float-right mb-0 mt-6 cursor-pointer justify-self-end rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
                    >
                      {getValues("id") ? "" : "Neue"} Adresse Speichern
                    </button>
                  </div>
                </div>
              </>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default Address;
