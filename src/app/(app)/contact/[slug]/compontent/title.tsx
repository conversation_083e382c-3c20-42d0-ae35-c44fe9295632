"use client";
import { MdCorporateFare } from "react-icons/md";
import React, { useContext } from "react";
import { Contact } from "@prisma/client";
import FormContext from "./formContext";

interface Props {
  contact: Contact;
}

const Title = ({ contact }: Props) => {
  const { edit, setEdit } = useContext(FormContext);

  return (
    <div
      className="relative flex min-w-0 flex-auto flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-4 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
      id="profile"
    >
      <div className="-mx-3 flex flex-wrap items-center justify-center">
        <div className="w-4/12 max-w-full flex-0 px-3 sm:w-auto">
          <div className="relative inline-flex h-19 w-19 items-center justify-center rounded-xl text-base text-white transition-all duration-200 ease-soft-in-out">
            <MdCorporateFare className={"text-xl text-black"} size={"3rem"} />
          </div>
        </div>
        <div className="my-auto w-8/12 max-w-full flex-0 px-3 sm:w-auto">
          <div className="h-full">
            <h5 className="mb-1 font-bold dark:text-white">{contact.name}</h5>
            <p className="mb-0 text-sm font-semibold leading-normal">{contact.companyName}</p>
          </div>
        </div>
        <div className="mt-4 flex max-w-full shrink-0 px-3 sm:ml-auto sm:mt-0 sm:w-auto sm:flex-0">
          <label
            profile-visibility-toggle-label=""
            htmlFor="profile-visibility-toggle"
            className="mb-0 ml-1 inline-block cursor-pointer text-sm font-normal text-slate-700 dark:text-white/80"
          >
            <small>Edit</small>
          </label>
          <div className="mb-0.5 ml-2 block min-h-6 pl-12">
            <input
              checked={edit}
              onClick={() => setEdit(!edit)}
              profile-visibility-toggle=""
              className="relative float-left -ml-12 mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
              type="checkbox"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Title;
