"use client";
import React, { useEffect, useMemo, useState } from "react";
import Table from "../../../../utils/table/table";
import type { ICellRendererParams } from "ag-grid-community";
import { useForm, useWatch } from "react-hook-form";

import { MdOutlineSyncDisabled } from "react-icons/md";
import { FaFileInvoiceDollar, FaTimes } from "react-icons/fa";
import {
  dateRenderer,
  dateTimeRenderer,
  twoDecimalPlacesFormatterWithCurrency,
} from "../../../../utils/table/formatter";

import type { GridApi, GridOptions, GridReadyEvent } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";

import type { Invoice } from "@prisma/client";
import type {
  ValueFormatterParams,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import Modal from "~/component/modal/modal";
import { GrPowerReset } from "react-icons/gr";
import TransactionInvoiceMapperModal from "~/app/(app)/qonto-transactions/component/TransactionInvoiceMapperModal";

export const revalidate = 0;
const QontoTransactionsTable = () => {
  const [rowData, setRowData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [gridApi, setGridApi] = useState<GridApi | undefined>(undefined);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const loadInvoiceData = async () => {
    const rowData = await fetch("/api/invoice");
    try {
      const data = await rowData.json();

      setInvoices(data);
    } catch (error) {
      console.log(error);
    }
  };
  const setIgnore = async (transaction_id: string) => {
    const response = await fetch(`/api/qonto/ignore?transaction_id=${transaction_id}`);
    gridApi?.showLoadingOverlay();
    setTimeout(() => {
      void loadTransactionData();
    }, 1000);
  };

  const loadTransactionData = async () => {
    const fetchedData = await fetch("/api/qonto?onlyUnpaid=true");
    try {
      const data = await fetchedData.json();
      setRowData(data);
    } catch (error) {
      console.log(error);
    }
  };
  const ActionCellRenderer = (params: ICellRendererParams) => {
    if (params.node.group) {
      return <></>;
    }
    return (
      <>
        <FaFileInvoiceDollar
          title={"Mit Rechnungen verknüpfen"}
          className={"ml-5 cursor-pointer"}
          onClick={() => {
            openModal();
            setSelectedRow(params.data);
          }}
        />
        {!params.data?.ignore_for_mapping && (
          <MdOutlineSyncDisabled
            className={"ml-5 cursor-pointer"}
            title={"Ignorieren"}
            onClick={() => {
              confirm("Transaktion wirklich als ignorieren markieren?") &&
                setIgnore(params.data.transaction_id);
            }}
          />
        )}
      </>
    );
  };

  const columnDefs = [
    { headerName: "Transaction ID", field: "transaction_id" },
    {
      headerName: "Eingegangen am",
      field: "settled_at",
      cellRenderer: dateTimeRenderer,
      sort: "desc" as const, //TS bug
    },
    { headerName: "Label", field: "label" },
    {
      headerName: "Betrag",
      field: "amount",
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    { headerName: "Reference", field: "reference" },
    {
      headerName: "Kontobewegung",
      field: "side",
      filter: "agSetColumnFilter",
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.side === "credit" ? "Einnahme" : "Ausgabe";
      },
      aggFunc: "count",
    },

    {
      headerName: "Ignoriert",
      field: "ignore_for_mapping",
      filter: "agSetColumnFilter",
      aggFunc: "count",
      valueGetter: (params: ValueGetterParams) => {
        return params?.data?.ignore_for_mapping ? "Ja" : "Nein";
      },
    },
    {
      headerName: "Action",
      cellRenderer: ActionCellRenderer,
    },
  ];
  useEffect(() => {
    void loadTransactionData();
  }, []);

  useEffect(() => {
    if (isModalOpen) {
      loadInvoiceData();
    } else {
      if (gridApi) {
        gridApi.showLoadingOverlay();
      }
      loadTransactionData();
    }
  }, [isModalOpen]);

  return (
    <>
      <TransactionInvoiceMapperModal
        isOpen={isModalOpen}
        invoices={invoices}
        selectedRow={selectedRow}
        onClose={closeModal}
      />

      <Table
        gridId={"qonto"}
        columnDefs={columnDefs}
        rowData={rowData}
        onFirstDataRendered={(params) => {
          params.api.getFilterInstance("ignore_for_mapping")?.setModel({
            values: ["Nein"],
          });
          params.api.getFilterInstance("side")?.setModel({
            values: ["Einnahme"],
          });
          params.api.onFilterChanged();
        }}
        groupIncludeTotalFooter={true}
      />
    </>
  );
};

export default QontoTransactionsTable;
