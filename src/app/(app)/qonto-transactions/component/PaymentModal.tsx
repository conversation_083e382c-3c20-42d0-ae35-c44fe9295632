import React, { useEffect, useState } from "react";
import type { Grid<PERSON>pi, GridOptions, GridReadyEvent } from "ag-grid-community";
import { dateRenderer } from "~/utils/table/formatter";
import { AgGridReact } from "ag-grid-react";

import type { QontoTransaction } from ".prisma/client";
import Modal from "~/component/modal/modal";

export interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (invoiceId: string, paymentDate: Date, paymentAmount: number) => void;
  selectedRow: any;
  transactions: QontoTransaction[];
  isStripe?: boolean;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isStripe,
  isOpen,
  onClose,
  onSave,
  selectedRow,
  transactions,
}) => {
  const [paymentDate, setPaymentDate] = useState<Date>(new Date());
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [selectedId, setSelectedId] = useState<string>("");
  const [showTable, setShowTable] = useState<boolean>(false);
  const [gridApi, setGridApi] = useState<GridApi | undefined>();
  const [sumGrossTotal, setSumGrossTotal] = useState<number>(0);

  const handleGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const handleSelectionChanged = () => {
    const selectedRows = gridApi?.getSelectedRows();
    const sumGross = selectedRows?.reduce((total, row) => total + row.amount, 0);
    setSumGrossTotal(sumGross);
    // Hier kannst du weitere Aktionen beim Ändern der Auswahl durchführen
  };

  useEffect(() => {
    if (isStripe) {
      setShowTable(true);
    }
  }, []);

  useEffect(() => {
    if (selectedRow) {
      setPaymentAmount(selectedRow.sumGross);
      setSelectedId(selectedRow.id);
    }
  }, [selectedRow]);

  const handleSubmit = async () => {
    // detail mapping
    if (showTable && gridApi) {
      const selectedRows = gridApi.getSelectedRows();
      gridApi.deselectAll();

      if (isStripe) {
        await fetch(`/api/qonto/mapStripePayouts`, {
          method: "POST",
          body: JSON.stringify({
            payoutId: selectedId,
            transactionId: selectedRows[0]?.transaction_id,
          }),
        });
      } else {
        await fetch(`/api/qonto/mapInvoices`, {
          method: "POST",
          body: JSON.stringify({
            invoiceIds: [selectedId],
            transactionIds: selectedRows.map((row) => row.transaction_id),
            multipleInvoicesFlag: false, // if multiple invoices are mapped to 1 transaction 1 otherwise false
          }),
        });
      }
    } else {
      // manual edit (no mapping)
      onSave(selectedId, paymentDate, paymentAmount);
    }

    onClose();
  };

  const columnDefs = [
    {
      headerName: "",
      field: "checkbox",
      width: 50,
      checkboxSelection: true,
    },
    { headerName: "Von", field: "label", initialWidth: 200 },
    { headerName: "Amount", field: "amount", initialWidth: 120, filter: "agNumberColumnFilter" },
    {
      headerName: "Datum",
      field: "emitted_at",
      cellRenderer: dateRenderer,
      initialWidth: 120,

      sort: "desc" as const,
    },
    { headerName: "Verwendungszweck", field: "reference", flex: 1 },
  ];

  const gridOptions: GridOptions = {
    //rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
    },
    rowHeight: 25,
  };

  return (
    <dialog
      className="left-1/2 top-1/2 z-10 w-full -translate-x-1/2 -translate-y-1/2 transform overflow-hidden rounded-lg bg-white shadow-xl"
      open={isOpen}
    >
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full  rounded-md bg-white p-6">
          <h2>{isStripe ? "Payout zuweisen" : "Zahlung zuweisen"}</h2>
          <h3 className="text-lg font-medium">
            <div> {isStripe ? "" : selectedRow?.subject}</div>
            <div>{isStripe ? `Id: ${selectedRow?.id} ` : selectedRow?.invoiceNumber}</div>
            <div>Betrag {isStripe ? selectedRow?.amount / 100 : selectedRow?.sumGross}</div>
          </h3>
          <hr className={"my-2 h-px border-1 bg-gray-200 dark:bg-gray-700"} />
          {!isStripe && (
            <div className="mb-1 w-2/12 max-w-full flex-0">
              <label className="text-xs font-bold text-slate-700 dark:text-white/80">
                Detailerfassung
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  onClick={() => {
                    setShowTable(!showTable);
                  }}
                  type="checkbox"
                  className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
                />
              </div>
            </div>
          )}
          {!showTable ? (
            <>
              <div className="mt-4">
                <label htmlFor="paymentDate" className="block text-sm font-medium">
                  Zahlungsdatum
                </label>
                <input
                  type="date"
                  id="paymentDate"
                  value={paymentDate.toISOString().substring(0, 10)}
                  onChange={(e) => setPaymentDate(new Date(e.target.value))}
                  className="mt-2 block w-full rounded border px-2"
                />
              </div>
              <div className="mt-4">
                <label htmlFor="paymentAmount" className="block text-sm font-medium">
                  Bezahlte Menge
                </label>
                <input
                  type="number"
                  id="paymentAmount"
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(parseFloat(e.target.value))}
                  className="mt-2 block w-full rounded border px-2"
                />
              </div>
            </>
          ) : (
            <>
              <h4 className="text-lg font-medium">
                {" "}
                Summe der ausgewählten Rechnungen:{" "}
                <span className={"font-bold"}> {sumGrossTotal?.toFixed(2)}</span>
              </h4>
              <div className="ag-theme-alpine" style={{ width: 800, height: 400 }}>
                <AgGridReact
                  rowData={transactions}
                  columnDefs={columnDefs}
                  gridOptions={gridOptions}
                  onGridReady={handleGridReady}
                  rowSelection={isStripe ? undefined : "multiple"}
                  onSelectionChanged={handleSelectionChanged}
                ></AgGridReact>
              </div>
            </>
          )}

          <div className="mt-6 flex">
            <button type="button" onClick={handleSubmit} className="btn mr-2">
              Speichern
            </button>
            <button type="button" onClick={onClose} className="btn">
              Abbrechen
            </button>
          </div>
        </div>
      </div>
    </dialog>
  );
};
