import React, { useEffect, useMemo, useState } from "react";
import type { Grid<PERSON><PERSON>, GridOptions, GridReadyEvent } from "ag-grid-community";
import { dateRenderer } from "~/utils/table/formatter";
import { useForm } from "react-hook-form";
import { AgGridReact } from "ag-grid-react";
import Modal from "~/component/modal/modal";
import { GrPowerReset } from "react-icons/gr";
import type { Invoice } from "@prisma/client";

interface TransactionInvoiceMapperModal {
  isOpen: boolean;
  onClose: () => void;
  selectedRow: any;
  invoices: Invoice[];
}

interface FormData {
  inputs: Record<string, string>; // Ein Objekt, bei dem jeder Schlüssel eine `id` ist und jeder Wert ein eingegebener Wert
}

const TransactionInvoiceMapperModal: React.FC<TransactionInvoiceMapperModal> = ({
  isOpen,
  onClose,
  selectedRow: selectedTransaction,
  invoices,
}) => {
  const [gridApi, setGridApi] = useState<GridApi | undefined>();
  const [sumGrossTotal, setSumGrossTotal] = useState<number>(0);
  const [amountNotMatching, setAmountNotMatching] = useState<boolean>(false);
  const [manualSum, setManualSum] = useState<number>(0);

  const fehlbetrag = useMemo(() => {
    return selectedTransaction?.amount - manualSum;
  }, [manualSum]);

  const handleGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };
  const handleSelectionChanged = () => {
    const selectedRows = gridApi?.getSelectedRows();
    const sumGross = selectedRows?.reduce((total, row) => total + row.sumGross, 0);
    setSumGrossTotal(sumGross);
  };

  const verifyAmount = () => {
    if (Math.abs(selectedTransaction.amount - sumGrossTotal) > 0.02) {
      const defaultValues = gridApi?.getSelectedRows().reduce((acc, item) => {
        acc[item.id] = item.sumGross;
        return acc;
      }, {});
      reset(defaultValues, { keepDefaultValues: false });
      setAmountNotMatching(true);
    } else {
      submitToServer(null);
    }
  };
  const submitToServer = async (manualIncoiceAmounts: FormData | null) => {
    // detail mapping
    if (gridApi) {
      const selectedInvoices = gridApi.getSelectedRows();
      gridApi.deselectAll();
      await fetch(`/api/qonto/mapInvoices`, {
        method: "POST",
        body: JSON.stringify({
          invoiceIds: selectedInvoices.map((row) => row.id),
          transactionIds: [selectedTransaction.transaction_id],
          multipleInvoicesFlag: true, // if multiple invoices are mapped to 1 transaction 1 otherwise false
          manualInvoiceAmounts: manualIncoiceAmounts,
        }),
      });
    } else {
      alert("GridApi not available - cannot save");
    }
    onClose();
  };
  const onFormSubmit = (data: FormData) => {
    submitToServer(data);
  };

  const columnDefs = [
    {
      headerName: "",
      field: "checkbox",
      width: 50,
      checkboxSelection: true,
    },
    { headerName: "Von", field: "label", initialWidth: 150 },
    { headerName: "RechnungsNr", field: "invoiceNumber" },
    { headerName: "EMP", field: "subject", initialWidth: 250 },
    {
      headerName: "Brutto €",
      field: "sumGross",
      initialWidth: 150,
      filter: "agNumberColumnFilter",
    },
    {
      headerName: "Datum",
      field: "invoiceDate",
      cellRenderer: dateRenderer,
      filter: "agDateColumnFilter",

      sort: "desc" as const,
    },
  ];

  const gridOptions: GridOptions = {
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      floatingFilter: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
    },
    rowHeight: 25,
  };

  const { register, resetField, handleSubmit, reset, watch } = useForm<FormData>();

  const watchedValues = watch();
  useEffect(() => {
    if (watchedValues) {
      const sum = Object.values(watchedValues)?.reduce(
        (total, value) => total + parseFloat(value),
        0,
      );
      // if any value is undefined sum will be NaN
      if (!isNaN(sum)) {
        setManualSum(sum.toFixed(2));
      }
    }
  }, [watchedValues]);

  return (
    <dialog
      className="left-1/2 top-1/2 z-10 w-full -translate-x-1/2 -translate-y-1/2 transform overflow-hidden rounded-lg bg-white shadow-xl"
      open={isOpen}
    >
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full  rounded-md bg-white p-6">
          <h2 className={"text-xl font-bold"}>Rechnung(en) zuweisen</h2>
          <h3 className="d-flex flex-row text-lg font-medium">
            <div>Von: {selectedTransaction?.label}</div>
            <div>Verwendungszweck: {selectedTransaction?.reference}</div>
            <div>Betrag: {selectedTransaction?.amount}</div>
          </h3>
          <hr className={"my-2 h-px border-1 bg-gray-950 dark:bg-gray-700"} />
          <h4 className="text-lg font-medium">
            {" "}
            Summe der ausgewählten Rechnungen:{" "}
            <span
              className={`font-bold ${
                Math.abs(selectedTransaction?.amount - sumGrossTotal) > 0.02
                  ? "text-red-500"
                  : "text-green-600"
              }`}
            >
              {sumGrossTotal?.toFixed(2)}
            </span>
          </h4>
          <div className="ag-theme-alpine" style={{ height: "300px", width: "100%" }}>
            <AgGridReact
              rowData={invoices}
              columnDefs={columnDefs}
              gridOptions={gridOptions}
              onGridReady={handleGridReady}
              rowSelection="multiple"
              onSelectionChanged={handleSelectionChanged}
            ></AgGridReact>
          </div>
          <div className="mt-6 flex">
            <button type="button" onClick={verifyAmount} className="btn mr-2">
              Speichern
            </button>
            <button
              type="button"
              onClick={() => {
                gridApi?.deselectAll();
                onClose();
              }}
              className="btn"
            >
              Abbrechen
            </button>
          </div>

          <Modal
            title={`Differenz erkannt bitte manuell zuweisen`}
            isOpen={amountNotMatching}
            onClose={() => {
              setAmountNotMatching(false);
            }}
          >
            <form onSubmit={handleSubmit(onFormSubmit)}>
              <div>
                {gridApi?.getSelectedRows().map((invoice) => (
                  <div key={invoice.id} className="mb-1 flex w-full items-center px-3">
                    <label
                      className="w-60 flex-shrink-0 whitespace-nowrap text-slate-700 dark:text-white/80"
                      htmlFor={invoice.invoiceNumber}
                    >
                      {invoice.invoiceNumber} {invoice.subject.replace("Roaming Invoice ", "")}:
                    </label>
                    <div className="relative ml-2 rounded-lg">
                      <input
                        {...register(invoice.id, { valueAsNumber: true })}
                        id={invoice.invoiceNumber}
                        type="number"
                        step={"0.01"}
                        className="block max-w-[120px] appearance-none  rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding py-1 pl-1 pr-5 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                      />
                      <span className="absolute inset-y-0 right-1 flex items-center">€</span>
                    </div>
                    <GrPowerReset
                      title={"Auf Rechnungsbetrag zurücksetzen"}
                      className={"ml-1 flex cursor-pointer"}
                      onClick={() =>
                        resetField(invoice.id, {
                          defaultValue: invoice?.sumGross,
                        })
                      }
                    ></GrPowerReset>
                  </div>
                ))}

                <div className="mb-1 flex w-full items-center border-t px-3 pt-1">
                  <label
                    className="w-60 flex-shrink-0 whitespace-nowrap font-bold text-slate-700 dark:text-white/80"
                    htmlFor="coutry_code"
                  >
                    Betrag der Transaktion: {selectedTransaction?.amount}€
                  </label>
                  <div className="relative ml-2 rounded-lg">
                    <span
                      className={`font-bold ${
                        fehlbetrag > 0 ? "text-green-DEFAULT-500" : "text-red-DEFAULT-600"
                      }`}
                    >
                      {fehlbetrag.toFixed(2)}
                      {"€ "} (Fehlbetrag)
                    </span>
                  </div>
                </div>

                <div className="mt-3 flex">
                  <button type="submit" className="btn mr-2">
                    Speichern
                  </button>
                  <button type="button" onClick={() => setAmountNotMatching(false)} className="btn">
                    Abbrechen
                  </button>
                </div>
              </div>
            </form>
          </Modal>
        </div>
      </div>
    </dialog>
  );
};
export default TransactionInvoiceMapperModal;
