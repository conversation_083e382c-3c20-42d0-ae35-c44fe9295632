import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Card from "~/component/card";
import Link from "next/link";
import Button from "~/component/button";
import { AiOutlinePlusCircle } from "react-icons/ai";
import EMPPriceTable from "./components/EMPPriceTable";
import LocationPriceTable from "./components/LocationPriceTable";
export const revalidate = 0;

async function getEMPPrices() {
  try {
    // Verwende den API-Endpunkt, um die Preise zu laden
    // Dies berücksichtigt automatisch die OU-Filterung
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/empPrice`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching EMP prices: ${response.statusText}`);
    }

    const empPrices = await response.json();
    return empPrices;
  } catch (error) {
    console.error("Error fetching EMP prices:", error);
    return [];
  }
}

async function getLocationPrices() {
  try {
    // Verwende den API-Endpunkt, um die Preise zu laden
    // Dies berücksichtigt automatisch die OU-Filterung
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/locationPrice`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching location prices: ${response.statusText}`);
    }

    const locationPrices = await response.json();
    return locationPrices;
  } catch (error) {
    console.error("Error fetching location prices:", error);
    return [];
  }
}

export default async function Page() {
  const session = await getServerSession(authOptions);
  if (!session) {
    return <NotFound />;
  }

  // Bestimme, ob der Benutzer ein Admin ist (für bedingte Anzeige von Elementen)
  const isAdmin = session?.user?.role === Role.ADMIN;

  const empPrices = await getEMPPrices();
  const locationPrices = await getLocationPrices();

  return (
    <>
      <div className={""}>
        <div
          className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-5 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
          id="emp-price-table"
        >
          <div className="mb-4 flex flex-col sm:flex-row sm:justify-between">
            <h4 className="sm:text-md mb-2 text-base text-primary">
              Übersicht EMP Preise (Adhoc-Tarif)
            </h4>
            {isAdmin && (
              <span>
                <Link href={"/adhoc-tarif/emp-price/new"}>
                  <Button type={"button"}>
                    <AiOutlinePlusCircle className={"mr-2"} size={"1.5rem"} />
                    Neuer EMP Preis
                  </Button>
                </Link>
              </span>
            )}
          </div>
          <EMPPriceTable data={empPrices} isAdmin={isAdmin} />
        </div>

        <div
          className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-5 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
          id="location-price-table"
        >
          <div className="mb-4 flex flex-col sm:flex-row sm:justify-between">
            <h4 className="sm:text-md mb-2 text-base text-primary">
              Übersicht Standort Preise (Adhoc-Tarif)
            </h4>
            {isAdmin && (
              <span>
                <Link href={"/adhoc-tarif/location-price/new"}>
                  <Button type={"button"}>
                    <AiOutlinePlusCircle className={"mr-2"} size={"1.5rem"} />
                    Neuer Standort Preis
                  </Button>
                </Link>
              </span>
            )}
          </div>
          <LocationPriceTable data={locationPrices} isAdmin={isAdmin} />
        </div>
      </div>
    </>
  );
}
