"use client";

import { useForm, SubmitHandler } from "react-hook-form";
import React, { useState } from "react";
import Button from "~/component/button";
import { FaSave } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { format } from "date-fns";

type EMPPriceType = {
  id?: string;
  start: string;
  end: string;
  energy_price: number;
  blocking_fee: number;
  blocking_fee_start: number;
  blocking_fee_max: number;
  session_fee: number;
  tax_rate: number;
  empId?: string;
  current_type: "AC" | "DC";
  contactId?: string; // ID des zugeordneten Contacts
};

type EMPType = {
  id: string;
  name: string;
  party_id: string;
  country_code: string;
};

type ContactType = {
  id: string;
  name?: string;
  companyName?: string;
  adhocEmpId?: string;
};

interface Props {
  empPrice?: EMPPriceType;
  emps: EMPType[];
  contacts: ContactType[];
  isAdmin: boolean;
}

const EMPPriceForm = ({ empPrice, emps, contacts, isAdmin }: Props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const formatDate = (date: Date | string) => {
    if (!date) return "";
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, "yyyy-MM-dd");
  };

  const defaultValues = empPrice
    ? {
        ...empPrice,
        start: formatDate(empPrice.start),
        end: formatDate(empPrice.end),
      }
    : {
        start: formatDate(new Date()),
        end: formatDate(new Date(new Date().setFullYear(new Date().getFullYear() + 1))),
        energy_price: 0.5,
        blocking_fee: 0.1,
        blocking_fee_start: 240,
        blocking_fee_max: 10,
        session_fee: 0,
        tax_rate: 19,
        current_type: "AC" as const,
      };

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<EMPPriceType>({
    defaultValues: defaultValues,
  });

  const onSubmit: SubmitHandler<EMPPriceType> = async (data) => {
    setIsSubmitting(true);
    try {
      // Speichern des EMPPrice
      const response = await fetch("/api/empPrice", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ data }),
      });

      if (response.ok) {
        // Wenn ein Contact ausgewählt wurde und wir eine empId haben, aktualisieren wir den Contact
        if (data.contactId && data.empId) {
          const updateContactResponse = await fetch("/api/contact/updateAdhocEmpId", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              contactId: data.contactId,
              adhocEmpId: data.empId
            }),
          });

          if (!updateContactResponse.ok) {
            console.error("Error updating contact:", await updateContactResponse.json());
            alert("Der Preis wurde gespeichert, aber die Zuordnung zum Contact konnte nicht aktualisiert werden.");
          }
        }

        router.push("/adhoc-tarif");
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error("Error submitting form:", errorData);
        alert("Fehler beim Speichern des Preises");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Fehler beim Speichern des Preises");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <label className="block text-sm font-medium text-gray-700">EMP</label>
          <select
            {...register("empId")}
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option value="">Bitte wählen</option>
            {emps.map((emp) => (
              <option key={emp.id} value={emp.id}>
                {emp.name} ({emp.party_id})
              </option>
            ))}
          </select>
          {errors.empId && (
            <p className="mt-1 text-sm text-red-600">{errors.empId.message}</p>
          )}
        </div>

        {isAdmin && (
          <div>
            <label className="block text-sm font-medium text-gray-700">Zuordnung zu Contact</label>
            <select
              {...register("contactId")}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">Bitte wählen</option>
              {contacts.map((contact) => (
                <option
                  key={contact.id}
                  value={contact.id}
                  // Markiere Contacts, die bereits eine adhocEmpId haben
                  className={contact.adhocEmpId ? "font-bold text-green-600" : ""}
                >
                  {contact.name || contact.companyName || "Unbenannter Contact"}
                  {contact.adhocEmpId === empPrice?.empId ? " (bereits diesem EMP zugeordnet)" :
                   contact.adhocEmpId ? " (bereits anderem EMP zugeordnet)" : ""}
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              Die ausgewählte EMP-ID wird dem Contact zugeordnet und für zukünftige Abfragen verwendet.
            </p>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700">Stromtyp</label>
          <select
            {...register("current_type")}
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option value="AC">AC</option>
            <option value="DC">DC</option>
          </select>
          {errors.current_type && (
            <p className="mt-1 text-sm text-red-600">{errors.current_type.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Energiepreis (€)</label>
          <input
            type="number"
            step="0.01"
            {...register("energy_price", { required: "Energiepreis ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.energy_price && (
            <p className="mt-1 text-sm text-red-600">{errors.energy_price.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Sessiongebühr (€)</label>
          <input
            type="number"
            step="0.01"
            {...register("session_fee", { required: "Sessiongebühr ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.session_fee && (
            <p className="mt-1 text-sm text-red-600">{errors.session_fee.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Blockierungsgebühr (€/min)</label>
          <input
            type="number"
            step="0.01"
            {...register("blocking_fee", { required: "Blockierungsgebühr ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.blocking_fee && (
            <p className="mt-1 text-sm text-red-600">{errors.blocking_fee.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Blockierung Start (Min)</label>
          <input
            type="number"
            {...register("blocking_fee_start", { required: "Blockierung Start ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.blocking_fee_start && (
            <p className="mt-1 text-sm text-red-600">{errors.blocking_fee_start.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Max. Blockierungsgebühr (€)</label>
          <input
            type="number"
            step="0.01"
            {...register("blocking_fee_max", { required: "Max. Blockierungsgebühr ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.blocking_fee_max && (
            <p className="mt-1 text-sm text-red-600">{errors.blocking_fee_max.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Steuersatz (%)</label>
          <input
            type="number"
            step="0.01"
            {...register("tax_rate", { required: "Steuersatz ist erforderlich", min: 0, max: 100 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.tax_rate && (
            <p className="mt-1 text-sm text-red-600">{errors.tax_rate.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Gültig ab</label>
          <input
            type="date"
            {...register("start", { required: "Gültig ab ist erforderlich" })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.start && (
            <p className="mt-1 text-sm text-red-600">{errors.start.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Gültig bis</label>
          <input
            type="date"
            {...register("end", { required: "Gültig bis ist erforderlich" })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.end && (
            <p className="mt-1 text-sm text-red-600">{errors.end.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          onClick={() => router.push("/adhoc-tarif")}
          className="inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Abbrechen
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          <FaSave className="mr-2" />
          {isSubmitting ? "Wird gespeichert..." : "Speichern"}
        </Button>
      </div>
    </form>
  );
};

export default EMPPriceForm;
