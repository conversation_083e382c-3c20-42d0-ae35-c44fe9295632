import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Card from "~/component/card";
import EMPPriceForm from "../../EMPPriceForm";
import { PrismaClient as PrismaClientMongo } from "~/../prismaMongoAdhoc/client";
import prisma from "~/server/db/prisma";

const prismaMongo = new PrismaClientMongo();

export const revalidate = 0;

async function getEMPs() {
  try {
    const emps = await prismaMongo.emp.findMany();
    return JSON.parse(JSON.stringify(emps));
  } catch (error) {
    console.error("Error fetching EMPs:", error);
    return [];
  }
}

async function getContacts() {
  try {
    const contacts = await prisma.contact.findMany({
      where: {
        cpo: true, // Nur Contacts mit cpo=true laden
      },
      select: {
        id: true,
        name: true,
        companyName: true,
        adhocEmpId: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
    return contacts;
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return [];
  }
}

async function getEMPPrice(id: string) {
  try {
    const empPrice = await prismaMongo.empPrice.findUnique({
      where: { id },
    });
    return JSON.parse(JSON.stringify(empPrice));
  } catch (error) {
    console.error("Error fetching EMP price:", error);
    return null;
  }
}

async function getContactByEmpId(empId: string) {
  try {
    const contact = await prisma.contact.findFirst({
      where: {
        adhocEmpId: empId,
        cpo: true, // Nur Contacts mit cpo=true berücksichtigen
      },
      select: { id: true },
    });
    return contact?.id || null;
  } catch (error) {
    console.error("Error fetching contact by empId:", error);
    return null;
  }
}

export default async function Page({ params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }

  const emps = await getEMPs();
  const contacts = await getContacts();
  const empPrice = await getEMPPrice(params.id);
  const isAdmin = session?.user?.role === Role.ADMIN;

  if (!empPrice) {
    return <NotFound />;
  }

  // Wenn die empId bereits einem Contact zugeordnet ist, setzen wir contactId
  if (empPrice.empId) {
    const contactId = await getContactByEmpId(empPrice.empId);
    if (contactId) {
      empPrice.contactId = contactId;
    }
  }

  return (
    <>
      <h1 className={"mb-4 font-bold dark:text-white"}>EMP Preis bearbeiten</h1>
      <Card>
        <EMPPriceForm empPrice={empPrice} emps={emps} contacts={contacts} isAdmin={isAdmin} />
      </Card>
    </>
  );
}
