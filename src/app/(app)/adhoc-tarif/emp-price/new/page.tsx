import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Card from "~/component/card";
import EMP<PERSON>riceForm from "../EMPPriceForm";
import { PrismaClient as PrismaClientMongo } from "~/../prismaMongoAdhoc/client";
import prisma from "~/server/db/prisma";

const prismaMongo = new PrismaClientMongo();

export const revalidate = 0;

async function getEMPs() {
  try {
    const emps = await prismaMongo.emp.findMany();
    return JSON.parse(JSON.stringify(emps));
  } catch (error) {
    console.error("Error fetching EMPs:", error);
    return [];
  }
}

async function getContacts() {
  try {
    const contacts = await prisma.contact.findMany({
      where: {
        cpo: true, // Nur Contacts mit cpo=true laden
      },
      select: {
        id: true,
        name: true,
        companyName: true,
        adhocEmpId: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
    return contacts;
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return [];
  }
}

export default async function Page() {
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }

  const emps = await getEMPs();
  const contacts = await getContacts();
  const isAdmin = session?.user?.role === Role.ADMIN;

  return (
    <>
      <h1 className={"mb-4 font-bold dark:text-white"}>Neuer EMP Preis</h1>
      <Card>
        <EMPPriceForm emps={emps} contacts={contacts} isAdmin={isAdmin} />
      </Card>
    </>
  );
}
