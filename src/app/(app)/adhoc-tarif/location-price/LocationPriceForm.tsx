"use client";

import { useForm, SubmitHandler } from "react-hook-form";
import React, { useState } from "react";
import Button from "~/component/button";
import { FaSave } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { format } from "date-fns";

type LocationPriceType = {
  id?: string;
  locationId?: string;
  start: string;
  end: string;
  energy_price: number;
  blocking_fee: number;
  current_type: "AC" | "DC";
  parking_price?: number;
  blocking_fee_start: number;
  blocking_fee_max: number;
  session_fee: number;
  tax_rate: number;
  empId?: string;
};

type LocationType = {
  id: string;
  name: string;
  party_id: string;
  country_code: string;
};

type EMPType = {
  id: string;
  name: string;
  party_id: string;
  country_code: string;
};

interface Props {
  locationPrice?: LocationPriceType;
  locations: LocationType[];
  emps: EMPType[];
}

const LocationPriceForm = ({ locationPrice, locations, emps }: Props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const formatDate = (date: Date | string) => {
    if (!date) return "";
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, "yyyy-MM-dd");
  };

  const defaultValues = locationPrice
    ? {
        ...locationPrice,
        start: formatDate(locationPrice.start),
        end: formatDate(locationPrice.end),
      }
    : {
        start: formatDate(new Date()),
        end: formatDate(new Date(new Date().setFullYear(new Date().getFullYear() + 1))),
        energy_price: 0.5,
        blocking_fee: 0.1,
        current_type: "AC" as const,
        parking_price: 0,
        blocking_fee_start: 240,
        blocking_fee_max: 10,
        session_fee: 0,
        tax_rate: 19,
      };

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LocationPriceType>({
    defaultValues: defaultValues,
  });

  const onSubmit: SubmitHandler<LocationPriceType> = async (data) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/locationPrice", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ data }),
      });

      if (response.ok) {
        router.push("/adhoc-tarif");
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error("Error submitting form:", errorData);
        alert("Fehler beim Speichern des Preises");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Fehler beim Speichern des Preises");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <label className="block text-sm font-medium text-gray-700">Standort</label>
          <select
            {...register("locationId")}
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option value="">Bitte wählen</option>
            {locations.map((location) => (
              <option key={location.id} value={location.id}>
                {location.name} ({location.party_id})
              </option>
            ))}
          </select>
          {errors.locationId && (
            <p className="mt-1 text-sm text-red-600">{errors.locationId.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">EMP</label>
          <select
            {...register("empId")}
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option value="">Bitte wählen</option>
            {emps.map((emp) => (
              <option key={emp.id} value={emp.id}>
                {emp.name} ({emp.party_id})
              </option>
            ))}
          </select>
          {errors.empId && (
            <p className="mt-1 text-sm text-red-600">{errors.empId.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Stromtyp</label>
          <select
            {...register("current_type")}
            className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          >
            <option value="AC">AC</option>
            <option value="DC">DC</option>
          </select>
          {errors.current_type && (
            <p className="mt-1 text-sm text-red-600">{errors.current_type.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Energiepreis (€)</label>
          <input
            type="number"
            step="0.01"
            {...register("energy_price", { required: "Energiepreis ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.energy_price && (
            <p className="mt-1 text-sm text-red-600">{errors.energy_price.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Sessiongebühr (€)</label>
          <input
            type="number"
            step="0.01"
            {...register("session_fee", { required: "Sessiongebühr ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.session_fee && (
            <p className="mt-1 text-sm text-red-600">{errors.session_fee.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Blockierungsgebühr (€/min)</label>
          <input
            type="number"
            step="0.01"
            {...register("blocking_fee", { required: "Blockierungsgebühr ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.blocking_fee && (
            <p className="mt-1 text-sm text-red-600">{errors.blocking_fee.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Parkgebühr (€/min)</label>
          <input
            type="number"
            step="0.01"
            {...register("parking_price", { min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.parking_price && (
            <p className="mt-1 text-sm text-red-600">{errors.parking_price.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Blockierung Start (Min)</label>
          <input
            type="number"
            {...register("blocking_fee_start", { required: "Blockierung Start ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.blocking_fee_start && (
            <p className="mt-1 text-sm text-red-600">{errors.blocking_fee_start.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Max. Blockierungsgebühr (€)</label>
          <input
            type="number"
            step="0.01"
            {...register("blocking_fee_max", { required: "Max. Blockierungsgebühr ist erforderlich", min: 0 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.blocking_fee_max && (
            <p className="mt-1 text-sm text-red-600">{errors.blocking_fee_max.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Steuersatz (%)</label>
          <input
            type="number"
            step="0.01"
            {...register("tax_rate", { required: "Steuersatz ist erforderlich", min: 0, max: 100 })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.tax_rate && (
            <p className="mt-1 text-sm text-red-600">{errors.tax_rate.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Gültig ab</label>
          <input
            type="date"
            {...register("start", { required: "Gültig ab ist erforderlich" })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.start && (
            <p className="mt-1 text-sm text-red-600">{errors.start.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Gültig bis</label>
          <input
            type="date"
            {...register("end", { required: "Gültig bis ist erforderlich" })}
            className="mt-1 block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          />
          {errors.end && (
            <p className="mt-1 text-sm text-red-600">{errors.end.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          onClick={() => router.push("/adhoc-tarif")}
          className="inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Abbrechen
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          <FaSave className="mr-2" />
          {isSubmitting ? "Wird gespeichert..." : "Speichern"}
        </Button>
      </div>
    </form>
  );
};

export default LocationPriceForm;
