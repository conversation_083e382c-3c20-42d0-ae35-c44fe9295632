import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Card from "~/component/card";
import LocationPriceForm from "../../LocationPriceForm";
import { PrismaClient as PrismaClientMongo } from "~/../prismaMongoAdhoc/client";

const prismaMongo = new PrismaClientMongo();

export const revalidate = 0;

async function getLocations() {
  try {
    const locations = await prismaMongo.location.findMany();
    return JSON.parse(JSON.stringify(locations));
  } catch (error) {
    console.error("Error fetching locations:", error);
    return [];
  }
}

async function getEMPs() {
  try {
    const emps = await prismaMongo.emp.findMany();
    return JSON.parse(JSON.stringify(emps));
  } catch (error) {
    console.error("Error fetching EMPs:", error);
    return [];
  }
}

async function getLocationPrice(id: string) {
  try {
    const locationPrice = await prismaMongo.locationPrice.findUnique({
      where: { id },
    });
    return JSON.parse(JSON.stringify(locationPrice));
  } catch (error) {
    console.error("Error fetching location price:", error);
    return null;
  }
}

export default async function Page({ params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }

  const locations = await getLocations();
  const emps = await getEMPs();
  const locationPrice = await getLocationPrice(params.id);

  if (!locationPrice) {
    return <NotFound />;
  }

  return (
    <>
      <h1 className={"mb-4 font-bold dark:text-white"}>Standort Preis bearbeiten</h1>
      <Card>
        <LocationPriceForm locationPrice={locationPrice} locations={locations} emps={emps} />
      </Card>
    </>
  );
}
