"use client";

import React from "react";
import Table from "~/utils/table/table";
import { dateRenderer, twoDecimalPlacesFormatterWithCurrency } from "~/utils/table/formatter";
import { FaEdit, FaTrash } from "react-icons/fa";
import { useRouter } from "next/navigation";

interface LocationPriceType {
  id: string;
  locationId?: string;
  start: Date;
  end: Date;
  energy_price: number;
  blocking_fee: number;
  current_type: "AC" | "DC";
  parking_price?: number;
  blocking_fee_start: number;
  blocking_fee_max: number;
  session_fee: number;
  tax_rate: number;
  empId?: string;
  location?: {
    id: string;
    name: string;
    party_id: string;
    country_code: string;
  };
  Emp?: {
    id: string;
    name: string;
    party_id: string;
    country_code: string;
  };
}

interface Props {
  data: LocationPriceType[];
  isAdmin?: boolean;
}

const LocationPriceTable = ({ data, isAdmin = false }: Props) => {
  const router = useRouter();

  const handleEdit = (id: string) => {
    router.push(`/adhoc-tarif/location-price/edit/${id}`);
  };

  const handleDelete = async (id: string) => {
    if (confirm("Sind Sie sicher, dass Sie diesen Preis löschen möchten?")) {
      try {
        const response = await fetch("/api/locationPrice", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ id }),
        });

        if (response.ok) {
          router.refresh();
        } else {
          alert("Fehler beim Löschen des Preises");
        }
      } catch (error) {
        console.error("Error deleting location price:", error);
        alert("Fehler beim Löschen des Preises");
      }
    }
  };

  const ActionCellRenderer = (params: any) => {
    return (
      <div className="flex space-x-2">
        <button
          onClick={() => handleEdit(params.data.id)}
          className="text-blue-500 hover:text-blue-700"
        >
          <FaEdit />
        </button>
        <button
          onClick={() => handleDelete(params.data.id)}
          className="text-red-500 hover:text-red-700"
        >
          <FaTrash />
        </button>
      </div>
    );
  };

  // Erstelle die Spalten-Definitionen
  let columnDefs = [
    {
      field: "location.name",
      headerName: "Standort Name",
      minWidth: 200,
      valueGetter: (params: any) => params.data?.location?.name || "N/A",
    },
    {
      field: "Emp.name",
      headerName: "EMP Name",
      minWidth: 200,
      valueGetter: (params: any) => params.data?.Emp?.name || "N/A",
    },
    {
      field: "current_type",
      headerName: "Stromtyp",
      minWidth: 100,
    },
    {
      field: "energy_price",
      headerName: "Energiepreis",
      minWidth: 150,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "session_fee",
      headerName: "Sessiongebühr",
      minWidth: 150,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "blocking_fee",
      headerName: "Blockierungsgebühr",
      minWidth: 150,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "parking_price",
      headerName: "Parkgebühr",
      minWidth: 150,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "blocking_fee_start",
      headerName: "Blockierung Start (Min)",
      minWidth: 180,
    },
    {
      field: "blocking_fee_max",
      headerName: "Max. Blockierungsgebühr",
      minWidth: 180,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "tax_rate",
      headerName: "Steuersatz (%)",
      minWidth: 150,
    },
    {
      field: "start",
      headerName: "Gültig ab",
      minWidth: 150,
      cellRenderer: dateRenderer,
    },
    {
      field: "end",
      headerName: "Gültig bis",
      minWidth: 150,
      cellRenderer: dateRenderer,
    },
    // Aktionen-Spalte nur für Admins anzeigen
    ...(isAdmin ? [{
      field: "action",
      headerName: "Aktionen",
      minWidth: 100,
      cellRenderer: ActionCellRenderer,
      sortable: false,
      filter: false,
    }] : []),
  ];

  return (
    <Table
      gridId={"location-prices"}
      groupIncludeTotalFooter={true}
      columnDefs={columnDefs}
      rowData={data}
    />
  );
};

export default LocationPriceTable;
