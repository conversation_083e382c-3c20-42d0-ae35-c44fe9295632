"use server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import ParkEventTable from "~/app/(app)/parkingsensor/events/ParkEventTable";
import { getDateStringFromDateObject } from "~/utils/date/date";
import React from "react";

interface ParkingEventWithSensor {
  device_id: bigint;
  timestamp: Date;
  state: boolean | null;
  battery: number | null;
  sensorTime: Date | null;
  evseId: string | null;
  // Fügen Sie hier weitere Felder hinzu, falls erforderlich
}

const getSensorDataEvents = async () => {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role != Role.ADMIN) {
    return [];
  }
  // Aktuelles Datum und Zeit
  const now = new Date();

  // Start der Woche (z.B. Montag)
  const weekStart = new Date(now);
  weekStart.setDate(weekStart.getDate() - weekStart.getDay() + 1); // Setzen auf Montag dieser Woche
  weekStart.setHours(0, 0, 0, 0); // Start des Tages
  const weekStartStr = getDateStringFromDateObject(weekStart);

  // Ende der Woche (z.B. Sonntag)
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekEnd.getDate() + 6); // Setzen auf Sonntag dieser Woche
  weekEnd.setHours(23, 59, 59, 999); // Ende des Tages
  const weekEndStr = getDateStringFromDateObject(weekEnd);
  const events: ParkingEventWithSensor = await prisma.$queryRawUnsafe(
    `SELECT pe.*, ps.evseId
  FROM ParkingEvent pe
  JOIN ParkingSensor ps ON pe.device_id = ps.device_id
  WHERE pe.timestamp >= '${weekStartStr}' AND pe.timestamp <= '${weekEndStr}'
  ORDER BY pe.timestamp DESC`,
  );
  return events;
};

const SensorEventPage = async () => {
  const events = await getSensorDataEvents();

  return (
    <>
      <h5>Sensor Events</h5>
      <ParkEventTable rowData={events} />
    </>
  );
};

export default SensorEventPage;
