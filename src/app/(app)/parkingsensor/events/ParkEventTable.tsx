"use client";
import Table from "~/utils/table/table";
import React, { useState } from "react";
import { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import { LuParkingCircle, LuParkingCircleOff } from "react-icons/lu";
import { ICellRendererParams } from "ag-grid-community";
import { IoHeart } from "react-icons/io5";
import { FaRegQuestionCircle, FaTools } from "react-icons/fa";

const ParkEventTable = ({ rowData: rowData }: { rowData: any }) => {
  const [columnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: "device_id", headerName: "Sensor ID" },
    { field: "evseId", headerName: "EvseId" },
    {
      field: "timestamp",
      headerName: "Timestamp",
      minWidth: 140,
      valueFormatter: (params) => {
        return params.value?.toLocaleString("de");
      },
    },
    {
      field: "sensorTime",
      headerName: "Sensortime",
      minWidth: 140,
      valueFormatter: (params) => {
        return params.value?.toLocaleString("de");
      },
    },
    { field: "battery", headerName: "Akku" },
    {
      field: "state",
      headerName: "Status",
      cellRenderer: (params: ICellRendererParams) => {
        {
          if (params.data.state == 0) {
            return <FaRegQuestionCircle size={25} color={"darkgray"} />;
          }
          if (params.data.state == 1) {
            return <LuParkingCircleOff size={25} className={"text-red-400"} />;
          }

          if (params.data.state == 2) {
            return <LuParkingCircle size={25} color={"green"} />;
          }
        }
      },
    },
    {
      field: "hearbeat",
      headerName: "Info",
      cellRenderer: (params: ICellRendererParams) => {
        {
          if (params?.data?.heartbeat) {
            return <IoHeart size={20} color={"red"} />;
          }
          if (params?.data?.init) {
            return <FaTools size={20} color={"darkgray"} />;
          }
          return <></>;
        }
      },
    },
  ]);
  return (
    <div className="mt-3" style={{ width: "100%", height: 800 }}>
      <Table gridId={"sensor_events"} columnDefs={columnDefs} rowData={rowData} />
    </div>
  );
};

export default ParkEventTable;
