import Card from "../../../component/card";

import prisma from "../../../server/db/prisma";
import { LuParkingCircle, LuParkingCircleOff } from "react-icons/lu";

import { loadChargepointstatus } from "~/pages/api/realtime2";
import { getOusBelowOu } from "~/server/model/ou/func";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import React from "react";
import { FaRegQuestionCircle } from "react-icons/fa";
import { IoHeart } from "react-icons/io5";
import { formatSecondsToUnit } from "~/utils/date/date";
export const revalidate = 0;

interface SensorData {
  evseId?: string;
  state?: number;
  name?: string;
  device_id?: string;
  lastUpdated?: Date;
  operationalStatus?: string;
}

interface Connector {
  operationalStatus: string;
  connectorNumber: string;
}
interface ChargePointStatus {
  id: string;
  connectors: Connector[];
}
const getStatusEmoticon = (operationalStatus: string | undefined) => {
  switch (operationalStatus) {
    case "Charging":
      return "⚡️";
    case "Available":
      return "🅿️";
    case "Preparing":
      return "🔌";
    case "SuspendedEV":
    case "SuspendedEVSE":
      return "⏸️";
    case "Finishing":
      return "🏁";
    default:
      return "unknown";
  }
};

const findOperationalStatus = (
  chargePointId: string | null | undefined,
  connectorNumber: string | null,
  chargepointStatusList: ChargePointStatus[] | undefined,
): string | undefined => {
  // Find the charge point with the given ID
  const chargePoint = chargepointStatusList?.find((cp) => cp.id === chargePointId);
  if (!chargePoint) {
    return undefined; // Charge point not found
  }

  // Find the connector with the given number
  const connector = chargePoint.connectors.find(
    (cn) => cn.connectorNumber.toString() === connectorNumber,
  );
  return getStatusEmoticon(connector?.operationalStatus); // Connector not found or return its operational status
};

const getSensors = async () => {
  const session = await getServerSession(authOptions);
  if (!session) {
    return {};
  }
  const ouId = session?.user.selectedOu.id;

  const ous = await getOusBelowOu(session?.user.selectedOu);
  const ouIds = ous.map((ou) => ou.id);

  const sensoren = await prisma.parkingSensor.findMany({
    where: {
      evse: {
        ouId: {
          in: ouIds,
        },
      },
    },
    include: {
      evse: {
        include: {
          location: true,
        },
      },
    },
  });

  if (!sensoren) {
    return {};
  }
  const sortedSensors = sensoren.sort((a, b) => {
    const strIdA = a?.evseId?.split("*").at(-1) ?? "";
    const strIdB = b?.evseId?.split("*").at(-1) ?? "";
    return Number(strIdA) - Number(strIdB);
  });

  const chargepointStatusList = await loadChargepointstatus(ouIds);
  if (!chargepointStatusList) {
    return {};
  }

  const result = sortedSensors.reduce((acc: { [locationId: string]: SensorData[] }, item) => {
    // Extrahieren Sie die benötigten Werte aus dem Objekt
    const locationName = item?.evse?.location?.name ?? "unknown location";

    // Erstellen Sie ein neues Objekt für die Werte
    const valueObject: SensorData = {
      evseId: item?.evseId ?? "Keine EVSE Id",
      state: item?.state,
      name: item?.name,
      device_id: item?.device_id.toString(),
      lastUpdated: item?.lastEventAt,
      operationalStatus: item?.evse
        ? findOperationalStatus(
            item.evse.chargePointId,
            item?.evse.connector,
            chargepointStatusList,
          )
        : "unknown",
    };

    // Fügen Sie das neue Objekt zum Array hinzu, das dem locationId-Schlüssel im Akkumulator zugeordnet ist
    if (!acc[locationName]) {
      acc[locationName] = []; // Initialisieren Sie ein neues Array, wenn dies das erste Mal ist
    }
    acc[locationName]?.push(valueObject);

    return acc;
  }, {});

  return result;
};

const ParksensorPage = async () => {
  const sensors = await getSensors();

  const getDuration = (lastUpdated: Date) => {
    const now = Date.now();
    const duration = (now - lastUpdated.getTime()) / 1000;
    return formatSecondsToUnit(duration);
  };
  return (
    <>
      <h5>
        <a className={"pl-4 text-secondary "} href={"/parkingsensor/events"}>
          Sensor Events ➡️
        </a>
      </h5>
      <div className={"flex flex-col gap-5"}>
        {Object.entries(sensors).map(([locationName, sensorArray], index) => (
          <>
            <div className={"flex flex-col  rounded-xl p-4 shadow-lg  md:w-1/2"}>
              <h5 className={"truncate"}>{locationName}</h5>
              <div className={"flex flex-row gap-1"}>
                {sensorArray.map((sensor, sensorIndex) => (
                  <div
                    key={sensorIndex}
                    className={"flex flex-col border-r-1 border-dashed px-1 text-center"}
                  >
                    <span className={"opacity-70"}>{sensor.operationalStatus ?? " "}</span>
                    <span
                      className={"cursor-pointer hover:scale-110"}
                      title={`${sensor.evseId}    ${
                        sensor?.lastUpdated?.toLocaleString("de") ?? ""
                      }`}
                    >
                      {sensor.state == 0 && (
                        <FaRegQuestionCircle className={""} size={25} color={"darkgray"} />
                      )}
                      {sensor.state == 2 && <LuParkingCircle size={25} color={"green"} />}
                      {sensor.state == 1 && (
                        <LuParkingCircleOff size={25} className={"text-red-400"} />
                      )}
                    </span>
                    <span>{sensor?.lastUpdated ? getDuration(sensor.lastUpdated) : ""}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        ))}
      </div>
      {Object.values(sensors).length === 0 && <div>Keine Sensoren verbaut</div>}
    </>
  );
};

export default ParksensorPage;
