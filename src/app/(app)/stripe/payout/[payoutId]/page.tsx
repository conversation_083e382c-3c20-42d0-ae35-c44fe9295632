import React from "react";
import StripePayoutDetailsTable from "../../component/stripePayoutDetailsTable";
import Card from "../../../../../component/card";
import Link from "next/link";

interface Props {
  params: {
    payoutId: string;
  };
}

const StripePayoutDetailsPage = async ({ params }: Props) => {
  return (
    <>
      <div>
        <h1 className={"mb-0 font-bold dark:text-white"}>
          <>Payout</>
        </h1>
        <Card
          header_left={
            <Link href={`https://dashboard.stripe.com/payouts/${params.payoutId}`}>
              <span>PayoutId: {params.payoutId}</span>
            </Link>
          }
        >
          <StripePayoutDetailsTable payoutId={params.payoutId} />
        </Card>
      </div>
    </>
  );
};

export default StripePayoutDetailsPage;
