"use client";
import React, { useEffect, useState } from "react";
import Table, { filterParams } from "../../../../utils/table/table";
import type { ICellRendererParams } from "ag-grid-community";
import { FaPlay, FaFileDownload, FaCheck, FaTimes } from "react-icons/fa";
import { MdAccountBalance } from "react-icons/md";
import { dateTimeRenderer } from "../../../../utils/table/formatter";
import type { ValueFormatterParams } from "ag-grid-community/dist/lib/entities/colDef";
import Link from "next/link";
import { HiMagnifyingGlass } from "react-icons/hi2";
import type { GridApi, GridReadyEvent } from "ag-grid-community";
import type { QontoTransaction } from ".prisma/client";
import { PaymentModal } from "~/app/(app)/qonto-transactions/component/PaymentModal";

export const revalidate = 0;

const StripePayoutTable = () => {
  const [transactions, setTransactions] = useState<QontoTransaction[]>([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [gridApi, setGridApi] = useState<GridApi | undefined>(undefined);
  const [rowData, setRowData] = useState([]);
  const loadPayoutData = async () => {
    const fetchedData = await fetch("/api/stripe/payout", {
      next: { revalidate: 0 },
    });
    try {
      const data = await fetchedData.json();
      setRowData(data);
      gridApi?.hideOverlay();
    } catch (error) {
      console.log(error);
      alert("Error fetching stripe payouts");
    }
  };

  const loadTransactionData = async () => {
    const fetchedData = await fetch("/api/qonto?onlyStripe=1", {
      next: { revalidate: 0 },
    });
    try {
      const transactions = (await fetchedData.json()) as QontoTransaction[];
      const sorted = transactions.sort((a, b) => a.amount - b.amount);

      setTransactions(sorted);
    } catch (error) {
      console.log(error);
    }
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSave = async (invoiceId: string, paymentDate: Date, paymentAmount: number) => {
    // Hier können Sie die Logik zum Speichern der Zahlungsinformationen hinzufügen
    const res = await fetch(`api/invoice/${invoiceId}`, {
      method: "PATCH",
      body: JSON.stringify({
        paidOnDate: paymentDate,
        paidAmount: paymentAmount,
      }),
    });
    if (res.status === 200) {
      void loadPayoutData();
    }
  };
  const triggerReport = async (payoutId: string) => {
    fetch(`/api/stripe/triggerPayoutReport?payoutId=${payoutId}`);
  };

  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        {params.data.FileRef &&
          Array.isArray(params.data.FileRef) &&
          params.data.FileRef.length > 0 && (
            <>
              <a
                className={"ml-5"}
                title={`Steuerberater Datei downloaden ${params.data.FileRef[0].name}`}
                href={`/api/stripe/downloadTaxReport?payoutId=${params.data.id}`}
              >
                <FaFileDownload color={"green"} />
              </a>
            </>
          )}
        {params.data.bankTransactions.length > 0 && params.data.FileRef.length == 0 && (
          <FaPlay
            className={"ml-5 hover:cursor-pointer"}
            color={"orange"}
            title={"Trigger Stripe Report"}
            onClick={() => {
              void triggerReport(params.data.id);
              params.api.showLoadingOverlay();

              setTimeout(() => {
                void loadPayoutData();
              }, 2000); // Verzögerung von 2000 Millisekunden (2 Sekunden)
            }}
          />
        )}

        {params.data?.bankTransactions?.length == 0 && (
          <MdAccountBalance
            className={"ml-5 cursor-pointer"}
            title={"Einer Transaktion zuweisen"}
            onClick={() => {
              openModal();
              setSelectedRow(params.data);
            }}
          />
        )}
        <Link href={`stripe/payout/${params?.data?.id}`}>
          <HiMagnifyingGlass className={"ml-5"} title={"Details"} />
        </Link>
      </>
    );
  };

  const columnsDefs = [
    { headerName: "PayoutId", field: "id" },
    {
      headerName: "Amount",
      field: "amount",
      valueFormater: (params: ValueFormatterParams) => {
        return params.value ? parseFloat(params.value) / 100 : params.value;
      },
    },
    {
      headerName: "Arrival Date",
      field: "arrival_date",
      filter: "agDateColumnFilter",
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      sort: "desc" as const,
    },
    {
      headerName: "Created",
      field: "created",
      filter: "agDateColumnFilter",
      filterParams: filterParams,
      cellRenderer: dateTimeRenderer,
    },
    {
      headerName: "Mit Bank verknüpft",
      field: "bankTransactionId",
      cellRenderer: (params: ICellRendererParams) => (
        <span>
          {params.data.bankTransactions.length > 0 ? (
            <FaCheck color={"green"} />
          ) : (
            <FaTimes color={"orange"} />
          )}
        </span>
      ),
    },
    { headerName: "Status", field: "status" },
    { headerName: "Action", cellRenderer: ActionCellRenderer },
  ];
  const handleGridReady = (params: GridReadyEvent) => {
    params.api.showLoadingOverlay();
    setGridApi(params.api);
  };

  useEffect(() => {
    void loadPayoutData();
  }, []);

  useEffect(() => {
    if (isModalOpen) {
      loadTransactionData();
    } else {
      if (gridApi) {
        gridApi.showLoadingOverlay();
      }
      loadPayoutData();
    }
  }, [isModalOpen]);

  return (
    <>
      <PaymentModal
        isStripe={true}
        isOpen={isModalOpen}
        onClose={closeModal}
        onSave={handleSave}
        selectedRow={selectedRow}
        transactions={transactions}
      />
      <div className="ag-theme-alpine" style={{ height: "400px", width: "100%" }}>
        <Table
          gridId={"payouts"}
          onGridReady={handleGridReady}
          columnDefs={columnsDefs}
          rowData={rowData}
        />
      </div>
    </>
  );
};

export default StripePayoutTable;
