"use client";
import React, { useEffect, useState } from "react";
import Table from "../../../../utils/table/table";
import type { ICellRendererParams } from "ag-grid-community";

interface Props {
  payoutId?: string;
}

const StripePayoutDetailsTable = (params: Props) => {
  const [rowData, setRowData] = useState([]);

  const loadPayoutItemData = async () => {
    if (params.payoutId) {
      const fetchedData = await fetch(`/api/stripe/payout/details?payoutId=${params.payoutId}`);
      try {
        const data = await fetchedData.json();
        setRowData(data);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const columnsDefs = [
    {
      headerName: "PayoutId",
      field: "automaticPayoutId",
      cellRenderer: (params: ICellRendererParams) => (
        <a
          href={`https://dashboard.stripe.com/payout/${params.value}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {params.value}
        </a>
      ),
    },
    {
      headerName: "PaymentIntentId",
      field: "paymentIntentId",
      enableRowGroup: true,
      cellRenderer: (params: ICellRendererParams) => (
        <a
          href={`https://dashboard.stripe.com/payments/${params.value}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {params.value}
        </a>
      ),
    },
    { headerName: "Betrag ohne Gebühr", field: "gross", aggFunc: "sum" },
    {
      headerName: "Betrag abzüglich Gebühr",
      field: "net",
      aggFunc: "sum",
    },
    { headerName: "Stripe Gebühr", field: "fee", aggFunc: "sum" },
    {
      headerName: "Kundenbetrag/Erstattung",
      field: "customerFacingAmount",
      aggFunc: "sum",
    },
    { headerName: "EVSE", field: "paymentMetadataEvse", enableRowGroup: true },
    { headerName: "Reporting Category", field: "reportingCategory" },
    {
      headerName: "CdrId",
      field: "paymentMetadataCdrId",
      cellRenderer: (params: ICellRendererParams) => params.value,
    },
    {
      headerName: "Adhoc Invoice Number",
      field: "adhocInvoiceNumber",
      enableRowGroup: true,
    },
  ];

  useEffect(() => {
    void loadPayoutItemData();
  }, []);

  return (
    <div className="ag-theme-alpine" style={{ height: "400px", width: "100%" }}>
      <Table columnDefs={columnsDefs} rowData={rowData} />
    </div>
  );
};

export default StripePayoutDetailsTable;
