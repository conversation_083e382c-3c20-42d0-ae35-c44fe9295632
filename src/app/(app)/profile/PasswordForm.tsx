"use client";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import React, { useState } from "react";
import { type SubmitHandler, useForm } from "react-hook-form";

interface FormProps {
  currentPassword: string;
  password: string;
  passwordRepeat: string;
}
export const PasswordForm = () => {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors, isSubmitting },
  } = useForm<FormProps>({});
  const [submissionSuccess, setSubmissionSuccess] = useState(false);

  const onSubmit: SubmitHandler<FormProps> = async (data) => {
    try {
      const res = await fetch("/api/user/changePassword", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data }),
      });

      if (res.status == 200) {
        setSubmissionSuccess(true);
      }
      // Hier könnten Sie einen Erfolgshandler hinzufügen
    } catch (error) {
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };
  const [showPassword, setShowPassword] = useState<boolean>(false);
  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={"flex w-full flex-col gap-1 rounded-lg bg-white transition-all "}
    >
      <div className="flex flex-col items-end gap-2">
        <div className="w-full">
          <label className="block text-sm font-medium">Aktuelles Passwort</label>
          <div className={"flex flex-row items-end gap-2"}>
            <input
              {...register("currentPassword")}
              type={`${showPassword ? "text" : "password"}`}
              className={` ${
                errors.currentPassword ? "border-red-500" : "border-gray-300"
              }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
            />
            <div className={""}>
              {showPassword ? (
                <FaEye onClick={() => setShowPassword(false)} size={20} />
              ) : (
                <FaEyeSlash onClick={() => setShowPassword(true)} size={20} />
              )}
            </div>
          </div>
        </div>

        <div className="w-full">
          <label className="block text-sm font-medium">Neues Passwort</label>
          <div className={"flex flex-row items-end gap-1"}>
            <input
              {...register("password", {
                validate: (value) => {
                  if (value.length < 8) {
                    return "Passwort muss mindeszens 8 Zeichen lang sein";
                  }
                  return (
                    [/[a-z]/, /[A-Z]/, /[0-9]/, /[^a-zA-Z0-9]/].every((pattern) =>
                      pattern.test(value),
                    ) ||
                    "Passwort muss Kleinbuchstaben, Großbuchstaben, Zahlen und Sonderzeichen enthalten"
                  );
                },
              })}
              type={`${showPassword ? "text" : "password"}`}
              className={` ${
                errors.password ? "border-red-500" : "border-gray-300"
              }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
            />
            <div className={""}>
              {showPassword ? (
                <FaEye onClick={() => setShowPassword(false)} size={20} />
              ) : (
                <FaEyeSlash onClick={() => setShowPassword(true)} size={20} />
              )}
            </div>
          </div>
        </div>
        <div className="w-full">
          <label className="block text-sm font-medium">Neues Passwort wiederholen</label>
          <div className={"flex flex-row items-end gap-1"}>
            <input
              {...register("passwordRepeat", {
                validate: (value) =>
                  value === getValues("password") || "Passwörter stimmen nicht überein",
              })}
              type={`${showPassword ? "text" : "password"}`}
              className={` ${
                errors.passwordRepeat ? "border-red-500" : "border-gray-300"
              }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
            />
            <div className={""}>
              {showPassword ? (
                <FaEye onClick={() => setShowPassword(false)} size={20} />
              ) : (
                <FaEyeSlash onClick={() => setShowPassword(true)} size={20} />
              )}
            </div>
          </div>
        </div>
      </div>
      <div>
        {errors.passwordRepeat && (
          <p className="text-xs text-red-500">{errors.passwordRepeat.message}!</p>
        )}
        {errors.password && <p className="text-xs text-red-500">{errors.password.message}!</p>}
      </div>
      <Button className={"w-full sm:max-w-64"} type="submit">
        Passwort ändern {isSubmitting && <FiLoader className="animate-spin" />}
      </Button>
      {submissionSuccess && (
        <div className="mt-4 text-green-600 transition-all">
          <p>Passwort aktualisiert!</p>
        </div>
      )}
    </form>
  );
};
