"use client";

import { useForm } from "react-hook-form";
import React, { useState } from "react";
import { UserWithOuAndAdress } from "~/types/prisma/user";

import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";

interface Props {
  user: UserWithOuAndAdress;
}

interface FormInputs {
  server?: string;
  id: string;
  name: string;
  lastName: string;
  street: string;
  streetNr: string;
  city: string;
  zip: string;
  country: string;
}

export const CardHolderProfileForm = ({ user }: Props) => {
  const address = user?.address.find(Boolean); //get first element
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty, isSubmitSuccessful, isLoading },
    setError,
  } = useForm<FormInputs>({
    defaultValues: {
      name: user.name,
      lastName: user.lastName,
      street: address?.street ?? "",
      streetNr: address?.streetNr ?? "",
      city: address?.city ?? "",
      zip: address?.zip ?? "",
      country: address?.country ?? "",
    },
  });
  const onSubmit = async (data: FormInputs) => {
    try {
      const response = await fetch("/api/user/updateProfile", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data, addressId: address?.id ?? "" }),
      });
      if (!response.ok) {
        setErrorMessage(await response.text());
        setSuccessMessage("");
      } else {
        setErrorMessage("");
        setSuccessMessage("Daten erfolgreich aktualisiert");
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
        setSuccessMessage("");
      } else {
        setErrorMessage("Fehler beim Speichern");
        setSuccessMessage("");
      }
    }
  };

  return (
    <div>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className={`w-full space-y-4  rounded-lg  bg-white transition-all`}
      >
        <div className="flex flex-col gap-2">
          <div className="">
            <label className="block text-sm font-medium">Email</label>
            <input
              value={user?.email}
              disabled={true}
              readOnly={true}
              className="disabled bg-eul-lightgray block w-full appearance-none rounded-lg
              border border-solid border-gray-300
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline
              focus:outline-none"
            />
          </div>
          {user?.name && (
            <>
              <div className="">
                <label className="block text-sm font-medium">Vorname</label>
                <input
                  {...register("name", { required: "Bitte einen Vornamen eingeben" })}
                  className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                />
                {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
              </div>
              <div className="">
                <label className="block text-sm font-medium">Nachname</label>
                <input
                  {...register("lastName", { required: "Bitte einen Nachnamen eingeben" })}
                  className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                />
                {errors.lastName && <p className={"text-red-500"}>{errors.lastName.message}</p>}
              </div>
              {user?.address && (
                <>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Land</label>
                      <select
                        {...register("country", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      >
                        <option value="Deutschland">Deutschland</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">PLZ</label>
                      <input
                        {...register("zip", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Ort</label>
                      <input
                        {...register("city", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2 md:col-span-2">
                      <label className="block text-sm font-medium">Straße</label>
                      <input
                        {...register("street", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Hausnummer</label>
                      <input
                        {...register("streetNr", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                </>
              )}
            </>
          )}
          {user?.name && (
            <Button
              disabled={(isSubmitting || isSubmitSuccessful) && !isDirty}
              className={"w-full sm:max-w-64"}
              type="submit"
            >
              Speichern {isSubmitting ? <FiLoader className="animate-spin" /> : ""}
            </Button>
          )}
        </div>
      </form>
      {successMessage && <span className={"text-green-600"}>{successMessage}</span>}
      {errorMessage && <span className={"text-red-600"}>{errorMessage}</span>}
    </div>
  );
};
