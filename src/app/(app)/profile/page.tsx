"use server";
import React from "react";
import { getServerSession } from "next-auth";
import prisma from "~/server/db/prisma";
import { CardHolderProfileForm } from "~/app/(app)/profile/CardHolderProfileForm";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Loading from "~/app/(app)/loading";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import { PasswordForm } from "~/app/(app)/profile/PasswordForm";

const getFullUser = async () => {
  const session = await getServerSession(authOptions);
  if (session?.user?.id) {
    const user = await prisma?.user.findUnique({
      where: { id: session.user.id },
      include: { address: true, ou: true },
    });
    return user;
  }
  return null;
};

const ProfilePage = async () => {
  const user = await getFullUser();
  if (!user) {
    return <>No session found</>;
  }

  return (
    <Card>
      <Headline title={"Benutzerprofil"} />
      <div
        className="border-primary relative mb-3 rounded-xl border bg-gray-100 px-4 py-3 text-primary sm:w-70/100"
        role="alert"
      >
        Hier können Sie ihr Benutzerprofil anpassen oder ihr Passwort ändern. Diese Daten werden für
        Rechnungen, sowie für den Versand von Ladekarten verwendet.
      </div>

      <div className={"flex flex-col gap-4 md:flex-row"}>
        <div className={"w-full md:max-w-70/100"}>
          <CardHolderProfileForm user={user} />
        </div>
        <div className={"w-full md:max-w-70/100"}>
          <PasswordForm />
        </div>
      </div>
    </Card>
  );
};

export default ProfilePage;
