"use client";

import { useState, useEffect } from "react";
import { MaintenanceTargetType } from "@prisma/client";
import MaintenanceForm from "./MaintenanceForm";
import MaintenanceTable from "./MaintenanceTable";
import CategoryManager from "./CategoryManager";
import Button from "~/component/button";
import { AiOutlinePlusCircle, AiOutlineSettings } from "react-icons/ai";

interface MaintenanceCategory {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  _count: {
    maintenanceRecords: number;
  };
}

interface MaintenanceRecord {
  id: string;
  date: string;
  description: string;
  notes?: string;
  targetType: MaintenanceTargetType;
  locationId?: string;
  evseId?: string;
  connectorId?: string;
  nextDueDate?: string;
  category: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    name: string;
    lastName: string;
  };
  location?: {
    id: string;
    name: string;
  };
  evse?: {
    uid: string;
    evse_id: string;
  };
  ou: {
    id: string;
    name: string;
  };
}

interface Location {
  id: string;
  name: string;
  city: string;
  street: string;
  evses: {
    uid: string;
    evse_id: string;
    status: string;
  }[];
}

const MaintenanceManager = () => {
  const [showForm, setShowForm] = useState(false);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [editingRecord, setEditingRecord] = useState<MaintenanceRecord | null>(null);
  const [categories, setCategories] = useState<MaintenanceCategory[]>([]);
  const [records, setRecords] = useState<MaintenanceRecord[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/maintenance/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  // Fetch records
  const fetchRecords = async () => {
    try {
      const response = await fetch("/api/maintenance/records");
      if (response.ok) {
        const data = await response.json();
        setRecords(data.records);
      }
    } catch (error) {
      console.error("Error fetching records:", error);
    }
  };

  // Fetch locations
  const fetchLocations = async () => {
    try {
      const response = await fetch("/api/maintenance/locations");
      if (response.ok) {
        const data = await response.json();
        setLocations(data);
      }
    } catch (error) {
      console.error("Error fetching locations:", error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await Promise.all([
        fetchCategories(),
        fetchRecords(),
        fetchLocations()
      ]);
      setLoading(false);
    };

    fetchData();
  }, [refreshTrigger]);

  const handleFormSubmit = () => {
    setShowForm(false);
    setEditingRecord(null);
    setRefreshTrigger(prev => prev + 1);
  };

  const handleEdit = (record: MaintenanceRecord) => {
    setEditingRecord(record);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Sind Sie sicher, dass Sie diesen Wartungseintrag löschen möchten?")) {
      return;
    }

    try {
      const response = await fetch(`/api/maintenance/records/${id}`, {
        method: "DELETE"
      });

      if (response.ok) {
        setRefreshTrigger(prev => prev + 1);
      } else {
        alert("Fehler beim Löschen des Wartungseintrags");
      }
    } catch (error) {
      console.error("Error deleting record:", error);
      alert("Fehler beim Löschen des Wartungseintrags");
    }
  };

  const handleCategoryUpdate = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  if (loading) {
    return <div>Laden...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header with action buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-xl font-bold text-primary">Wartung & Reparaturen</h2>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            type="button"
            onClick={() => setShowCategoryManager(true)}
            className="flex items-center gap-2"
          >
            <AiOutlineSettings size={16} />
            Kategorien verwalten
          </Button>
          <Button
            type="button"
            onClick={() => setShowForm(true)}
            className="flex items-center gap-2"
          >
            <AiOutlinePlusCircle size={16} />
            Wartung eintragen
          </Button>
        </div>
      </div>

      {/* Forms and modals */}
      {showForm && (
        <MaintenanceForm
          categories={categories}
          locations={locations}
          editingRecord={editingRecord}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingRecord(null);
          }}
        />
      )}

      {showCategoryManager && (
        <CategoryManager
          categories={categories}
          onUpdate={handleCategoryUpdate}
          onClose={() => setShowCategoryManager(false)}
        />
      )}

      {/* Records table */}
      <MaintenanceTable
        records={records}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default MaintenanceManager;
