"use client";

import { useState, useEffect } from "react";
import { MaintenanceTargetType } from "@prisma/client";
import Button from "~/component/button";
import { AiOutlineSave, AiOutlineCheck } from "react-icons/ai";

interface MaintenanceCategory {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
}

interface Location {
  id: string;
  name: string;
  city: string;
  street: string;
  evses: {
    uid: string;
    evse_id: string;
    status: string;
  }[];
}

interface Props {
  categories: MaintenanceCategory[];
  locations: Location[];
  onSubmit: () => void;
}

const QuickMaintenanceForm = ({ categories, locations, onSubmit }: Props) => {
  const [formData, setFormData] = useState({
    date: "",
    description: "",
    notes: "",
    targetType: MaintenanceTargetType.LOCATION,
    locationId: "",
    evseId: "",
    categoryId: ""
  });
  const [loading, setLoading] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [showAllEvses, setShowAllEvses] = useState(false);

  useEffect(() => {
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    setFormData(prev => ({ ...prev, date: today }));
  }, []);

  const handleLocationChange = (locationId: string) => {
    const location = locations.find(l => l.id === locationId);
    setSelectedLocation(location || null);
    setFormData(prev => ({
      ...prev,
      locationId,
      evseId: "",
      targetType: MaintenanceTargetType.LOCATION
    }));
    setShowAllEvses(false);
  };

  const handleAllEvsesToggle = () => {
    if (showAllEvses) {
      setFormData(prev => ({
        ...prev,
        targetType: MaintenanceTargetType.LOCATION,
        evseId: ""
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        targetType: MaintenanceTargetType.ALL_EVSES,
        evseId: ""
      }));
    }
    setShowAllEvses(!showAllEvses);
  };

  const handleEvseSelect = (evseUid: string) => {
    setFormData(prev => ({
      ...prev,
      evseId: evseUid,
      targetType: MaintenanceTargetType.EVSE
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/maintenance/records", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        // Reset form
        setFormData({
          date: new Date().toISOString().split('T')[0],
          description: "",
          notes: "",
          targetType: MaintenanceTargetType.LOCATION,
          locationId: "",
          evseId: "",
          categoryId: ""
        });
        setSelectedLocation(null);
        setShowAllEvses(false);
        onSubmit();
      } else {
        const error = await response.json();
        alert(`Fehler: ${error.error}`);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Fehler beim Speichern");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <h3 className="text-lg font-bold text-primary mb-4">Schnelle Wartungseintragung</h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Date and Category Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Datum
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none text-sm"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Kategorie
            </label>
            <select
              value={formData.categoryId}
              onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none text-sm"
              required
            >
              <option value="">Auswählen</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Location Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Standort
          </label>
          <select
            value={formData.locationId}
            onChange={(e) => handleLocationChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none text-sm"
            required
          >
            <option value="">Standort auswählen</option>
            {locations.map(location => (
              <option key={location.id} value={location.id}>
                {location.name} - {location.city}
              </option>
            ))}
          </select>
        </div>

        {/* All EVSEs Toggle - only show when location is selected */}
        {selectedLocation && (
          <div className="flex items-center">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={showAllEvses}
                onChange={handleAllEvsesToggle}
                className="mr-2"
              />
              <span className="text-sm font-medium text-gray-700">
                Alle Ladesäulen des Standorts
              </span>
            </label>
          </div>
        )}

        {/* EVSE Selection */}
        {selectedLocation && selectedLocation.evses.length > 0 && !showAllEvses && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ladesäule (optional - leer lassen für gesamten Standort)
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 max-h-40 overflow-y-auto">
              {selectedLocation.evses.map(evse => (
                <button
                  key={evse.uid}
                  type="button"
                  onClick={() => handleEvseSelect(evse.uid)}
                  className={`p-2 text-left text-xs border rounded-md transition-colors ${
                    formData.evseId === evse.uid
                      ? "bg-primary text-white border-primary"
                      : "bg-gray-50 hover:bg-gray-100 border-gray-300"
                  }`}
                >
                  <div className="font-medium">{evse.evse_id}</div>
                  <div className="text-xs opacity-75">{evse.status}</div>
                </button>
              ))}
            </div>
            {formData.evseId && (
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, evseId: "", targetType: MaintenanceTargetType.LOCATION }))}
                className="mt-2 text-xs text-gray-500 hover:text-gray-700"
              >
                Auswahl aufheben
              </button>
            )}
          </div>
        )}

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Was wurde gemacht? *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none text-sm"
            rows={2}
            required
            placeholder="Kurze Beschreibung der durchgeführten Arbeiten..."
          />
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notizen (optional)
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none text-sm"
            rows={2}
            placeholder="Zusätzliche Informationen..."
          />
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={loading}
          className="w-full flex items-center justify-center gap-2"
        >
          {loading ? (
            <>Speichern...</>
          ) : (
            <>
              <AiOutlineSave size={16} />
              Wartung eintragen
            </>
          )}
        </Button>
      </form>
    </div>
  );
};

export default QuickMaintenanceForm;
