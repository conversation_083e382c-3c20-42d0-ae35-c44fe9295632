"use client";

import { useState } from "react";
import Button from "~/component/button";
import { AiOutlineClose, AiOutlinePlusCircle, AiOutlineEdit, AiOutlineDelete } from "react-icons/ai";

interface MaintenanceCategory {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  _count: {
    maintenanceRecords: number;
  };
}

interface Props {
  categories: MaintenanceCategory[];
  onUpdate: () => void;
  onClose: () => void;
}

const CategoryManager = ({ categories, onUpdate, onClose }: Props) => {
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<MaintenanceCategory | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: ""
  });
  const [loading, setLoading] = useState(false);

  const handleEdit = (category: MaintenanceCategory) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || ""
    });
    setShowForm(true);
  };

  const handleAdd = () => {
    setEditingCategory(null);
    setFormData({
      name: "",
      description: ""
    });
    setShowForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const url = editingCategory 
        ? `/api/maintenance/categories/${editingCategory.id}`
        : "/api/maintenance/categories";
      
      const method = editingCategory ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        setShowForm(false);
        setEditingCategory(null);
        setFormData({ name: "", description: "" });
        onUpdate();
      } else {
        const error = await response.json();
        alert(`Fehler: ${error.error}`);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Fehler beim Speichern");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (category: MaintenanceCategory) => {
    if (category.isDefault) {
      alert("Standard-Kategorien können nicht gelöscht werden.");
      return;
    }

    if (category._count.maintenanceRecords > 0) {
      alert("Kategorien mit vorhandenen Wartungseinträgen können nicht gelöscht werden.");
      return;
    }

    if (!confirm(`Sind Sie sicher, dass Sie die Kategorie "${category.name}" löschen möchten?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/maintenance/categories/${category.id}`, {
        method: "DELETE"
      });

      if (response.ok) {
        onUpdate();
      } else {
        const error = await response.json();
        alert(`Fehler: ${error.error}`);
      }
    } catch (error) {
      console.error("Error deleting category:", error);
      alert("Fehler beim Löschen");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-bold text-primary">Kategorien verwalten</h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <AiOutlineClose size={24} />
            </button>
          </div>

          {!showForm ? (
            <>
              <div className="flex justify-end mb-4">
                <Button
                  type="button"
                  onClick={handleAdd}
                  className="flex items-center gap-2"
                >
                  <AiOutlinePlusCircle size={16} />
                  Neue Kategorie
                </Button>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Beschreibung
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Typ
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Einträge
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Aktionen
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categories.map((category) => (
                      <tr key={category.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {category.name}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {category.description || "-"}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          {category.isDefault ? (
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                              Standard
                            </span>
                          ) : (
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                              Benutzerdefiniert
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {category._count.maintenanceRecords}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => handleEdit(category)}
                              className="text-blue-600 hover:text-blue-900"
                              title="Bearbeiten"
                            >
                              <AiOutlineEdit size={16} />
                            </button>
                            {!category.isDefault && category._count.maintenanceRecords === 0 && (
                              <button
                                onClick={() => handleDelete(category)}
                                className="text-red-600 hover:text-red-900"
                                title="Löschen"
                              >
                                <AiOutlineDelete size={16} />
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <h4 className="text-md font-medium text-gray-900">
                {editingCategory ? "Kategorie bearbeiten" : "Neue Kategorie erstellen"}
              </h4>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none"
                  required
                  placeholder="z.B. Sonstiges"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Beschreibung
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none"
                  rows={3}
                  placeholder="Optionale Beschreibung der Kategorie"
                />
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading ? "Speichern..." : "Speichern"}
                </Button>
                <Button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="bg-gray-500 hover:bg-gray-600"
                >
                  Abbrechen
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryManager;
