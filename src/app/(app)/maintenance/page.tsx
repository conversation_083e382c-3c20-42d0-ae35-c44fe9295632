import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Card from "~/component/card";
import MaintenanceManager from "./components/MaintenanceManager";

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.role || session.user.role !== Role.ADMIN) {
    return <NotFound />;
  }

  return <MaintenanceManager />;
};

export default Page;
