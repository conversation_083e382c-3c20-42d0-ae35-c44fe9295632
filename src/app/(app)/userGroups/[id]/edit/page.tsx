import Card from "../../../../../component/card";
import UserGroupForm from "../../components/UserGroupForm";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect, notFound } from "next/navigation";
import prisma from "~/server/db/prisma";

interface Props {
  params: {
    id: string;
  };
}

const getUserGroup = async (id: string) => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return null;
  }

  const userGroup = await prisma.userGroup.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      description: true,
      ouId: true,
      ou: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  // Check permissions
  if (userGroup && session.user.role !== Role.ADMIN && userGroup.ouId !== session.user.selectedOu.id) {
    return null;
  }

  return userGroup;
};

const getAvailableOUs = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session) return [];

  if (session.user.role === Role.ADMIN) {
    return await prisma.ou.findMany({
      where: {
        deleted: null,
        hide: false,
      },
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: "asc",
      },
    });
  } else {
    // CARD_MANAGER can only edit user groups in their own OU
    return [{
      id: session.user.selectedOu.id,
      name: session.user.selectedOu.name,
    }];
  }
};

const EditUserGroupPage = async ({ params }: Props) => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    redirect("/");
  }

  const [userGroup, availableOUs] = await Promise.all([
    getUserGroup(params.id),
    getAvailableOUs(),
  ]);

  if (!userGroup) {
    notFound();
  }

  const initialData = {
    id: userGroup.id,
    name: userGroup.name,
    description: userGroup.description || "",
    ouId: userGroup.ouId,
  };

  return (
    <Card header_left={`Nutzergruppe bearbeiten: ${userGroup.name}`}>
      <UserGroupForm availableOUs={availableOUs} initialData={initialData} />
    </Card>
  );
};

export default EditUserGroupPage;
