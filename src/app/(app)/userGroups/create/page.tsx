import Card from "../../../../component/card";
import UserGroupForm from "../components/UserGroupForm";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect } from "next/navigation";
import prisma from "~/server/db/prisma";

const getAvailableOUs = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session) return [];

  if (session.user.role === Role.ADMIN) {
    return await prisma.ou.findMany({
      where: {
        deleted: null,
        hide: false,
      },
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: "asc",
      },
    });
  } else {
    // CARD_MANAGER can only create user groups in their own OU
    return [{
      id: session.user.selectedOu.id,
      name: session.user.selectedOu.name,
    }];
  }
};

const CreateUserGroupPage = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    redirect("/");
  }

  const availableOUs = await getAvailableOUs();

  return (
    <Card header_left={"Neue Nutzergruppe erstellen"}>
      <UserGroupForm availableOUs={availableOUs} />
    </Card>
  );
};

export default CreateUserGroupPage;
