"use client";

import { useForm } from "react-hook-form";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";

interface FormInputs {
  name: string;
  description?: string;
  ouId: string;
}

interface OU {
  id: string;
  name: string;
}

interface Props {
  availableOUs: OU[];
  initialData?: {
    id?: string;
    name: string;
    description?: string;
    ouId: string;
  };
}

const UserGroupForm = ({ availableOUs, initialData }: Props) => {
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormInputs>({
    defaultValues: initialData || {
      ouId: availableOUs.length === 1 ? availableOUs[0].id : "",
    },
  });

  const onSubmit = async (data: FormInputs) => {
    try {
      const url = initialData?.id 
        ? `/api/userGroup/${initialData.id}` 
        : "/api/userGroup";
      
      const method = initialData?.id ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setErrorMessage(errorData.error || "Fehler beim Speichern");
        setSuccessMessage("");
        return;
      }

      setErrorMessage("");
      setSuccessMessage(
        initialData?.id 
          ? "Nutzergruppe erfolgreich aktualisiert" 
          : "Nutzergruppe erfolgreich erstellt"
      );
      
      if (!initialData?.id) {
        reset();
      }

      // Redirect after successful creation/update
      setTimeout(() => {
        router.push("/userGroups");
      }, 1500);
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Unbekannter Fehler beim Speichern");
      }
      setSuccessMessage("");
    }
  };

  return (
    <div className="max-w-2xl">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* OU Selection */}
        <div>
          <label htmlFor="ouId" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Organisationseinheit *
          </label>
          <select
            id="ouId"
            {...register("ouId", { required: "OU ist erforderlich" })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            disabled={availableOUs.length === 1}
          >
            <option value="">Bitte wählen...</option>
            {availableOUs.map((ou) => (
              <option key={ou.id} value={ou.id}>
                {ou.name}
              </option>
            ))}
          </select>
          {errors.ouId && (
            <p className="mt-1 text-sm text-red-600">{errors.ouId.message}</p>
          )}
        </div>

        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Name *
          </label>
          <input
            type="text"
            id="name"
            {...register("name", { 
              required: "Name ist erforderlich",
              minLength: { value: 1, message: "Name muss mindestens 1 Zeichen lang sein" }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            placeholder="z.B. Mieter, Angestellte, Gäste"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Beschreibung
          </label>
          <textarea
            id="description"
            rows={3}
            {...register("description")}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            placeholder="Optionale Beschreibung der Nutzergruppe..."
          />
        </div>

        {/* Messages */}
        {successMessage && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="text-sm text-green-700">{successMessage}</div>
          </div>
        )}

        {errorMessage && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{errorMessage}</div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push("/userGroups")}
          >
            Abbrechen
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center"
          >
            {isSubmitting && <FiLoader className="mr-2 animate-spin" />}
            {initialData?.id ? "Aktualisieren" : "Erstellen"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default UserGroupForm;
