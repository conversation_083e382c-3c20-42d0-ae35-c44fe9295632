"use client";

import { useState, useEffect } from "react";
import Card from "../../../../component/card";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { CompanyTarif } from "@prisma/client";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";

interface UserGroupTarifsData {
  assigned: CompanyTarif[];
  available: CompanyTarif[];
}

interface Props {
  userGroupId: string;
  userGroupName?: string;
}

const UserGroupTarifs = ({ userGroupId, userGroupName }: Props) => {
  const [data, setData] = useState<UserGroupTarifsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [selectedTarifs, setSelectedTarifs] = useState<string[]>([]);

  useEffect(() => {
    fetchTarifs();
  }, [userGroupId]);

  const fetchTarifs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`);

      if (!response.ok) {
        throw new Error("Fehler beim Laden der Tarife");
      }

      const tarifsData = await response.json();
      setData(tarifsData);
      setSelectedTarifs(tarifsData.assigned.map((t: CompanyTarif) => t.id));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setLoading(false);
    }
  };

  const handleTarifToggle = (tarifId: string) => {
    if (!data) return;

    const tarif = data.available.find((t) => t.id === tarifId);
    if (!tarif) return;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isValidTarif = new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today;

    let newSelection: string[];

    if (selectedTarifs.includes(tarifId)) {
      // Deselecting - always allowed
      newSelection = selectedTarifs.filter((id) => id !== tarifId);
      setError(""); // Clear any errors when deselecting
    } else {
      // Selecting - allow multiple AC and DC tarifs
      newSelection = [...selectedTarifs, tarifId];
      setError(""); // Clear any previous errors
    }

    setSelectedTarifs(newSelection);
  };

  // No validation needed - user groups can have multiple AC and DC tarifs

  const handleSave = async () => {
    try {
      setSaving(true);
      setError("");
      setSuccessMessage("");

      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ tarifIds: selectedTarifs }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Fehler beim Speichern");
      }

      const result = await response.json();
      setSuccessMessage(result.message || "Tarif-Zuordnungen erfolgreich aktualisiert");

      // Refresh data
      await fetchTarifs();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <div className="flex justify-center py-8">
          <FiLoader className="animate-spin text-2xl" />
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <p className="text-red-600">Fehler beim Laden der Tarife</p>
      </Card>
    );
  }

  const hasChanges =
    JSON.stringify(selectedTarifs.sort()) !== JSON.stringify(data.assigned.map((t) => t.id).sort());

  return (
    <Card
      header_left={`Tarifzuordnungen für Nutzergruppe: "${
        userGroupName ? `${userGroupName}` : ""
      }"`}
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Wählen Sie die Tarife aus der gleichen OU aus, die für diese Nutzergruppe verfügbar
              sein sollen.
            </p>
            <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
              💡 Benutzer dieser Nutzergruppe sehen bei der Ladekarten-Registrierung nur die hier zugeordneten Tarife.
            </p>
            <p className="mt-1 text-xs text-green-600 dark:text-green-400">
              ✅ Sie können mehrere{" "}
              <span className="rounded bg-primary px-1 text-white">AC</span>- und{" "}
              <span className="rounded bg-primary px-1 text-white">DC</span>-Tarife zuordnen.
            </p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              🚫 Interne Tarife können nicht zu Nutzergruppen zugeordnet werden.
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            className="flex items-center"
          >
            {saving && <FiLoader className="mr-2 animate-spin" />}
            Speichern
          </Button>
        </div>

        {successMessage && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="text-sm text-green-700">{successMessage}</div>
          </div>
        )}

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        <div className="space-y-6">
          {data.available.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">Keine Tarife für diese OU verfügbar.</p>
          ) : (
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
              {/* Zugeordnete Tarife - Links */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">
                    Zugeordnete Tarife
                  </span>
                  <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-primary">
                    {selectedTarifs.length}
                  </span>
                </div>

                <div className="space-y-3">
                  {selectedTarifs.length === 0 ? (
                    <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center dark:border-gray-600">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Keine Tarife zugeordnet
                      </p>
                      <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                        Wählen Sie Tarife aus der rechten Spalte aus
                      </p>
                    </div>
                  ) : (
                    data.available
                      .filter((tarif) => selectedTarifs.includes(tarif.id))
                      .map((tarif) => (
                        <CompactTarifCard
                          key={tarif.id}
                          tarif={tarif}
                          isSelected={true}
                          onToggle={handleTarifToggle}
                          side="assigned"
                        />
                      ))
                  )}
                </div>
              </div>

              {/* Verfügbare Tarife - Rechts */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">
                    Verfügbare Tarife
                  </span>
                  <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                    {data.available.filter((tarif) => !selectedTarifs.includes(tarif.id)).length}
                  </span>
                </div>

                <div className="space-y-3">
                  {data.available
                    .filter((tarif) => !selectedTarifs.includes(tarif.id))
                    .map((tarif) => (
                      <CompactTarifCard
                        key={tarif.id}
                        tarif={tarif}
                        isSelected={false}
                        onToggle={handleTarifToggle}
                        side="available"
                      />
                    ))}

                  {data.available.filter((tarif) => !selectedTarifs.includes(tarif.id)).length === 0 && (
                    <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center dark:border-gray-600">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Alle verfügbaren Tarife sind bereits zugeordnet
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

// Kompakte Tarifkarte für die Zuordnungsansicht
const CompactTarifCard = ({
  tarif,
  isSelected,
  onToggle,
  side
}: {
  tarif: CompanyTarif;
  isSelected: boolean;
  onToggle: (id: string) => void;
  side: 'assigned' | 'available';
}) => {
  const [expanded, setExpanded] = useState(false);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const isValid = new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 4,
    }).format(price);
  };

  return (
    <div
      className={`rounded-lg border transition-all ${
        isSelected
          ? 'border-primary bg-primary/5 shadow-sm'
          : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
      }`}
    >
      {/* Kompakte Ansicht */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3 flex-1">
          {/* Tarif Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h4 className="font-medium text-gray-900 dark:text-white truncate">
                {tarif.name}
              </h4>

              {/* AC/DC Badge */}
              {tarif.currentType && (
                <span className="inline-flex px-2 py-1 text-xs font-bold rounded bg-primary text-white">
                  {tarif.currentType}
                </span>
              )}

              {/* Validity Badge */}
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                isValid
                  ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
              }`}>
                {isValid ? 'Gültig' : 'Ungültig'}
              </span>
            </div>

            <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {formatPrice(tarif.energyPrice)}/kWh
            </div>
          </div>
        </div>

        {/* Action Buttons - rechts positioniert */}
        <div className="flex items-center space-x-2 ml-4">
          {/* Expand Button */}
          <button
            onClick={() => setExpanded(!expanded)}
            className="flex h-6 w-6 items-center justify-center rounded text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
            title={expanded ? 'Details ausblenden' : 'Details anzeigen'}
          >
            <svg
              className={`h-3 w-3 transition-transform ${expanded ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2.5}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {/* Action Button */}
          <button
            onClick={() => onToggle(tarif.id)}
            className={`flex h-6 w-6 items-center justify-center rounded transition-colors ${
              side === 'assigned'
                ? 'text-red-400 hover:text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20'
                : 'text-green-500 hover:text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20'
            }`}
            title={side === 'assigned' ? 'Tarif entfernen' : 'Tarif hinzufügen'}
          >
            {side === 'assigned' ? (
              <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            ) : (
              <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Erweiterte Ansicht */}
      {expanded && (
        <div className="border-t border-gray-200 p-4 dark:border-gray-700">
          <div className="space-y-3">
            {tarif.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {tarif.description}
              </p>
            )}

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Energiepreis:</span>
                <span className="ml-1 font-medium text-gray-900 dark:text-white">
                  {formatPrice(tarif.energyPrice)}/kWh
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Sitzungsgebühr:</span>
                <span className="ml-1 font-medium text-gray-900 dark:text-white">
                  {formatPrice(tarif.sessionPrice)}
                </span>
              </div>
              {tarif.basicFee > 0 && (
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Grundgebühr:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {formatPrice(tarif.basicFee)}/Monat
                  </span>
                </div>
              )}
              {tarif.oneTimeFee > 0 && (
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Einmalgebühr:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {formatPrice(tarif.oneTimeFee)}
                  </span>
                </div>
              )}
            </div>

            <div className="text-xs text-gray-500 dark:text-gray-400">
              Gültig: {new Date(tarif.validFrom).toLocaleDateString('de-DE')} - {new Date(tarif.validTo).toLocaleDateString('de-DE')}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserGroupTarifs;
