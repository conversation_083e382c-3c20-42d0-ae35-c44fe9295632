import Link from "next/link";
import { IoIosAdd } from "react-icons/io";
import Card from "../../../component/card";
import UserGroupTable from "./components/UserGroupTable";
import prisma from "../../../server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect } from "next/navigation";

export const revalidate = 0;

const getUserGroups = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return [];
  }

  const whereClause = session.user.role === Role.ADMIN 
    ? {} 
    : { ouId: session.user.selectedOu.id };

  return await prisma.userGroup.findMany({
    where: whereClause,
    include: {
      ou: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          users: true,
          physicalCards: true,
          companyTarifs: true,
        },
      },
    },
    orderBy: {
      name: "asc",
    },
  });
};

const UserGroupsPage = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    redirect("/");
  }

  const userGroups = await getUserGroups();

  return (
    <div className="space-y-6">
      {/* Explanation Section */}
      <Card header_left={"Was sind Nutzergruppen?"}>
        <div className="space-y-4">
          <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Zweck von Nutzergruppen
                </h3>
                <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                  <p>
                    Nutzergruppen sind dafür gedacht, Benutzern bestimmte Tarife zugänglich zu machen.
                    Benutzer werden Nutzergruppen zugeordnet, zum Beispiel <strong>Mitarbeitern</strong>, <strong>Mietern</strong> oder <strong>ansässigen Geschäftspartnern</strong>.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Funktionsweise
                </h3>
                <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                  <p>
                    Bei der Registrierung und Bestellung einer Ladekarte erhält der Nutzer dann nur die Tarife angezeigt,
                    die seiner Nutzergruppe zugeordnet sind. Dies ermöglicht eine gezielte Tarifverwaltung für verschiedene Benutzertypen.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card header_left={"Nutzergruppen"}>
        <div className={"mb-10 flex flex-row-reverse"}>
          <Link href={"/userGroups/create"}>
            <button className={"btn text-right"}>
              <IoIosAdd size={20} />
              Neue Nutzergruppe
            </button>
          </Link>
        </div>
        <UserGroupTable data={userGroups} />
      </Card>
    </div>
  );
};

export default UserGroupsPage;
