import React from "react";
import Link from "next/link";

interface Props {
  href: string;
  disabled?: boolean;
  children: React.ReactNode;
  className?: string; // Optionaler zusätzlicher Klassenname
}

const StyledLink: React.FC<Props> = ({ disabled = false, href, children, className }) => {
  const combinedClassName = `block hover:text-white sm:min-w-72 rounded-lg bg-primary px-4 py-2.7 text-center text-xl font-bold text-white hover:brightness-90 ${
    className || ""
  }`;

  if (disabled) {
    return (
      <span className={`${combinedClassName} brightness-90 hover:cursor-not-allowed`}>
        {" "}
        {children}{" "}
      </span>
    );
  } else {
    return (
      <Link className={combinedClassName} href={href} passHref>
        {children}
      </Link>
    );
  }
};

export default StyledLink;
