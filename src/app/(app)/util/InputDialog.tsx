import React, { useEffect, useRef, useState } from "react";
import Button from "~/component/button";
import Dropdown from "~/app/(app)/util/Dropdown";

interface InputDialogProps {
  message: string;
  title: string;
  onYes: (value: string, overwrite?: boolean) => void;
  onNo: () => void;
  yesLabel?: string;
  noLabel?: string;
  overwriteLabel?: string;
  defaultValue?: string;
  asSelect?: boolean;
}

const InputDialog: React.FC<InputDialogProps> = ({
  message,
  title,
  onYes,
  onNo,
  yesLabel = "Ja",
  noLabel = "Nein",
  overwriteLabel,
  asSelect = false,
}) => {
  const [value, setValue] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const [overwrite, setOverwrite] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  useEffect(() => {
    inputRef?.current?.focus();
  }, []);

  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-opacity-50 backdrop-blur-sm ">
      <div className="w-2/3 rounded-xl bg-white p-4 text-lg shadow-soft-3xl md:w-1/2 lg:w-1/3">
        {title && <h2 className="mb-2 text-xl font-bold">{title}</h2>}
        <p className={"text-black"}>{message}</p>
        <div className={"mb-2 flex flex-row items-center gap-2"}>
          <input
            ref={inputRef}
            className="mt-1 block w-full  rounded-md border border-gray-300 px-2 py-2 text-xs font-semibold focus:outline-gray-400 sm:text-sm  sm:text-sm"
            type="text"
            placeholder={"Name der Ansicht"}
            value={value}
            onChange={(event) => setValue(event.target.value)}
          />

          {overwriteLabel && (
            <>
              <label
                className="mr-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor={"overwriteId"}
              >
                {overwriteLabel}
              </label>
              <div className="flex rounded-lg">
                <input
                  onChange={(event) => {
                    setOverwrite(event.target.checked);
                  }}
                  id={"overwriteId"}
                  type="checkbox"
                  className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid
                border-gray-200 bg-eul-gray bg-none bg-contain bg-left bg-no-repeat align-top font-bold
                transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4
                after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250
                after:content-[''] checked:border-gray-600 checked:bg-primary checked:bg-none
                checked:bg-right checked:after:translate-x-5.3"
                />
              </div>
            </>
          )}
        </div>

        <div className="flex shrink-0  items-center justify-end justify-center gap-1 rounded-b-xl border-t border-solid border-slate-100 p-3">
          <Button className={"w-1/3 text-xs sm:text-sm"} onClick={() => onYes(value, overwrite)}>
            {yesLabel}
          </Button>

          {noLabel && (
            <Button className={"w-1/3 text-xs sm:text-sm"} onClick={onNo}>
              {noLabel}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default InputDialog;
