"use client";
import React, { useEffect, useRef, useState } from "react";
import Button from "~/component/button";
import { FaEye } from "react-icons/fa";
import { RxCross1 } from "react-icons/rx";
import { ouColors } from "~/styles/oucolors/oucolors";

// Definieren Sie einen Typ für eine einzelne Option
export interface Option {
  id: string;
  label: string;
  selected: boolean;
  color?: string;
}

// Definieren Sie einen Typ für die Props der Dropdown-Komponente
interface DropdownProps {
  title?: string;
  options: Option[] | undefined;
  onChange: (id: string) => void;
  onDelete: (id: string) => void;
  canDelete?: boolean;
  icon?: React.ReactNode;
  className?: string;
  placeHolder?: string;
}

const Dropdown = ({
  title,
  options,
  onChange,
  onDelete,
  canDelete = true,
  icon,
  className,
  placeHolder = "--------",
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLUListElement>(null);
  const ref = useRef<HTMLDivElement>(null);

  // will register events in order to close the opened
  // value list if a click is done somewhere else or ESC key
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!ref?.current?.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    const handleEscape = (event: KeyboardEvent) => {
      // on ESC close list
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    // Event-Listener hinzufügen, wenn das Komponenten gemountet wird
    document.addEventListener("mouseup", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    // Event-Listener entfernen, wenn das Komponenten unmountet wird
    return () => {
      document.removeEventListener("mouseup", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen]);

  const handleOptionClick = (id: string) => {
    setIsOpen(false); // schließt das Dropdown-Menü
    onChange(id); // ruft die übergebene onChange-Funktion auf
  };

  const handleDelete = (id: string) => {
    setIsOpen(false);
    onDelete(id);
  };

  return (
    <div className={`relative ${className ?? ""}`} ref={ref}>
      <Button
        title={title ?? "Auswahl"}
        onClick={() => {
          setIsOpen(!isOpen);
        }}
        aria-expanded={isOpen}
        type="button"
        className={"w-full items-center truncate"}
      >
        {icon ?? <FaEye size={18} className={"mr-1"} />}
        {options?.find((option) => option.selected)?.label || <span>{placeHolder}</span>}
        {isOpen ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="ml-2 h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 15l7-7 7 7" // Pfeil nach oben
            />{" "}
          </svg>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="ml-2 h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </Button>
      <ul
        ref={dropdownRef}
        className={`before:font-awesome pointer-events-none absolute left-auto top-0 z-10 m-0 -mr-4 mt-2 max-h-50-screen  min-w-44 list-none overflow-y-auto overscroll-y-auto rounded-lg border-0 border-solid border-transparent bg-white bg-clip-padding px-0 py-2 text-left text-sm text-slate-500 opacity-0 transition-all duration-250 transform-dropdown-show before:absolute before:left-auto before:right-7 before:top-0 before:z-40 before:text-5.5 before:text-white before:transition-all before:duration-350 before:ease-soft before:content-['\f0d8'] lg:shadow-soft-3xl ${
          isOpen ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"
        }`}
      >
        {options?.map((option) => (
          <li
            key={option.id}
            className={`${
              option.selected ? "bg-blue-50" : "bg-transparent"
            } flew-row flex  items-center hover:bg-gray-200 `}
          >
            {option.color && (
              <span
                className={"h-[2rem] w-2"}
                style={{
                  backgroundColor: option.color,
                }}
              ></span>
            )}

            <button
              className={`clear-both block w-full whitespace-nowrap border-0  px-4 py-1.2 text-left font-normal text-slate-500 hover:bg-gray-200 hover:text-slate-700 dark:text-white dark:hover:bg-gray-200/80 dark:hover:text-slate-700 lg:transition-colors lg:duration-300 lg:ease-soft`}
              onClick={() => handleOptionClick(option.id)}
            >
              {option.label}
            </button>
            {canDelete && (
              <RxCross1
                className={`mr-2 transition-colors duration-75 hover:scale-150 hover:cursor-pointer hover:text-red-500`}
                onClick={() => handleDelete(option.id)}
              />
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Dropdown;
