"use client";
import Image from "next/image";
import React from "react";

const SettingsSVG: React.FC = () => {
  return (
    <svg
      width="191"
      height="189"
      viewBox="0 0 191 189"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1_136)">
        <path
          d="M21.8965 29.5215C20.4784 27.2549 19.0602 24.7051 17.3584 22.4386C15.9402 19.8887 15.9402 19.0388 18.2093 17.3389C22.1802 14.2224 26.4347 11.1059 30.6892 7.98946C32.6747 6.57288 33.8092 6.57288 35.511 8.55609C36.9292 10.256 38.631 11.9559 40.3328 13.9391C41.1837 15.0724 42.0346 15.0724 43.4528 14.789C46.8564 13.6558 50.2601 12.8058 53.6637 12.2392C55.0819 11.9559 55.9328 11.3893 56.2164 9.97267C56.5 7.42283 57.3509 4.87299 57.6346 2.32314C57.9182 0.62325 59.0528 -0.226698 60.7546 0.0566182C66.4273 0.62325 72.1 1.4732 77.7727 2.32314C79.4745 2.60646 80.3254 3.45641 80.0418 5.1563C80.0418 7.98946 79.7581 10.8226 79.7581 13.3725C79.7581 14.5057 80.0418 15.0724 81.1763 15.639C84.5799 17.6222 87.9835 19.3221 91.1035 21.5886C91.9544 22.1553 92.5217 22.1553 93.3726 21.5886C95.6417 20.172 97.6272 18.7555 99.8962 17.3389C101.882 16.2056 103.016 16.2056 104.434 18.1888C107.838 22.4386 110.958 26.6883 114.362 30.938C115.496 32.3546 115.496 33.7712 114.078 35.1878C112.093 36.8877 110.107 38.8709 108.122 40.5708C106.987 41.4207 106.987 42.2707 107.554 43.4039C108.689 46.8037 109.54 50.4868 110.391 53.8866C110.674 55.3032 111.242 55.5865 112.376 55.8698C114.929 56.4365 117.482 57.0031 120.034 57.5697C121.736 57.8531 122.587 58.9863 122.303 60.9695C121.736 66.3525 120.885 71.7355 120.034 77.1185C119.751 79.1017 118.616 79.9517 116.631 79.9517C114.078 79.6684 111.525 79.6684 108.689 79.6684C107.554 79.6684 106.987 79.9517 106.42 81.085C104.718 84.4847 102.733 87.8845 100.747 91.001C100.18 92.1343 100.18 92.7009 100.747 93.8342C102.165 95.8174 103.583 98.0839 105.002 100.35C106.136 102.334 105.853 103.467 104.151 104.883C99.8962 108 95.9253 111.4 91.6708 114.516C89.6854 115.933 88.5508 115.933 86.849 113.95C85.1472 111.966 83.4454 110.266 81.7436 108.283C80.8927 107.433 80.3254 107.433 78.9072 107.717C75.22 108.85 71.8163 109.7 68.1291 110.55C66.9945 110.833 66.4273 111.116 66.1436 112.533C65.5764 115.083 65.0091 117.633 64.4418 120.183C63.8745 122.449 63.0236 123.016 60.4709 122.732C55.3655 122.166 50.2601 121.316 45.1546 120.749C42.3183 120.466 41.751 119.616 41.751 116.499C42.6019 113.1 42.6019 110.833 42.6019 108.283C42.6019 107.717 42.0346 106.867 41.4674 106.583C38.0637 104.6 34.6601 102.617 31.2565 100.634C30.6892 100.35 29.5547 100.35 28.9874 100.634C26.7183 102.05 24.4492 103.467 22.4638 104.883C20.4784 106.3 19.3438 106.017 17.9256 104.034C14.8056 99.7838 11.402 95.8174 8.28203 91.5676C6.86386 89.5844 6.86386 88.4512 8.8493 86.7513C10.8348 85.0514 12.8202 83.3515 14.522 81.6516C15.0893 81.085 15.0893 80.235 15.0893 79.3851C14.2384 75.702 13.1038 72.0189 12.2529 68.3357C11.9693 67.2025 11.6857 66.6358 10.2675 66.3525C7.71476 66.0692 5.44568 65.2193 2.89296 64.936C0.340246 64.3693 -0.227024 63.5194 0.0566109 61.2528C0.623882 55.8698 1.47479 50.4868 2.32569 45.1038C2.60933 43.1206 3.74387 42.554 5.72932 42.554C8.56567 42.8373 11.1184 42.8373 13.9547 42.8373C14.522 42.8373 15.3729 42.554 15.6566 41.9874C17.642 37.7376 19.6274 33.7712 21.8965 29.5215ZM93.089 60.6862C93.3726 43.6873 78.9072 28.9548 61.6055 28.6715C44.0201 28.3882 29.5547 42.554 28.9874 60.1196C28.7038 77.4018 42.8855 92.1343 60.4709 92.4176C78.34 92.9842 92.8054 78.8184 93.089 60.6862Z"
          fill="currentColor"
        />
        <path
          d="M161.161 93.2676C160.877 95.5341 160.31 97.8007 160.31 100.067C160.31 100.634 160.594 101.767 161.161 102.05C163.714 104.034 166.266 106.017 168.819 107.717C169.386 108 170.237 108.283 171.088 108C173.074 107.15 175.059 106.3 176.761 105.167C178.463 104.317 179.597 104.6 180.448 106.3C182.717 109.983 184.986 113.666 187.255 117.633C188.39 119.333 188.106 120.466 186.404 121.599C184.419 123.016 181.583 124.149 180.732 126.132C179.881 128.115 181.866 130.665 181.583 133.215C181.299 137.465 183.568 139.165 187.255 140.015C190.46 140.95 191.595 143.046 190.659 146.248C189.808 149.647 188.957 153.331 188.106 156.73C187.539 158.714 186.688 159.28 184.703 158.997C182.433 158.43 179.597 157.297 177.612 158.147C175.91 158.714 174.775 161.547 173.357 163.53C173.074 163.813 173.074 164.097 172.79 164.097C169.386 166.363 170.237 169.196 171.939 172.029C174.775 176.562 174.492 176.846 169.67 179.679C166.834 181.379 163.997 183.079 160.877 184.779C158.608 185.912 158.041 185.912 156.623 183.929C155.204 181.945 154.354 179.396 152.368 178.546C150.666 177.696 148.114 178.829 145.845 179.112C145.277 179.112 144.426 179.396 143.859 179.396C140.455 178.829 139.605 180.812 138.754 183.645C136.768 190.162 137.052 189.595 130.812 187.895C127.692 187.045 124.572 186.195 121.452 185.629C119.75 185.062 118.899 184.212 119.183 182.229C119.466 179.962 120.034 177.979 120.034 175.996C120.034 175.429 119.75 174.579 119.183 174.013C116.63 172.029 114.077 170.046 111.241 168.346C110.674 168.063 109.823 167.78 108.972 168.063C106.986 168.913 105.001 170.046 103.299 170.896C101.881 171.746 100.463 171.463 99.612 169.763C97.3429 166.08 95.0738 162.113 92.8047 158.43C91.6702 156.73 92.2374 155.597 93.6556 154.464C95.3574 153.331 97.0592 152.197 98.7611 150.781C99.3283 150.214 99.612 149.364 99.612 148.514C99.3283 145.398 98.7611 142.565 98.1938 139.448C98.1938 138.315 97.6265 137.465 96.492 137.182C94.5065 136.615 92.5211 135.765 90.5356 135.198C88.8338 134.632 88.2665 133.782 88.8338 132.082C89.9684 127.549 91.1029 123.016 92.2374 118.483C92.5211 117.066 93.6556 116.5 95.3574 116.5C97.3429 116.783 99.612 117.066 101.597 117.633C102.732 117.916 103.583 117.633 104.15 116.5C105.852 113.95 107.554 111.4 109.539 109.133C110.39 108.283 110.39 107.717 109.823 106.583C108.972 104.884 107.837 102.9 106.986 100.917C106.136 99.2172 106.419 98.084 108.121 97.234C111.808 94.9675 115.496 92.701 119.466 90.4345C121.452 89.3012 122.303 89.5845 123.721 91.2844C125.139 93.2676 125.99 95.8174 127.975 96.6674C129.677 97.5173 132.514 96.1008 134.783 95.8174C135.35 95.8174 135.917 95.5341 136.768 95.5341C139.888 96.1008 141.023 94.1176 141.59 91.5677C141.874 90.1511 142.441 89.0179 143.008 87.6013C143.575 86.1847 144.426 85.6181 145.845 85.9014C150.383 87.0347 154.921 88.1679 159.459 89.3012C160.594 91.0011 161.444 91.851 161.161 93.2676ZM113.51 138.315C113.51 153.047 125.423 165.23 140.455 165.23C155.488 165.23 167.401 153.047 167.401 138.315C167.401 123.582 155.204 111.683 140.455 111.683C125.423 111.4 113.51 123.582 113.51 138.315Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_136">
          <rect width="191" height="189" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
export default SettingsSVG;
