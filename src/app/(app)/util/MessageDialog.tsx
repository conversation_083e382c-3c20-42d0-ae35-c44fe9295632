import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import Button from "~/component/button";
import Dropdown, { Option } from "~/app/(app)/util/Dropdown";
import { BiRfid } from "react-icons/bi";

interface MessageDialogProps {
  message: string;
  title: string;
  onYes: (returnValue?: string) => void;
  onNo: () => void;
  yesLabel?: string;
  noLabel?: string;
  withSelect?: boolean;
  selectOptions?: Option[];
}

const MessageDialog: React.FC<MessageDialogProps> = ({
  message,
  title,
  onYes,
  onNo,
  yesLabel = "Ja",
  noLabel = "Nein",
  withSelect = false,
  selectOptions = [],
}) => {
  const [selectedOptionId, setSelectedOptionId] = useState<string>("");
  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-opacity-50 backdrop-blur-sm">
      <div className="w-2/3 rounded-xl bg-white p-4 text-lg shadow-soft-3xl md:w-1/2 lg:w-1/3">
        {title && <h2 className="mb-2 text-xl font-bold">{title}</h2>}
        <p className={"text-black"}>{message}</p>

        <div className="flex w-full justify-center gap-1 rounded-b-xl border-t border-solid border-slate-100 p-3">
          {withSelect && (
            <Dropdown
              placeHolder={"Bitte auswählen"}
              canDelete={false}
              icon={<BiRfid className={"mr-1 items-center"} size={20} />}
              options={selectOptions?.map((option) => {
                return { ...option, selected: option.id === selectedOptionId };
              })}
              onChange={(id) => {
                setSelectedOptionId(id);
              }}
              onDelete={() => console.log("dummy")}
            />
          )}
        </div>

        <div className="flex shrink-0  items-center justify-end justify-center gap-1 rounded-b-xl border-t border-solid border-slate-100 p-3">
          <Button
            className={"w-1/3"}
            onClick={() => (withSelect ? onYes(selectedOptionId) : onYes())}
          >
            {yesLabel}
          </Button>

          {noLabel && (
            <Button className={"w-1/3"} onClick={() => onNo()}>
              {noLabel}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MessageDialog;
