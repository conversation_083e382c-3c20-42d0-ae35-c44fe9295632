import React from "react";
import { FaMoneyBillWaveAlt } from "react-icons/fa";
import Card from "../../../component/card";
import prisma from "../../../server/db/prisma";

import { formatEuro, formatKwh } from "../../../utils/format/numbers";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getOusBelowOu } from "~/server/model/ou/func";
import { MdAttachMoney } from "react-icons/md";
import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";

export const revalidate = 0;
const thgPrice = 0.05;
const getGrossMarginCurrentYear = async () => {
  const date = new Date();
  const yearStart = new Date(date.getFullYear(), 0, 0);

  const endDate = new Date();
  yearStart.setUTCHours(0, 0, 0, 0);
  endDate.setUTCHours(23, 59, 59, 999);

  const session = await getServerSession(authOptions);

  if (!session) {
    return {};
  }
  const ouWithChildren = await getOusBelowOu(session.user.selectedOu);
  const ouCodes = ouWithChildren?.map((ou) => `'${ou.code}'`);
  const cdrs = await prisma.cdr.findMany({
    where: {
      End_datetime: {
        gte: yearStart,
        lte: endDate,
      },
      OU_Code: { in: ouCodes },
    },
    include: {
      cost: true,
      tarif: true,
    },
  });

  //const calcCdrs = await computeRoamingCostByEmpPrice(cdrs);

  const kWh = cdrs.reduce((acc, cdr) => acc + (cdr.Volume || 0), 0);

  const revenue = cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
  const energyCost = cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);

  const energyGrossMargin = revenue - energyCost;

  const grossMargin = energyGrossMargin + thgPrice * kWh;

  const diff = date.getTime() - yearStart.getTime();
  const oneHour = 1000 * 60 * 60;
  const hourOfYear = Math.floor(diff / oneHour);

  const isLeapYear =
    (date.getFullYear() % 4 == 0 && date.getFullYear() % 100 != 0) || date.getFullYear() % 400 == 0;
  const totalHoursOfYear = isLeapYear ? 366 * 24 : 365 * 24;

  return {
    thgUntilNow: thgPrice * kWh,
    energyGrossMarginUntilNow: energyGrossMargin,
    kWhUntilNow: kWh,
    thgCurrentYear: ((thgPrice * kWh) / hourOfYear) * totalHoursOfYear,
    energyGrossMarginCurrentYear: (energyGrossMargin / hourOfYear) * totalHoursOfYear,
    grossMarginCurrentYear: (grossMargin / hourOfYear) * totalHoursOfYear,
    kWhCurrentYear: (kWh / hourOfYear) * totalHoursOfYear,
  };
};

const Page = async () => {
  const {
    thgUntilNow,
    energyGrossMarginUntilNow,
    kWhUntilNow,
    thgCurrentYear,
    energyGrossMarginCurrentYear,
    grossMarginCurrentYear,
    kWhCurrentYear,
  } = await getGrossMarginCurrentYear();

  return (
    <>
      <Card
        className={"mt-5"}
        header_left={
          <h2 className={"mb-0 mb-2 font-bold dark:text-white"}>
            Hochrechnung {new Date().getFullYear()}
          </h2>
        }
      >
        <div className={"flex w-full flex-col gap-1  sm:flex-row sm:gap-2"}>
          <RealtimeWidget
            caption={"Energie Marge"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(energyGrossMarginCurrentYear ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der Marge von Ein- und Verkauf,
              basierend auf den hinterlegten Stromverträgen und bereits erfassten Ladevorgängen
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"THG"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(thgCurrentYear ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der THG Erlöse der bereits erfassten
              Ladevorgänge unter Annahme von 5ct/kWh
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"Energie"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatKwh(kWhCurrentYear ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der Energie aller bereits erfassten
              Ladevorgängen
            </span>
          </RealtimeWidget>
        </div>
      </Card>
      <Card
        className={"mt-5"}
        header_left={
          <h2 className={"mb-0 mb-2 font-bold dark:text-white"}>Aktuelles Jahr bis heute</h2>
        }
      >
        <div className={"flex w-full flex-col gap-1  sm:flex-row sm:gap-2"}>
          <RealtimeWidget
            caption={"Energie Marge"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(energyGrossMarginUntilNow ?? 0)}
          >
            <span className={"text-xs"}>
              Marge von Ein- und Verkauf, basierend auf den hinterlegten Stromverträgen und bereits
              erfassten Ladevorgängen
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"THG"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(thgUntilNow ?? 0)}
          >
            <span className={"text-xs"}>
              THG Erlöse der bereits erfassten Ladevorgänge unter Annahme von 5ct/kWh
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"Energie"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatKwh(kWhUntilNow ?? 0)}
          >
            <span className={"text-xs"}>Energie aller bereits erfassten Ladevorgängen</span>
          </RealtimeWidget>
        </div>
      </Card>
    </>
  );
};
export default Page;
