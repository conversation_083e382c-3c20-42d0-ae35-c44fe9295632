import prisma from "~/server/db/prisma";
import RoamingTariffForm from "~/app/(app)/tarif/roaming/RoamingTariffForm";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getCpoContracts } from "~/utils/dbmodel/dbmodel";

export const revalidate = 0;

const getRoamingTariff = async (slug: string) => {
  const roamingTariff = await prisma.tarif.findUniqueOrThrow({
    where: {
      id: slug,
    },
  });
  return JSON.parse(JSON.stringify(roamingTariff));
};

const Page = async ({ params }: { params: { slug: string } }) => {
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }
  if (!params.slug) {
    return <>Missing Params</>;
  }

  const roamingTariff = await getRoamingTariff(params.slug);
  const cpoContracts = await getCpoContracts();

  return (
    <>
      <RoamingTariffForm tarif={roamingTariff} cpoContracts={cpoContracts}></RoamingTariffForm>
    </>
  );
};

export default Page;
