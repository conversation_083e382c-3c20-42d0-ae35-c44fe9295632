import RoamingTariffForm from "~/app/(app)/tarif/roaming/RoamingTariffForm";
import Card from "~/component/card";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getCpoContracts } from "~/utils/dbmodel/dbmodel";

const NewRoamingTariffPage = async ({ params }: { params: { tariffType: string } }) => {
  const { tariffType } = params;
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }
  const cpoContracts = await getCpoContracts();
  return (
    <Card>
      <RoamingTariffForm tarif={false} cpoContracts={cpoContracts}></RoamingTariffForm>
    </Card>
  );
};

export default NewRoamingTariffPage;
