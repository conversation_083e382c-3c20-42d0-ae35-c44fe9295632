"use client";
import { AiOutlinePlusCircle } from "react-icons/ai";
import React from "react";

import Link from "next/link";
import Button from "~/component/button";

export const NewTariffButton = ({ href, label }: { href: string; label: string }) => {
  return (
    <>
      <Link href={href}>
        <Button>
          <AiOutlinePlusCircle className={"mr-2"} size={"1rem"} />
          {label}
        </Button>
      </Link>
    </>
  );
};
