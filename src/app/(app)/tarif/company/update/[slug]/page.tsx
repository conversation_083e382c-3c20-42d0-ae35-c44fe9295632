import prisma from "~/server/db/prisma";
import CompanyTariffForm from "~/app/(app)/tarif/company/CompanyTariffForm";

export const revalidate = 0;

const getCompanyTariffs = async (slug: string) => {
  const companyTariffs = await prisma.companyTarif.findUniqueOrThrow({
    where: {
      id: slug,
    },
  });
  return JSON.parse(JSON.stringify(companyTariffs));
};

const Page = async ({ params }: { params: { slug: string } }) => {
  if (!params.slug) {
    return <>Missing Params</>;
  }

  const companyTariffs = await getCompanyTariffs(params.slug);

  return (
    <>
      <CompanyTariffForm tarif={companyTariffs}></CompanyTariffForm>
    </>
  );
};

export default Page;
