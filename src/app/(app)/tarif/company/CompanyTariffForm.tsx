"use client";
import { Company<PERSON>arif, Role } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import But<PERSON> from "~/component/button";
import { FaClock, FaInfoCircle, FaParking, FaPlug, FaSave, FaUser, FaUsers } from "react-icons/fa";
import { BsLightningChargeFill } from "react-icons/bs";
import { useSession } from "next-auth/react";
import { TbClockPause } from "react-icons/tb";
import { BiRfid } from "react-icons/bi";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import { userStore } from "~/server/zustand/store";

type CompanyTariffType = Omit<CompanyTarif, "validFrom" | "validTo"> & {
  validFrom: string;
  validTo: string;
};

type CompanyTariffTypeErrorType = {
  [K in keyof CompanyTariffType]?: {
    message: string;
  };
};

interface Props {
  tarif?: CompanyTariffType;
}

const CompanyTariffForm = ({ tarif }: Props) => {
  const router = useRouter();
  const [apiErrorMessage, setApiErrorMessage] = useState<string>("");
  const { data: session } = useSession();
  const userState = userStore();
  const [firmenName, setFirmenName] = useState<string>(session?.user.selectedOu.name ?? "...");

  let defaultValues = {};
  if (tarif) {
    defaultValues = {
      ...tarif,
      validFrom: tarif.validFrom.substring(0, 10),
      validTo: tarif.validTo.substring(0, 10),
    };
  } else {
    defaultValues = { oneTimeFee: session?.user?.selectedOu?.defaultEMPCardOneTimeFee ?? "" }; // hard coded 9€ brutto
  }

  useEffect(() => {
    setFirmenName(userState.selectedOuName);
  }, [userState]);

  const {
    getValues,
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<CompanyTariffType, CompanyTariffTypeErrorType>({ defaultValues: defaultValues });
  const watchedFields = watch();

  const onSubmit: SubmitHandler<CompanyTariffType> = async (data) => {
    console.log(data);
    const response = await fetch(`/api/tarif/companyTarif`, {
      method: "POST",
      body: JSON.stringify({ data }),
    });

    if (response.ok) {
      router.push("/emp/tarif/managerview");
      router.refresh();
    } else {
      const message = await response.json();
      setApiErrorMessage(message?.message ?? "Fehler beim Speichern");
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <input disabled={!getValues("id")} type={"text"} {...register("id")} className={"hidden"} />
        <div className="mb-0 flex justify-between rounded-t-2xl p-6 ">
          <h5 id={"basic-data"} className="flex flex-row gap-1 dark:text-white">
            <span>Neuer Firmentarif für</span>
            <span>{firmenName}</span>
          </h5>
          {Object.keys(errors).length > 0 && (
            <ul className={"text-red-500"}>
              {Object.keys(errors).map((fieldName, index) => (
                <li key={index}>
                  {errors[fieldName as keyof CompanyTariffTypeErrorType]?.message}
                </li>
              ))}
            </ul>
          )}

          <Button className={"hidden items-center sm:flex"} type={"submit"}>
            <FaSave className={"mr-1"} />
            Tarif {tarif?.id ? "aktualisieren" : "Speichern"}
          </Button>
        </div>
        <div className="flex-auto p-6 pt-0">
          <div className="-mx-3 flex flex-wrap">
            <div
              className={`${
                session?.user?.role != Role.ADMIN ? " w-full sm:w-6/12" : "w-1/2 sm:w-5/12"
              } max-w-full flex-0 px-3`}
            >
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                Name
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  placeholder={"z.B. Musterfirma 03/2024"}
                  {...register("name", { required: "Ein Name ist erforderlich" })}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            {session?.user.role == Role.ADMIN && (
              <div className="w-1/2 max-w-full flex-0 px-3 sm:w-1/12">
                <label
                  className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="optional"
                >
                  Optional
                </label>

                <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                  <input
                    {...register("optional")}
                    id={"optional"}
                    className={`relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3`}
                    type="checkbox"
                  />
                </div>
              </div>
            )}

            <div className="w-1/2 max-w-full flex-0 px-3 sm:w-3/12">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="gültig ab"
              >
                Gültig ab
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("validFrom", { valueAsDate: true })}
                  type="date"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-1/2 max-w-full flex-0 px-3 sm:w-3/12">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Gültig bis"
              >
                Gültig bis
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("validTo", { valueAsDate: true })}
                  name="validTo"
                  type="date"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <BsLightningChargeFill size={10} color={"orange"} />
                <label
                  className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="energyprice"
                >
                  Preis pro kWh in Euro
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id={"energyprice"}
                  {...register("energyPrice", {
                    validate: (value) =>
                      value > -1 || "Energiekostem müssen größer 0 sein, wenn Strom verkauf wird",
                  })}
                  type="number"
                  min={"0"}
                  step={"0.01"}
                  placeholder={"z.B. 0.40 für 40cent/kWh"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <FaPlug size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="sessionprice"
                >
                  Preis pro Session in Euro
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  min={"0"}
                  id={"sessionprice"}
                  {...register("sessionPrice")}
                  type="number"
                  step={"0.01"}
                  placeholder={"z.B. 1.00 für 1€ pro Ladevorgang"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <FaClock size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="mindest Lademenge in kWh"
                >
                  Mindest-Lademenge in kWh
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("minChargingEnergy")}
                  type="number"
                  step={"0.01"}
                  min={"0"}
                  placeholder={"z.B. 0.2 für 0.2kWh"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <FaClock size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="mindest Ladezeit in sek"
                >
                  Mindest-Ladezeit in Sekunden
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("minChargingTime")}
                  type="number"
                  step={"1"}
                  min={"0"}
                  placeholder={"z.B. 120 für 2 Minuten"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <BsLightningChargeFill size={10} color={"orange"} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="currentType"
                >
                  Ladesäulentyp (AC / DC)
                </label>
              </div>

              <select
                id={"currentType"}
                {...register("currentType")}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              >
                <option value="AC">AC (Typ2 - Wechselstrom)</option>
                <option value="DC">DC (CCS - Gleichstrom)</option>
              </select>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <TbClockPause size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="blockingFee"
                >
                  Blockiergebühr €/min
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("blockingFee")}
                  type={"number"}
                  min={"0"}
                  step="0.01"
                  placeholder={"z.B. 0.05 für 5 Cent pro minute"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <TbClockPause size={10} />
                <label
                  className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="blockingFeeBeginAtMin"
                >
                  Blockiergebühr ab min
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("blockingFeeBeginAtMin")}
                  type={"number"}
                  placeholder={"z.B. 240 für nach 4 Stunden"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <TbClockPause size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="blockingFeeMax"
                >
                  Blockiergebühr Max €
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("blockingFeeMax")}
                  type={"number"}
                  step="0.01"
                  min={"0"}
                  placeholder={"z.B. 12 für 12€ "}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <BiRfid size={10} />
                <label
                  className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="oneTimeFee"
                >
                  Einmalgebühr pro Karte €
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("oneTimeFee")}
                  disabled={true}
                  type={"number"}
                  step="0.1"
                  min={"0"}
                  placeholder={"0.0"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-soft-primary-outline focus:outline-none  disabled:cursor-not-allowed dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
                <span className={"ml-2 mt-2 flex text-secondary"}>
                  Die Einmalgebühr wird beim Bestellen einer Ladekarte fällig. Die Kosten werden vom
                  Portalbetreiber nach P1 festgelegt und können nicht editiert werden.
                </span>
              </div>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                <FaUser size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="oneTimeFeePayer"
                >
                  Einmalgebühr wird bezahlt von:
                </label>
              </div>

              <select
                id={"oneTimeFeePayer"}
                {...register("oneTimeFeePayer")}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              >
                <option value={Role.CARD_HOLDER}>Besteller zahlt selber</option>
                <option value={Role.CARD_MANAGER}>Kosten werden übernommen</option>
              </select>
              <span className={"ml-2 mt-2 flex text-secondary"}>
                Hier kann gewählt werden, ob die Kartenbesteller die Einmalgebühr selber zahlen oder
                ob die Kosten durch den Arbeitgeber/Club übernommen und in Rechnung gestellt werden.
              </span>
            </div>

            <div className="w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <div className={"mb-2 mt-6 flex items-center "}>
                {" "}
                <BiRfid size={10} />
                <label
                  className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="basicFee"
                >
                  Grundgebühr (monatlich) €
                </label>
              </div>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("basicFee")}
                  type={"number"}
                  step="0.1"
                  placeholder={"0.0"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="mb-2 mt-4 w-full max-w-full flex-0 px-3  sm:w-1/2 md:w-4/12 lg:w-3/12">
              <label
                className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="vorschau"
              >
                Vorschau (brutto)
              </label>
              <TarifCard
                title={watchedFields.name}
                description={watchedFields.description ?? "Keine Beschreibung"}
                pricekWh={watchedFields.energyPrice}
                priceSession={watchedFields.sessionPrice}
                tarifName={watchedFields.name}
                optional={false}
                interactive={false}
                tarifId={""}
                currentType={watchedFields.currentType ?? "Nicht ausgewählt"}
                basicFee={watchedFields.basicFee}
                oneTimeFee={watchedFields.oneTimeFee}
                vat={19}
                blockingFee={watchedFields.blockingFee}
                blockingFeeMax={watchedFields.blockingFeeMax}
                blockingFeeBeginAtMin={watchedFields.blockingFeeBeginAtMin}
                size={"small"}
                oneTimeFeePayer={watchedFields.oneTimeFeePayer}
                internal={watchedFields.internal}
              ></TarifCard>
            </div>

            <div className="w-full flex-0 px-3 sm:w-6/12 sm:max-w-full">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="description"
              >
                Intern
              </label>
              <div className="flex rounded-lg">
                <input
                  {...register("internal")}
                  id={"overwriteId"}
                  type="checkbox"
                  className={
                    "relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
                  }
                />
              </div>

              <label
                className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="description"
              >
                Beschreibung
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <textarea
                  placeholder={
                    "Dieser Text wird dem Kunden angezeigt zur Erläuterung des Tarifs. Zum Beispiel: 'Tarif für den Zugang zu allen Ladepunkten am Standort Bremen'"
                  }
                  rows={8}
                  id={"description"}
                  {...register("description")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
        </div>
        <div className={"block w-full sm:hidden"}>
          <Button className={"flex w-full items-center justify-center"} type={"submit"}>
            <FaSave className={"mr-1"} />
            Tarif {tarif?.id ? "aktualisieren" : "Speichern"}
          </Button>
        </div>
      </form>

      {apiErrorMessage && (
        <div className="relative mx-4  rounded-lg border border-solid bg-red-400 p-4 pr-12 text-white">
          {apiErrorMessage}
          <button
            type="button"
            onClick={() => setApiErrorMessage("")}
            className="absolute right-0 top-0 z-2 box-content h-4 w-4 rounded border-0 bg-transparent p-4 text-sm text-white"
          >
            <span aria-hidden="true" className="cursor-pointer text-center">
              &#10005;
            </span>
          </button>
        </div>
      )}
    </>
  );
};

export default CompanyTariffForm;
