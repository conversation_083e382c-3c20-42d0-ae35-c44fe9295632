import Card from "~/component/card";
import CompanyTariffForm from "~/app/(app)/tarif/company/CompanyTariffForm";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

const NewCompanyTariffPage = async () => {
  const session = await getServerSession(authOptions);
  if (session && (session?.user?.role == Role.ADMIN || session.user.role == Role.CARD_MANAGER)) {
    return (
      <Card>
        <CompanyTariffForm />
      </Card>
    );
  }
  return "Keine Berechtigung";
};

export default NewCompanyTariffPage;
