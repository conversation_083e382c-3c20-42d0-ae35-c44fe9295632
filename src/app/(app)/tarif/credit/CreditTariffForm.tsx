"use client";
import React, { useState } from "react";
import { useF<PERSON>, SubmitHandler } from "react-hook-form";
import { useRouter } from "next/navigation";
import But<PERSON> from "~/component/button";
import { FaClock, FaInfoCircle, FaParking, FaPlug, FaSave } from "react-icons/fa";
import { CreditTarif } from "@prisma/client";
import { BsLightningChargeFill } from "react-icons/bs";

type CreditTarifErrorType = {
  [K in keyof CreditTarif]?: {
    message: string;
  };
};
interface Props {
  tarif: CreditTarif | boolean;
}

const CreditTariffForm = ({ tarif }: Props) => {
  const router = useRouter();
  const [apiErrorMessage, setApiErrorMessage] = useState<string>("");

  const {
    register,
    getValues,
    handleSubmit,
    formState: { errors },
  } = useForm<CreditTarif, CreditTarifErrorType>();

  const onSubmit: SubmitHandler<CreditTarif> = async (data) => {
    // Mutate external data source
    const response = await fetch(`/api/tarif/creditTarif`, {
      method: "POST",
      body: JSON.stringify({ data }),
    });
    if (response.ok) {
      router.push("/tarif");
      router.refresh();
    } else {
      const message = await response.json();

      setApiErrorMessage(message?.message ?? "Fehler beim Speichern");
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <input disabled={!getValues("id")} type={"text"} {...register("id")} className={"hidden"} />

        <div className="mb-0 flex flex-row justify-between rounded-t-2xl p-6">
          <h5 className="text-primary">Neuer Credit Tarif</h5>
          {Object.keys(errors).length > 0 && (
            <ul className="text-red-500">
              {Object.keys(errors).map((fieldName, index) => (
                <li key={index}>{errors[fieldName as keyof CreditTarifErrorType]?.message}</li>
              ))}
            </ul>
          )}

          <Button type="submit">
            <FaSave className="mr-1" />
            Tarif Speichern
          </Button>
        </div>

        <div className="flex-auto p-6 pt-0">
          <div className="-mx-3 flex flex-wrap">
            <div className="mb-3  w-1/3 max-w-full flex-0 px-3">
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="name"
              >
                Name
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id={"name"}
                  {...register("name", { required: "Ein Name ist erforderlich" })}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="mb-3  w-1/3  max-w-full flex-0 px-3">
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="validFrom"
              >
                Gültig ab
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id={"validFrom"}
                  {...register("validFrom", { valueAsDate: true })}
                  type="date"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="mb-3 w-1/3 max-w-full flex-0 px-3">
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="validTo"
              >
                Gültig bis
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id={"validTo"}
                  {...register("validTo", { valueAsDate: true })}
                  name="validTo"
                  type="date"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="w-2/4 max-w-full flex-0 px-3">
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="tarifType"
              >
                Flex / Fix
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <select
                  id={"tarifType"}
                  {...register("tarifType")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                >
                  <option value="FIX">Fix</option>
                  <option value="FLEX">Flex</option>
                </select>
              </div>
            </div>

            <div className="w-2/4 max-w-full flex-0 px-3">
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="powertype"
              >
                AC / DC
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <select
                  id={"powertype"}
                  {...register("powerType")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                >
                  <option value="AC">AC</option>
                  <option value="DC">DC</option>
                </select>
              </div>
            </div>

            <div className="mb-3 w-1/2 max-w-full flex-0 px-3 ">
              <FaPlug size={10} />
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="sessionCredit"
              >
                Session Credit €/Session
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("sessionCredit")}
                  name={"sessionCredit"}
                  id="sessionCreditAC"
                  type="number"
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="mb-3 w-1/2 max-w-full flex-0 px-3">
              <BsLightningChargeFill size={10} color={"orange"} />
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="energyCredit"
              >
                Energy Credit €/kWh
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("energyCredit")}
                  name={"energyCredit"}
                  id="energyCreditAC"
                  type="number"
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="mb-3 w-1/2 max-w-full flex-0 px-3">
              <FaParking size={10} />
              <label
                className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="blockingCredit"
              >
                Blocking Credit €/min
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id="blockingCredit"
                  {...register("blockingCredit")}
                  name={"blockingCredit"}
                  type="number"
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="mb-3 w-1/2 max-w-full flex-0 px-3">
              <FaParking size={10} />
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="maxBlockingCreditAC"
              >
                Max Blocking Credit €
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id="maxBlockingCredit"
                  {...register("maxBlockingCredit")}
                  name={"maxBlockingCredit"}
                  type="number"
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>

            <div className="mb-3 w-1/2 max-w-full flex-0 px-3">
              <FaClock size={10} />
              <label
                className="  ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="blockingFeeMinStart"
              >
                Blocking Fee Min Start
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  id="blockingFeeMinStart"
                  {...register("blockingFeeMinStart")}
                  name={"blockingFeeMinStart"}
                  type="number"
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Beispiel für Fehlermeldung */}
      </form>
      {apiErrorMessage && (
        <div className="relative mx-4  rounded-lg border border-solid bg-red-400 p-4 pr-12 text-white">
          {apiErrorMessage}
          <button
            type="button"
            onClick={() => setApiErrorMessage("")}
            className="absolute right-0 top-0 z-2 box-content h-4 w-4 rounded border-0 bg-transparent p-4 text-sm text-white"
          >
            <span aria-hidden="true" className="cursor-pointer text-center">
              &#10005;
            </span>
          </button>
        </div>
      )}
    </>
  );
};

export default CreditTariffForm;
