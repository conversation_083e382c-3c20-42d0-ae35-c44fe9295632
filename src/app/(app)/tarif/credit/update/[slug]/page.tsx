import prisma from "~/server/db/prisma";
import CreditTariffForm from "~/app/(app)/tarif/credit/CreditTariffForm";

export const revalidate = 0;

const getCreditTariff = async (slug: string) => {
  const roamingTariff = await prisma.creditTarif.findUniqueOrThrow({
    where: {
      id: slug,
    },
  });
  return JSON.parse(JSON.stringify(roamingTariff));
};

const Page = async ({ params }: { params: { slug: string } }) => {
  if (!params.slug) {
    return <>Missing Params</>;
  }

  const creditTarif = await getCreditTariff(params.slug);

  return (
    <>
      <CreditTariffForm tarif={creditTarif}></CreditTariffForm>
    </>
  );
};

export default Page;
