import type { Prisma } from "@prisma/client";
import Card from "../../../../../component/card";
import TarifContactMappingList from "./TarifContactMappingList";
import prisma from "~/server/db/prisma";

export const revalidate = 0;

const getContactOffer = async (slug: string) => {
  const tarif = await prisma.tarif.findUnique({
    where: {
      id: slug,
    },
  });
  if (!tarif) {
    const creditTarif = await prisma.creditTarif.findUnique({
      where: {
        id: slug,
      },
    });
    return JSON.parse(JSON.stringify(creditTarif));
  }

  return JSON.parse(JSON.stringify(tarif));
};

const getAllContact = async () => {
  const allContact = await prisma.contact.findMany({
    include: {
      tarifs: {
        include: {
          tarif: true,
        },
      },
      creditTarifs: {
        include: {
          creditTarif: true,
        },
      },
    },
  });
  return JSON.parse(JSON.stringify(allContact));
};

export type ContactsWithIncludes = Prisma.ContactGetPayload<{
  include: {
    tarifs: { include: { tarif: true } };
    creditTarifs: { include: { creditTarif: true } };
  };
}>;

const Page = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;

  if (!slug) {
    return <>Missing Params</>;
  }
  const id = params.slug;

  const tarif = await getContactOffer(id);
  const contacts = await getAllContact();

  return (
    <>
      <Card header_left={"Abonniert"}>
        <>
          <h3 className="dark:text-wh eg:mt-0 mt-6">{tarif.name}</h3>
          <ul>
            <li>Gültig ab {new Date(tarif.validFrom).toLocaleDateString()}</li>
            <li>Gültig bis {new Date(tarif.validTo).toLocaleDateString()}</li>
            <li>Preis pro kWh {tarif.kwh} €</li>
            <li>Preis pro Session {tarif.sessionFee} €</li>
            <li>Mindest Lademenge {tarif.minChargingEnergy} kWh</li>
            <li>Mindest Ladezeit {tarif.minChargingTime} sek</li>
          </ul>
        </>
        <div className={"mt-12"}>
          <h4 className="eg:mt-0 mt-6 text-primary">Abonnenten:</h4>
          <TarifContactMappingList tarif={tarif} contacts={contacts}></TarifContactMappingList>
        </div>
      </Card>
    </>
  );
};

export default Page;
