"use client";
import { CreditTarif, Tarif } from "@prisma/client";
import { useRouter } from "next/navigation";
import React, { useMemo, useTransition } from "react";
import type { ContactsWithIncludes } from "./page";

interface Props {
  tarif: Tarif | CreditTarif;
  contacts: ContactsWithIncludes[];
}

const TarifContactMappingList = ({ tarif, contacts }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const apiEndpoint = useMemo(() => {
    if ("sessionCredit" in tarif) {
      return "/api/contact/mapCreditTarif";
    } else {
      return "/api/contact/mapRoamingTarif";
    }
  }, [tarif]);

  const mapTarifToContact = async (contact: ContactsWithIncludes, subscribed: boolean) => {
    await fetch(apiEndpoint, {
      method: "POST",
      body: JSON.stringify({
        contact: contact,
        tarif: tarif,
        subscribed: subscribed,
      }),
    });
    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
  };

  const filteredContacts = useMemo(() => {
    if ("energyCredit" in tarif) {
      return contacts.filter((contact) => contact.cpo);
    } else {
      return contacts.filter((contact) => !contact.cpo);
    }
  }, [contacts]);

  const isMapped = (contact: ContactsWithIncludes, tarif: Tarif | CreditTarif) => {
    if ("energyCredit" in tarif) {
      return !!contact.creditTarifs.find((contactTarif) => contactTarif.creditTarifId == tarif.id);
    } else {
      return !!contact.tarifs.find((contactTarif) => contactTarif.tarifId == tarif.id);
    }
  };

  return (
    <>
      <ul className={"w-1/2"}>
        {filteredContacts.map((contact) => {
          const mapped = isMapped(contact, tarif);
          return (
            <li key={contact.name}>
              {contact.name}
              <input
                checked={mapped}
                onChange={() => mapTarifToContact(contact, !mapped)}
                type="checkbox"
                className="relative float-right mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200  bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3"
              />
            </li>
          );
        })}
      </ul>
    </>
  );
};

export default TarifContactMappingList;
