"use client";

import React, { useContext, useTransition } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import FormContext from "./formContext";
import { useRouter } from "next/navigation";

import type { Location } from "@prisma/client";
import Card from "~/component/card";
import { MdEditLocationAlt } from "react-icons/md";

interface Props {
  location: Location;
}

type Inputs = {
  name: string;
  exampleRequired: string;
};

const BasicInfo = ({ location }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const {
    getValues,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<Location>({ defaultValues: location || {} });

  const onSubmit: SubmitHandler<Location> = async (data) => {
    // Mutate external data source
    await fetch(`/api/location`, {
      method: "POST",
      body: JSON.stringify({ contact: data }),
    });
    reset(data);
    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
  };

  const { edit } = useContext(FormContext);

  return (
    <Card
      header_left={
        <div className={"flex items-center gap-1"}>
          <h3>
            <MdEditLocationAlt size={25} />
            {location?.name}
          </h3>
        </div>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-0 rounded-t-2xl p-6">
          <h5 id={"basic-data"} className="dark:text-white">
            Basis Daten
          </h5>
        </div>
        <div className="flex-auto p-6 pt-0">
          <div className="-mx-3 flex flex-wrap">
            <div className="w-4/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                Name
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("name")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="id"
              >
                id
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("id")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="coutry_code"
              >
                Country Code
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("country_code")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="party_id"
              >
                Party Id
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("party_id")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-4/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="street"
              >
                Street
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("street")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="houseNumber"
              >
                House Number
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("houseNumber")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="postal_code"
              >
                Postal Code
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("postal_code")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="City"
              >
                City
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("city")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="country"
              >
                Country
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("country")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="hotline_phonenumbere"
              >
                Hotline Phonenumber
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("hotline_phonenumber")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-12/12 w-full max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="note"
              >
                Note
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <textarea
                  disabled={!edit}
                  rows={8}
                  {...register("note")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          {isDirty && (
            <div className="-mx-3 flex">
              <div className={"flex-3 w-full max-w-full px-3"}>
                <button
                  type={"submit"}
                  className="float-right mb-0 mt-16 inline-block cursor-pointer rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
                >
                  Änderungen Speichern
                </button>
              </div>
            </div>
          )}
        </div>
      </form>
    </Card>
  );
};

export default BasicInfo;
