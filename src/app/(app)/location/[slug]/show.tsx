import prisma from "../../../../server/db/prisma";
import React from "react";

export const revalidate = 0; // revalidate every 30sec

const getLocations = async (slug: string) => {
  return prisma.location.findUniqueOrThrow({
    where: {
      id: slug,
    },
    include: {
      powerContract: true,
    },
  });
};

const Show = async ({ params }: { params: { slug: string } }) => {
  const location = await getLocations(params.slug);
};

export default Show;
