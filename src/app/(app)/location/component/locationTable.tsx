"use client";
import { AgGridReact } from "ag-grid-react";

import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import React, { useEffect, useState } from "react";
import { LicenseManager } from "ag-grid-enterprise";
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import Link from "next/link";
import { HiMagnifyingGlass } from "react-icons/hi2";
import { ColumnApi, GridApi } from "ag-grid-community";
import { AiOutlineAppstoreAdd } from "react-icons/ai";

LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

const LocationTable = (location: any) => {
  const [rowData, setRowData] = useState({});

  const [gridApi, setGridApi] = useState<GridApi | undefined>();

  const [columApi, setColumnApi] = useState<ColumnApi | undefined>();

  useEffect(() => {
    const loadLocation = async () => {
      const result = await fetch("api/location");
      const data = await result.json();
      setRowData(data);
    };
    loadLocation();
  }, []);

  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <Link href={`location/${params.data.id}`}>
          <HiMagnifyingGlass />
        </Link>
        <Link href={`powerContract/upsert/${params.data.id}`}>
          <AiOutlineAppstoreAdd />
        </Link>
      </>
    );
  };

  const [columnDefs, setColumnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: "id", width: 100 },
    { field: "name", width: 400 },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: CellRenderer,
      editable: false,
    },
  ]);

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 30,
  };

  return (
    <>
      <div className="ag-theme-alpine" style={{ width: "100%", height: 500 }}>
        <AgGridReact
          gridOptions={defaultBackendGridOptions}
          onRowDataChanged={(api) => {
            api.api.sizeColumnsToFit();
            return api;
          }}
          rowData={Object.values(rowData)}
          columnDefs={columnDefs}
          onGridSizeChanged={(params) => {
            params.api.sizeColumnsToFit();
          }}
        />
      </div>
    </>
  );
};
export default LocationTable;
