import React from "react";

import { MdPriceCheck } from "react-icons/md";
import LocationTable from "./component/locationTable";

export const revalidate = 0;

export default async function Page() {
  return (
    <>
      <div className={"lg:w-12/12 w-full max-w-full shrink-0 px-3 lg:flex-0"}>
        <div
          className="relative flex min-w-0 flex-auto flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-4 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
          id="profile"
        >
          <div className="-mx-3 flex flex-wrap items-center justify-center">
            <div className="w-4/12 max-w-full flex-0 px-3 sm:w-auto">
              <div className="relative inline-flex h-19 w-19 items-center justify-center rounded-xl text-base text-white transition-all duration-200 ease-soft-in-out">
                <MdPriceCheck className={"text-xl text-black"} size={"3rem"} />
              </div>
            </div>
            <div className="my-auto w-8/12 max-w-full flex-0 px-3 sm:w-auto">
              <div className="h-full">
                <h5 className="mb-1 font-bold dark:text-white">Location</h5>
                <p className="mb-0 text-sm font-semibold leading-normal">Übersicht</p>
              </div>
            </div>
            <div className="mt-4 flex max-w-full shrink-0 px-3 sm:ml-auto sm:mt-0 sm:w-auto sm:flex-0"></div>
          </div>
        </div>

        <div
          className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-5 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
          id="romaing-offer-table"
        >
          <LocationTable gridId={"location"} />
        </div>
      </div>
    </>
  );
}
