import UpsertForm from "./[slug]/form";
import prisma from "~/server/db/prisma";
export const revalidate = 0;

const getLocations = async () => {
  const location = await prisma.location.findMany({
    include: {
      powerContract: true,
    },
  });
  return JSON.parse(JSON.stringify(location));
};

const OfferPage = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;

  const locations = await getLocations();

  return (
    <>
      <UpsertForm locations={locations}></UpsertForm>
    </>
  );
};

export default OfferPage;
