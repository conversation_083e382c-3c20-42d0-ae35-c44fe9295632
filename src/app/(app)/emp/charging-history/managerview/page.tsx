import Card from "~/component/card";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import React from "react";
import { ChargingHistoryTable } from "~/app/(app)/emp/charging-history/components/ChargingHistoryTable";
import { Role } from "@prisma/client";
import Headline from "~/component/Headline";
import NotFound from "~/app/(app)/not-found";
import { getOusBelowOu } from "~/server/model/ou/func";

const getCdrsForCardManager = async () => {
  const session = await getServerSession(authOptions);

  const ou = session?.user?.selectedOu;

  if (ou) {
    const ouWithChildren = await getOusBelowOu(ou);
    const ouCodes = ouWithChildren?.map((ou) => `'${ou.code}'`);
    return prisma.cdr.findMany({
      where: { OU_Code: { in: ouCodes }, End_datetime: { gt: new Date("01.01.2024") } }, //avoid old CDRs from 'recycled' OUs
      include: { companyTarif: true, tarif: true, cost: true, creditPayout: true },
    });
  }
};

const Page = async () => {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    (session?.user?.role !== Role.CARD_MANAGER &&
      session?.user?.role !== Role.ADMIN &&
      session?.user?.role !== Role.CPO)
  ) {
    return <NotFound />;
  }
  return (
    <>
      <Card className={"mb-4"}>
        <Headline title={"Ladevorgänge"} />
        <div
          className="border-primary relative mb-3 rounded-xl border bg-gray-100 px-4 py-3  text-primary sm:w-70/100"
          role="alert"
        >
          Hier können Sie alle Ladevorgänge an ihrer Infrastruktur einsehen.
        </div>
        <ChargingHistoryTable cdrs={(await getCdrsForCardManager()) ?? []} />
      </Card>
    </>
  );
};

export default Page;
