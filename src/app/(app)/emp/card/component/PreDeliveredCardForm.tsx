"use client";

import React, { useState } from "react";
import Button from "~/component/button";
import { useRouter } from "next/navigation";
import { FaCheckCircle } from "react-icons/fa";
import { FiLoader } from "react-icons/fi";

const PredeliverdCardForm = () => {
  const [cardValid, setCardValid] = useState<boolean>(false);
  const [cardNumber, setCardNumber] = useState<string>("");
  const [verifying, setVerifying] = useState<boolean>(false);
  const [cardNotFoundMessage, setCardNotFoundMessage] = useState<string>("");
  const router = useRouter();
  const checkCardNumber = async () => {
    setVerifying(true);
    try {
      const res = await fetch(`/api/emp/verify-card?cardnumber=${cardNumber}&create=1`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (res.status == 200) {
        setCardValid(true);
        setCardNotFoundMessage("");

        router.refresh();
        setTimeout(() => {
          setCardValid(false);
          setCardNumber("");
        }, 3000);
      } else {
        setCardNotFoundMessage(await res.text());
      }
      setVerifying(false);
      // Hier könnten Sie einen Erfolgshandler hinzufügen
    } catch (error) {
      setVerifying(false);
      setCardNotFoundMessage("Fehler beim Prüfen der Kartennummer");

      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };

  return (
    <>
      <div className={"mb-5 flex flex-col gap-1"}>
        <span>Hier können Sie eine weitere Ladekarte hinzufügen, die Sie erhalten haben.</span>
        <div className="row flex  w-full flex-col items-center gap-0 sm:w-auto sm:flex-row sm:gap-1 md:max-w-70/100 ">
          <label className="self-start font-medium sm:self-auto">Kartennummer:</label>
          <div className={"flex w-full flex-row  items-center gap-1 sm:w-auto "}>
            <input
              value={cardNumber}
              disabled={verifying || cardValid}
              onChange={(e) => setCardNumber(e.target.value)}
              className={`disabled w-full sm:max-w-52 ${
                cardValid ? "bg-green-100" : "bg-eul-lightgray"
              }  block appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none
              `}
            />
            {cardValid && (
              <div>
                <FaCheckCircle color={"green"} size={18} />
              </div>
            )}
          </div>

          {!cardValid && (
            <Button
              className={"mt-4 w-full self-start sm:mt-0 sm:max-w-52 sm:self-auto "}
              onClick={checkCardNumber}
              disabled={verifying}
            >
              Prüfen und Aktivieren {verifying && <FiLoader className="animate-spin" />}
            </Button>
          )}
        </div>
        {cardNotFoundMessage && (
          <div>
            <span className={"text-red-400"}>Fehler beim Prüfen: {cardNotFoundMessage}!</span>
          </div>
        )}
      </div>
    </>
  );
};

export default PredeliverdCardForm;
