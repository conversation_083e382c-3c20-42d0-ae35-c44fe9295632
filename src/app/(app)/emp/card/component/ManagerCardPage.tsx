"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

import React from "react";

import PageContentWrapper from "~/component/PageContentWrapper";

import { EMPCardTable } from "~/app/(app)/emp/card/component/EMPCardTable";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import Link from "next/link";
import StyledLink from "~/app/(app)/util/StyledLink";
import EMPCard from "~/app/(app)/emp/card/component/EMPCard";

const getEMPCardsForManager = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return [];
  }

  const ouId = session?.user?.ou?.id;

  const empcards = await prisma.eMPCard.findMany({
    where: {
      OR: [{ user: { ouId: ouId } }, { contact: { ouId: ouId } }],
    },
    include: {
      user: true,
      contact: true,
      physicalCard: true,
      tarifs: {
        include: {
          tarif: true,
        },
      },
    },
  });
  return empcards;
};
export const ManagerCardPage = async () => {
  const empCards = (await getEMPCardsForManager()) ?? [];
  return (
    <>
      <Headline title={"Ladekarten"} />
      <div className={"flex flex-col justify-between gap-1 sm:flex-row"}>
        <div
          className="border-primary relative mb-3 rounded-xl border bg-gray-100 px-4 py-3 text-primary sm:w-70/100"
          role="alert"
        >
          <span>
            Hier können Sie alle Ladekarten einsehen, die einem Ihrer Tarife zugeordnet wurden.
            Diese Ladekarten können Ihre Ladepunkte somit freischalten und werden zum gebuchten
            Tarif abgerechnet.
          </span>
          <span className={"mt-5"}>
            Desweiteren können Sie eine interne Ladekarte bestellen, die einem internen Tarif
            zugeordnet wird. Interne Tarife werden nur zur Berechnung der Ladevorgänge verwendet
            werden aber nicht in Rechnung gestellt. Intere Karten sind ideal für Firmenfahrzeuge,
            die Laden sollen, jedoch keiner Partei in Rechnung gestellt werden.
          </span>
        </div>
        <div className={" w-full max-w-75 "}>
          <StyledLink href="/emp/card/new/internal">Interne Ladekarte bestellen</StyledLink>
        </div>
      </div>
      <div>
        <div className={"gap-2 md:grid md:grid-cols-2 md:gap-4 lg:max-w-240"}>
          {empCards
            .filter((ecard) => ecard.contact)
            .sort((a, b) => (a.deactivatedAt ? 1 : -1))
            .map((card, index) => (
              <EMPCard card={card} key={index} />
            ))}
        </div>
      </div>
      <EMPCardTable empcards={empCards} />
    </>
  );
};
