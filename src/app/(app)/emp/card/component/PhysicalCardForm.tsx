"use client";

import { useForm } from "react-hook-form";
import React, { useState, useEffect } from "react";

import But<PERSON> from "~/component/button";
import { FiLoader } from "react-icons/fi";
import prisma from "~/server/db/prisma";
import { ContactProviders } from "~/app/(app)/contact/page";
import { Contact } from "@prisma/client";

interface FormInputs {
  uid: string;
  visualNumber: string;
  contactId: string;
  active: boolean;
  userGroupId?: string;
}

interface UserGroup {
  id: string;
  name: string;
  description?: string;
}

export const PhysicalCardForm = ({ cpos }: { cpos: Contact[] }) => {
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);
  const [loadingUserGroups, setLoadingUserGroups] = useState(true);

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty, isSubmitSuccessful, isLoading },
  } = useForm<FormInputs>({});

  useEffect(() => {
    const fetchUserGroups = async () => {
      try {
        // Fetch UserGroups for current user's OU only
        const response = await fetch("/api/userGroup");
        if (response.ok) {
          const data = await response.json();
          setUserGroups(data);
        }
      } catch (error) {
        console.error("Error fetching user groups:", error);
      } finally {
        setLoadingUserGroups(false);
      }
    };

    fetchUserGroups();
  }, []);
  const onSubmit = async (data: FormInputs) => {
    try {
      const response = await fetch("/api/physicalCard/new", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data }),
      });
      if (!response.ok) {
        setErrorMessage(await response.text());
        setSuccessMessage("");
      } else {
        setErrorMessage("");
        setSuccessMessage("Karte erfolgreich eingepflegt");
        reset();
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
        setSuccessMessage("");
      } else {
        setErrorMessage("Fehler beim Anlegen");
        setSuccessMessage("");
      }
    }
  };

  return (
    <div className={"mb-4"}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className={`sm:max-w-3/4 w-full  flex-col  space-y-4 rounded-lg bg-white transition-all`}
      >
        <div className="flex flex-col gap-2 sm:flex-row">
          <div className="">
            <label className="block text-sm font-medium">UID / IdTag</label>
            <input
              placeholder={"xxaayybb12"}
              {...register("uid", { required: "Bitte eine UID eingeben" })}
              className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
            {errors.uid && <p className={"text-red-500"}>{errors.uid.message}</p>}
          </div>
          <div className="">
            <label className="block text-sm font-medium">Kartennummer</label>
            <input
              placeholder={"XXX-397023"}
              {...register("visualNumber", {
                required: "Bitte eine Kartennummer (Visual Number) eingeben",
              })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
            {errors.visualNumber && <p className={"text-red-500"}>{errors.visualNumber.message}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium">Aktivieren</label>
            <input
              {...register("active")}
              type="checkbox"
              className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200  bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:bg-primary checked:bg-primary  checked:bg-none checked:bg-right checked:after:translate-x-5.3"
            />
            {errors.visualNumber && <p className={"text-red-500"}>{errors.visualNumber.message}</p>}
          </div>
          <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
              Verknüpfter CPO
            </label>
            <select
              {...register("contactId")}
              className="form-select m-0 block w-full appearance-none
      rounded border border-solid
      border-gray-300 bg-white bg-clip-padding
      bg-no-repeat px-3 py-1.5
      text-base font-normal
      text-gray-700 transition ease-in-out
      focus:border-blue-600 focus:bg-white focus:text-gray-700 focus:outline-none"
              aria-label="Default select example"
            >
              <option key={"placeholder"} value={""}>
                Bitte wählen
              </option>
              {cpos &&
                cpos.map((contact) => (
                  <option key={contact.id} value={contact.id}>
                    {contact.name}
                  </option>
                ))}
            </select>
            {errors.contactId && <span className="text-red-500">{errors.contactId.message}</span>}
          </div>
          <div className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
            <label className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
              Nutzergruppe (optional)
            </label>
            <select
              {...register("userGroupId")}
              className="form-select m-0 block w-full appearance-none
      rounded border border-solid
      border-gray-300 bg-white bg-clip-padding
      bg-no-repeat px-3 py-1.5
      text-base font-normal
      text-gray-700 transition ease-in-out
      focus:border-blue-600 focus:bg-white focus:text-gray-700 focus:outline-none"
              aria-label="UserGroup select"
              disabled={loadingUserGroups}
            >
              <option key={"no-group"} value={""}>
                Keine Nutzergruppe
              </option>
              {userGroups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                  {group.description && ` - ${group.description}`}
                </option>
              ))}
            </select>
            {loadingUserGroups && (
              <span className="text-sm text-gray-500">Lade Nutzergruppen...</span>
            )}
          </div>
          <div className={" mb-2 content-end"}>
            <Button
              disabled={(isSubmitting || isSubmitSuccessful) && !isDirty}
              className={" w-full sm:max-w-64"}
              type="submit"
            >
              Speichern {isSubmitting ? <FiLoader className="animate-spin" /> : ""}
            </Button>
          </div>
        </div>
      </form>
      {successMessage && <span className={"text-green-600"}>{successMessage}</span>}
      {errorMessage && <span className={"text-red-600"}>{errorMessage}</span>}
    </div>
  );
};
