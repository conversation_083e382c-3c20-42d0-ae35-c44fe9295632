"use client";

import React from "react";

import { EMPCard } from "@prisma/client";
import Table, { filterParams } from "~/utils/table/table";
import { dateTimeRenderer } from "~/utils/table/formatter";
import { ICellRendererParams } from "ag-grid-community";

import { IoMdCheckmarkCircleOutline, IoMdRemoveCircleOutline } from "react-icons/io";
import { FaGear } from "react-icons/fa6";
import { ValueFormatterParams } from "ag-grid-community/dist/lib/entities/colDef";

export const EMPCardTable = ({ empcards }: { empcards: EMPCard[] }) => {
  const gridOptions = { suppressAggFuncInHeader: true };
  const columnDefs = [
    {
      field: "id",
      headerName: "Eigentümer",
      editable: false,
      enableRowGroup: true,
      valueFormatter: (params: ValueFormatterParams) => {
        if (params?.data?.contact) {
          return params.data.contact.name;
        } else if (params?.data?.user) {
          return params.data.user.email;
        }
      },
    },
    {
      field: "orderedAt",
      filter: "agDateColumnFilter",
      headerName: "Bestellt am",
      editable: false,
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      enableRowGroup: false,
    },
    {
      field: "deactivatedAt",
      filter: "agDateColumnFilter",
      headerName: "Deaktiviert am",
      editable: false,
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      enableRowGroup: false,
    },
    {
      field: "active",
      headerName: "Aktiv",
      editable: false,
      enableRowGroup: true,
      cellRenderer: (params: ICellRendererParams) => {
        {
          if (params.data.active == 1) {
            return (
              <>
                <IoMdCheckmarkCircleOutline size={20} className={"mr-1 text-green-400"} />
                <span
                  title={
                    "Karte ist aktiv und kann zum Freischalten von Ladepunkten verwendet werden"
                  }
                >
                  Karte ist aktiviert
                </span>
              </>
            );
          }
          if (params.data.active == 0) {
            if (params.data.deactivatedAt) {
              return (
                <>
                  <IoMdRemoveCircleOutline size={20} className={"mr-1 text-red-400"} />
                  <span
                    title={
                      "Ladekarte wurde deaktiviert und kann nicht mehr zum Freischalten verwendet werden."
                    }
                  >
                    Karte ist deaktiviert
                  </span>
                </>
              );
            } else {
              return (
                <>
                  <FaGear size={20} className={"mr-1 text-orange-400"} />
                  <span title={"Karte wurde versand, jedoch noch nicht im Portal aktiviert"}>
                    noch nicht aktiviert
                  </span>
                </>
              );
            }
          }
        }
      },
    },
  ];
  return <Table gridOptions={gridOptions} columnDefs={columnDefs} rowData={empcards} />;
};
