"use client";
import { BsLightningCharge, BsLightningChargeFill } from "react-icons/bs";
import { Prisma } from "@prisma/client";
import React, { useState } from "react";
import Button from "~/component/button";
import Loading from "~/app/(app)/loading";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import { getBackgroundColor } from "~/utils/register/whitelabelHelper";

export type EMPCardWithPhysicalCardAndTarifs = Prisma.EMPCardGetPayload<{
  include: { physicalCard: true; tarifs: { include: { tarif: true }; contact: true } };
}>;

const EMPCard = ({ card }: { card: EMPCardWithPhysicalCardAndTarifs }) => {
  const [cardNumber, setCardNumber] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [showDeactivateDialog, setShowDeactivateDialog] = useState<boolean>(false);

  const router = useRouter();

  const changeActivation = async (activate: boolean) => {
    if (!card) {
      return;
    }
    setLoading(true);
    try {
      const result = await fetch(`/api/emp/${activate ? "activateCard" : "deactivateCard"}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardId: card.id,
          cardNumber: card?.physicalCard ?? cardNumber,
        }),
      });
      if (!result.ok) {
        const e = await result.text();
        setError(`Fehler beim ${activate ? "Aktivieren" : "Deaktivieren"}: ${e}`);
        setLoading(false);
      } else {
        setTimeout(() => {
          router.refresh();
        }, 3000);

        setError("");
      }
    } catch (error) {
      setError(`Fehler beim ${activate ? "Aktivieren" : "Deaktivieren"}`);
      setLoading(false);
    }
    setShowDeactivateDialog(false);
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  };

  return (
    card && (
      <div
        className={`${
          card.deactivatedAt ? `opacity-40` : ""
        } shadow-md-all  relative mb-3 flex h-48 min-w-0 max-w-100 flex-col break-words rounded-xl bg-clip-border `}
      >
        <BsLightningChargeFill className="absolute right-0 top-0 m-3" size={24} />{" "}
        {/* MdPayment-Icon hinzufügen */}
        <div className="grid grid-cols-[2fr,3fr] p-4">
          <div className="">Kartennummer:</div>
          <div className="">
            {card?.physicalCard ? card.physicalCard.visualNumber : "- noch nicht aktiviert - "}
          </div>
          <div className="">Bestellt am:</div>
          <div className="">{format(card.orderedAt, "dd.MM.yyyy")}</div>
          <div className="">Tarife:</div>
          <div
            className="truncate text-ellipsis hover:cursor-help"
            title={card.tarifs?.map((tarifs) => tarifs.tarif.name).join(" & ")}
          >
            {card.tarifs?.map((tarifs) => tarifs.tarif.name).join(" & ")}
          </div>

          {/* Eine Spalte, die sich innerhalb des Grids erstreckt und über beide Spalten geht */}
          <div className="col-span-2 flex w-full justify-center">
            {!card?.physicalCard && !loading && (
              <div className={"mt-3 flex w-full flex-row gap-1 text-center"}>
                <input
                  onChange={(e) => setCardNumber(e.target.value)}
                  type="text"
                  placeholder="Kartennummer"
                  className="block w-70/100 appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-gray-600"
                ></input>
                <Button onClick={() => changeActivation(true)}>Aktivieren </Button>
              </div>
            )}
            {loading && (
              <div className={"w-48"}>
                <Loading />
              </div>
            )}
          </div>
          {card?.physicalCard && !loading && card?.active && !card.deactivatedAt && (
            <div className={"w-full"}>Karte ist aktiv:</div>
          )}
          <div className="w-full">
            {card?.physicalCard && !loading && card?.active && !card.deactivatedAt && (
              <Button
                className={"hover:outline-gray bg-elm-950"}
                onClick={() => setShowDeactivateDialog(true)}
              >
                Deaktivieren{" "}
              </Button>
            )}
            {loading && (
              <div className={"w-48"}>
                <Loading />
              </div>
            )}
          </div>
          {card?.physicalCard && !card?.active && !card.activatedAt && (
            <div className={"col-span-2 mt-2 font-bold "}>
              Karte wird automatisch aktiv, sobald ein Zahlungmittel hinterlegt wurde.
            </div>
          )}

          {card?.physicalCard && !loading && card.deactivatedAt && (
            <>
              <div>Status:</div>
              <div
                className={
                  "max-w-20 rounded-full bg-gradient-to-tr from-slate-400 to-slate-600 text-center text-sm font-bold font-medium text-white"
                }
              >
                Deaktiviert
              </div>
              <div>Deaktiviert am:{format(card.deactivatedAt, "dd.MM.yyyy")}</div>
            </>
          )}
          {error && (
            <MessageDialog
              onNo={() => {}}
              noLabel={""}
              message={error}
              title={"Fehler"}
              onYes={() => setError("")}
              yesLabel={"Okay"}
            />
          )}
        </div>
        {showDeactivateDialog && (
          <MessageDialog
            message={`Möchten Sie ihre Ladekarte wirklich deaktivieren? 
               Deaktivierte Karten können nicht mehr aktiviert werden. 
               Das Laden mit der Ladekarte wird dann nicht mehr möglich sein.`}
            title={"Ladekarte deaktivieren"}
            onYes={() => changeActivation(false)}
            onNo={() => setShowDeactivateDialog(false)}
          ></MessageDialog>
        )}
      </div>
    )
  );
};

export default EMPCard;
