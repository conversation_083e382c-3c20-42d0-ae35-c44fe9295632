"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

import React from "react";

import { Role } from "@prisma/client";
import { ManagerCardPage } from "~/app/(app)/emp/card/component/ManagerCardPage";
import { UserCardPage } from "~/app/(app)/emp/card/component/UserCardPage";
import Card from "~/component/card";
import NotFound from "~/app/(app)/not-found";
import { AdminCardPage } from "~/app/(app)/emp/card/component/AdminCardPage";

const Page = async () => {
  const session = await getServerSession(authOptions);

  const renderContent = async () => {
    switch (session?.user?.role) {
      case Role.CARD_HOLDER:
        return <UserCardPage />;
      case Role.CARD_MANAGER:
        return <ManagerCardPage />;
      case Role.USER:
      case Role.ADMIN:
        return <AdminCardPage />;
      default:
        return <NotFound />;
    }
  };

  return <Card>{await renderContent()}</Card>;
};

export default Page;
