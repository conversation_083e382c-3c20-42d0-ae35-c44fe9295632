"use client";
import Button from "~/component/button";
import Card from "~/component/card";
import SweatAlert from "~/component/sweatAlert";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import React, { useEffect, useState } from "react";
import Modal from "~/component/modal/modal";
import Loading from "~/app/(app)/loading";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Headline from "~/component/Headline";
import PageContentWrapper from "~/component/PageContentWrapper";
import { CompanyTarif, Role } from "@prisma/client";
import StyledLink from "~/app/(app)/util/StyledLink";
import { FiLoader } from "react-icons/fi";
import NotFound from "~/app/(app)/not-found";

const NewInternalEMPCardPage = () => {
  const { data: session } = useSession();
  const [tariffsLoading, setTariffsLoading] = useState<boolean>(true);
  const [orderLoading, setOrderLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const router = useRouter();
  const [selectedCompanyTariffIds, setSelectedCompanyTariffIds] = useState<string[]>([]);
  const [companyTariffs, setCompanyTariffs] = useState<CompanyTarif[]>([]);

  const fetchCompanyTarrifs = async () => {
    try {
      const result = await fetch("/api/tarif/companyTarif?internal=1", {
        method: "GET",
      });
      if (result.ok) {
        const tarifs: CompanyTarif[] = await result.json();
        setCompanyTariffs(tarifs);
        setSelectedCompanyTariffIds(
          tarifs.filter((tarif) => !tarif.optional).map((tarif) => tarif.id),
        );
      }
    } catch (e) {
      setError("Fehler beim Laden der Tarife");
    }
    setTariffsLoading(false);
  };

  const sendOrderToServer = async () => {
    try {
      setOrderLoading(true);
      const result = await fetch("/api/emp/orderCard/internal", {
        method: "POST",
        body: JSON.stringify({ selectedTarifIds: selectedCompanyTariffIds }),
      });
      if (result.ok) {
        setTimeout(() => {
          setOrderLoading(false);
          router.push("/emp/card");
          router.refresh();
        }, 3000);
      }
    } catch (e) {
      setOrderLoading(false);
      setError("Fehler bei der Bestellung");
    }
  };

  useEffect(() => {
    void fetchCompanyTarrifs();
  }, []);

  if (session?.user?.role != Role.CARD_MANAGER && session?.user?.role !== Role.ADMIN) {
    return <NotFound />;
  }
  return tariffsLoading ? (
    <Loading />
  ) : (
    <PageContentWrapper>
      <Card>
        <Headline title={"Interne Ladekarte bestellen"} />
        {companyTariffs && companyTariffs.length > 0 && (
          <div className={"mb-3 md:max-w-1/2"}>
            Hier können Sie eine interne Ladekarte bestellen. Diese Ladekarte sind an einen internen
            Tarif gebunden. Ladevorgänge mit der Karte werden nicht abgerechnet. Der Tarif kann
            jedoch genutzt werden, um bei Bedarf die Kosten zu ermitteln.
          </div>
        )}
        {tariffsLoading && <Loading />}
        {!companyTariffs ||
          (companyTariffs.length == 0 && (
            <div className={"sm:max-w-120"}>
              <SweatAlert text={"Es wurde noch kein interner Tarif hinterlegt."} />
            </div>
          ))}
        <div className={"flex flex-col gap-4 sm:flex-row"}>
          {companyTariffs.map((tarif: CompanyTarif) => (
            <TarifCard
              blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
              blockingFeeMax={tarif.blockingFeeMax}
              oneTimeFeePayer={tarif.oneTimeFeePayer}
              vat={0} // internal
              internal={tarif.internal}
              blockingFee={tarif.blockingFee}
              size={"small"}
              oneTimeFee={tarif.oneTimeFee}
              basicFee={tarif.basicFee}
              currentType={tarif.currentType ?? ""}
              onCheck={(event) => {
                const { id, checked } = event.target;

                if (checked) {
                  // Füge die ID zum State hinzu, wenn sie noch nicht vorhanden ist
                  setSelectedCompanyTariffIds((prevIds) => [...prevIds, id]);
                } else {
                  // Entferne die ID aus dem State
                  setSelectedCompanyTariffIds((prevIds) =>
                    prevIds.filter((prevId) => prevId !== id),
                  );
                }
              }}
              key={tarif.id}
              tarifId={tarif.id}
              interactive={false}
              optional={tarif.optional}
              tarifName={`${tarif.name} (intern)` ?? ""}
              pricekWh={tarif.energyPrice}
              priceSession={tarif.sessionPrice}
              title={tarif.name ?? ""}
              description={tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"}
            />
          ))}
        </div>

        <Button
          onClick={() => sendOrderToServer()}
          className="md:max-w-1/3 block w-full rounded-full px-3 px-4 py-2  text-center font-bold hover:bg-blue-700 sm:max-w-1/2 lg:max-w-lg"
        >
          Zahlungspflichtig bestellen
          {orderLoading && <FiLoader className="ml-1 animate-spin text-blue-500" />}
        </Button>

        <Modal title={"Fehler"} isOpen={!!error} onClose={() => setError("")}>
          <span>{error}</span>
        </Modal>
      </Card>
    </PageContentWrapper>
  );
};

export default NewInternalEMPCardPage;
