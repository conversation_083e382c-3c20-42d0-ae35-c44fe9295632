import Card from "~/component/card";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import { z } from "zod";
import Headline from "~/component/Headline";

import { CompanyTarif, Prisma, Role, User } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import React from "react";
import NotFound from "~/app/(app)/not-found";
import { NextResponse } from "next/server";
import SweatAlert from "~/component/sweatAlert";
import StyledLink from "~/app/(app)/util/StyledLink";

const userDetailsSchema = z.object({
  name: z.string(),
  lastName: z.string(),
  email: z.string(),
  street: z.string(),
  streetNr: z.string(),
  city: z.string(),
  zip: z.string(),
  country: z.string(),
});

interface UserDetails {
  name: string;
  lastName: string;
  email: string;
  street?: string | null;
  streetNr?: string | null;
  city?: string | null;
  zip?: string | null;
  country?: string | null;
}

const getCompanyTariffs = async () => {
  const session = await getServerSession(authOptions);

  const user = session?.user;
  if (!session || !user) {
    return [];
  }

  const userOuid = user?.ou?.id;
  const companyTariffs = await prisma.companyTarif.findMany({
    where: {
      ouId: userOuid,
    },
  });

  return companyTariffs.filter((tarif) => !tarif.internal || user.role == Role.CARD_MANAGER);
};

const getUserCompanyTariffs = async () => {
  const session = await getServerSession(authOptions);
  const user = session?.user;

  if (user) {
    const userTariffs: CompanyTarif[] =
      await prisma?.$queryRawUnsafe(`select distinct CompanyTarif.*
        from EMPCard
                 left join CompanyTarifOnEMPCard on EMPCard.id = CompanyTarifOnEMPCard.empCardId
                 inner join CompanyTarif on CompanyTarifOnEMPCard.tarifId = CompanyTarif.id
        where userId = '${user.id}'`);
    return userTariffs;
  }
  return [];
};

const UserTarifPage = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return <NotFound />;
  }
  if (session && session?.user?.role != Role.CARD_HOLDER) {
    return <div>No valid role for this page</div>;
  }
  const userTariffs = await getUserCompanyTariffs();
  const possibleTariffs = await getCompanyTariffs();

  return (
    <>
      <Headline title={"Tarife"} />

      <Card
        className={"text-primary"}
        header_left={
          userTariffs.length > 0 ? "Diese Tarife wurden über eine Ladekarte von Ihnen gebucht:" : ""
        }
      >
        <div className={"flex flex-col gap-4 sm:flex-row"}>
          {possibleTariffs?.length == 0 && (
            <div
              className="border-primary relative mb-3  rounded-xl border bg-gray-100 px-4 py-3  text-primary sm:w-70/100"
              role="alert"
            >
              <span>Es wurden leider noch keine Tarife vom Betreiber hinterlegt.</span>
            </div>
          )}
          {userTariffs.length == 0 && possibleTariffs?.length > 0 && (
            <div className={"mb-4 max-w-100 "}>
              <SweatAlert text={"Es wurde noch keine Ladekarte mit einem Tarif verknüft."}>
                {session?.user?.selectedOu.registrationSlug}
                <StyledLink href={"/emp/payment/new"}>Zahlungsmittel hinterlegen</StyledLink>
              </SweatAlert>
            </div>
          )}

          {userTariffs &&
            userTariffs.map((tarif: CompanyTarif) => (
              <TarifCard
                internal={tarif.internal}
                blockingFee={tarif.blockingFee}
                blockingFeeMax={tarif.blockingFeeMax}
                blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                size={"normal"}
                oneTimeFeePayer={tarif.oneTimeFeePayer}
                vat={19}
                oneTimeFee={tarif.oneTimeFee}
                basicFee={tarif.basicFee}
                currentType={tarif.currentType ?? ""}
                key={tarif.id}
                tarifId={tarif.id}
                interactive={false}
                optional={tarif.optional}
                tarifName={tarif.name ?? ""}
                pricekWh={tarif.energyPrice}
                priceSession={tarif.sessionPrice}
                title={tarif.name ?? ""}
                description={tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"}
              />
            ))}
        </div>
        <div>
          {userTariffs.length == 0 && possibleTariffs.length > 0 && (
            <>
              <span>Folgende Tarife werden angeboten:</span>
              <div className={"flex flex-col gap-1 sm:flex-row sm:gap-4"}>
                {possibleTariffs.map((tarif: CompanyTarif) => (
                  <TarifCard
                    internal={tarif.internal}
                    vat={19}
                    blockingFee={tarif.blockingFee}
                    blockingFeeMax={tarif.blockingFeeMax}
                    blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                    size={"normal"}
                    oneTimeFeePayer={tarif.oneTimeFeePayer}
                    oneTimeFee={tarif.oneTimeFee}
                    basicFee={tarif.basicFee}
                    currentType={tarif.currentType ?? ""}
                    key={tarif.id}
                    tarifId={tarif.id}
                    interactive={false}
                    optional={tarif.optional}
                    tarifName={tarif.name ?? ""}
                    pricekWh={tarif.energyPrice}
                    priceSession={tarif.sessionPrice}
                    title={tarif.name ?? ""}
                    description={
                      tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"
                    }
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </Card>
    </>
  );
};

export default UserTarifPage;
