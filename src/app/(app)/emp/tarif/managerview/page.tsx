import Headline from "~/component/Headline";
import { CompanyTarif, Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { NewTariffButton } from "~/app/(app)/tarif/components/NewTariffButton";
import React from "react";

import NotFound from "~/app/(app)/not-found";
import Card from "~/component/card";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";

async function getCompanyTariffs(ouId: string) {
  const companyTariffs = await prisma.companyTarif.findMany({
    where: {
      ouId: ouId,
    },
  });
  return companyTariffs;
}

const ManagerTarifPage = async () => {
  const session = await getServerSession(authOptions);
  if (!session) {
    return <NotFound />;
  }
  if (session?.user?.role !== Role.CARD_MANAGER && session.user.role !== Role.ADMIN) {
    return <>Keine <PERSON></>;
  }
  let companyTariffs;
  if (session?.user.role == Role.ADMIN) {
    companyTariffs = await getCompanyTariffs(session?.user?.selectedOu?.id ?? "");
  } else {
    companyTariffs = await getCompanyTariffs(session?.user?.ou?.id ?? "");
  }

  return (
    <Card>
      <Headline title={"Firmentarife"} />
      <div className={" flex w-full flex-col-reverse justify-between gap-2 sm:flex-row"}>
        <div
          className="border-primary relative mb-3 flex-col rounded-xl border bg-gray-100 px-4 py-3 text-primary sm:w-70/100"
          role="alert"
        >
          {companyTariffs && companyTariffs.length == 0 && (
            <span className="">
              Hier können Sie neue Tarife erstellen, welche im Rahmen der Bestellung einer Ladekarte
              gebucht werden können. Alle Preisangaben sind netto.
            </span>
          )}
          {companyTariffs && companyTariffs.length == 1 && (
            <span>
              Zur Zeit kann lediglich 1 Tarif festgelegt werden. An der Bereitstellung mehrerer
              Tarife wird zur Zeit gearbeitet.
            </span>
          )}
        </div>

        {companyTariffs && companyTariffs.length == 0 && (
          <NewTariffButton label={"Neuer Firmentarif"} href={"/tarif/company/new"} />
        )}
      </div>

      {companyTariffs.filter((tarif) => !tarif.internal) &&
      companyTariffs.filter((tarif) => !tarif.internal).length > 0 ? (
        //<CompanyTariffTable data={companyTariffs} />
        <>
          <span>So sehen die Karteninhaber die angebotenen Tarife:</span>
          <div className={"flex flex-col gap-1 sm:flex-row sm:gap-4"}>
            {companyTariffs
              .filter((tarif) => !tarif.internal)
              .map((tarif: CompanyTarif) => (
                <TarifCard
                  blockingFee={tarif.blockingFee}
                  blockingFeeMax={tarif.blockingFeeMax}
                  blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                  size={"normal"}
                  oneTimeFeePayer={tarif.oneTimeFeePayer}
                  vat={19}
                  oneTimeFee={tarif.oneTimeFee}
                  basicFee={tarif.basicFee}
                  currentType={tarif.currentType ?? ""}
                  key={tarif.id}
                  tarifId={tarif.id}
                  interactive={false}
                  optional={tarif.optional}
                  tarifName={tarif.name ?? ""}
                  pricekWh={tarif.energyPrice}
                  priceSession={tarif.sessionPrice}
                  title={tarif.name ?? ""}
                  internal={tarif.internal}
                  description={
                    tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"
                  }
                />
              ))}
          </div>
        </>
      ) : (
        <div
          className="border-primary relative mb-3 flex-col rounded border bg-gray-100 px-4 py-3 text-primary sm:w-70/100"
          role="alert"
        >
          Es wurde noch kein Firmentarif hinzugefügt. Damit Mitarbeiter oder Club-Mitglieder eine
          Ladekarte bestellen oder aktivieren können, muss mindestens 1 Tarif festgelegt werden
        </div>
      )}

      {companyTariffs.filter((tarif) => tarif.internal)?.length > 0 && (
        //<CompanyTariffTable data={companyTariffs} />
        <>
          <span>
            <div
              className="border-primary relative mb-3 flex-col rounded border bg-gray-100 px-4 py-3 text-primary sm:w-70/100"
              role="alert"
            >
              Hier ihre internen Tarife, die nur von Managern eingesehen werden können und nicht
              abgerechnet werden:
            </div>
          </span>
          <div>
            {companyTariffs
              .filter((tarif) => tarif.internal)
              .map((tarif: CompanyTarif) => (
                <TarifCard
                  blockingFee={tarif.blockingFee}
                  blockingFeeMax={tarif.blockingFeeMax}
                  blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                  size={"small"}
                  oneTimeFeePayer={tarif.oneTimeFeePayer}
                  vat={0}
                  oneTimeFee={tarif.oneTimeFee}
                  basicFee={tarif.basicFee}
                  currentType={tarif.currentType ?? ""}
                  key={tarif.id}
                  tarifId={tarif.id}
                  interactive={false}
                  optional={tarif.optional}
                  tarifName={`${tarif.name} (intern)` ?? ""}
                  pricekWh={tarif.energyPrice}
                  priceSession={tarif.sessionPrice}
                  title={tarif.name ?? ""}
                  description={
                    tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"
                  }
                  internal={tarif.internal}
                />
              ))}
          </div>
        </>
      )}
    </Card>
  );
};

export default ManagerTarifPage;
