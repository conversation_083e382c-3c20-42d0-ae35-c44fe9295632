"use server";
import Card from "~/component/card";
import { getPaymentMethods } from "~/server/stripe/paymentUtils";
import PaymentCard from "~/app/(app)/emp/payment/components/PaymentCard";
import StyledLink from "~/app/(app)/util/StyledLink";
import React from "react";
import Headline from "~/component/Headline";
import { getEMPCardForUser } from "~/utils/user/empcard";
import { FaCheckCircle } from "react-icons/fa";

const Page = async () => {
  const paymentMethods = await getPaymentMethods();
  const cards = await getEMPCardForUser();
  const unactivatedPredeliveredExists = cards?.find(
    (card) => card.preDelivered && !card.activatedAt,
  );
  return (
    <>
      <Headline title={"Zahlungsdaten"} />

      {paymentMethods && paymentMethods?.length > 0 && (
        <>
          <Card className={"mb-4"}>
            <div className={"md:grid md:grid-cols-2 md:gap-4 lg:max-w-240"}>
              {paymentMethods.map((item, index) => (
                <PaymentCard item={item} key={index} />
              ))}
            </div>
          </Card>
          <div className={"mt-3 w-full sm:max-w-100"}>
            <StyledLink href={"/emp/payment/new"}>Weiteres Zahlungsmittel hinzufügen</StyledLink>
          </div>
        </>
      )}

      {paymentMethods && paymentMethods?.length == 0 && unactivatedPredeliveredExists && (
        <>
          <Card>
            <div className={"md:grid md:grid-cols-2 md:gap-4 lg:max-w-240"}>
              <span>
                Sie haben bei der Registrierung bereits eine Ladekarte hinterlegt, die Sie im
                Vorfeld erhalten haben. Um diese Karte nutzen zu können, müssen Sie ein
                Zahlungsmittel hinterlegen und ihr Benutzerprofil vervollständigen.
              </span>
            </div>
          </Card>
          <div className={"mt-3 w-full sm:max-w-100"}>
            <StyledLink href={"/emp/payment/newWithCard"}>Zahlungsmittel hinzufügen</StyledLink>
          </div>
        </>
      )}
      {paymentMethods && paymentMethods?.length == 0 && !unactivatedPredeliveredExists && (
        <>
          <Card>
            <div className={"md:grid md:grid-cols-2 md:gap-4 lg:max-w-240"}>
              <span>Sie haben noch kein Zahlungsmittel hinterlegt.</span>
            </div>
          </Card>
          <div className={"mt-3 w-full sm:max-w-100"}>
            <StyledLink href={"/emp/payment/new"}>Zahlungsmittel hinzufügen</StyledLink>
          </div>
        </>
      )}
    </>
  );
};

export default Page;
