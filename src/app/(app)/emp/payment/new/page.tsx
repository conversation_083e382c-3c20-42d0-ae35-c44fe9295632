"use client";
import React, { Suspense, useEffect, useState } from "react";
import { PaymentWrapper } from "~/app/(app)/emp/payment/components/PaymentWrapper";
import Headline from "~/component/Headline";

import { useForm } from "react-hook-form";
import { useSession } from "next-auth/react";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import Loading from "~/app/(app)/loading";
import { CompanyTarif } from "@prisma/client";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import { FaCheckCircle } from "react-icons/fa";

interface FormInputs {
  addressId: string;
  name: string;
  lastName: string;
  street: string;
  streetNr: string;
  city: string;
  zip: string;
  country: string;
  companyName: string;
}
const Page = () => {
  const [clientSecret, setClientSecret] = useState<string>();

  const createPaymentIntent = async () => {
    const result = await fetch("/api/stripe/setupSepaDebit", {
      method: "GET",
    });
    //profil form ausblenden
    setProfileUpdated(true);
    setClientSecret((await result.json()).clientSecret);
  };
  const [tariffs, setTariffs] = useState<CompanyTarif[]>([]);
  const [profileUpdated, setProfileUpdated] = useState<boolean>(false);
  const { data: session, update: updateSession } = useSession();
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting, isDirty, isSubmitSuccessful, isLoading },
  } = useForm<FormInputs>({ defaultValues: { addressId: "" } });

  const fetchTarifs = async () => {
    const response = await fetch(`/api/tarif/companyTarif`, {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    });
    console.log(response);
    if (response.ok) {
      const tarifs = await response.json();

      setTariffs(tarifs);
    } else {
      console.log(response);
    }
  };
  useEffect(() => {
    void fetchTarifs();
  }, []);

  useEffect(() => {
    if (session?.user?.lastName) {
      createPaymentIntent();
    }
  }, [session?.user]);
  const onSubmit = async (data: FormInputs) => {
    try {
      const response = await fetch("/api/user/updateProfile", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data }),
      });
      if (!response.ok) {
        setErrorMessage(await response.text());
        setSuccessMessage("");
      } else {
        //session daten setzen, damit in der nächsten form die eingegebenen Daten direkt verwendet werden
        void updateSession({ name: watch("name"), lastName: watch("lastName") });

        // zeige Sepa Felder
        void createPaymentIntent();

        //verstecke userform
        setProfileUpdated(true);

        //falls vorher ein fehler war, lösche den
        setErrorMessage("");
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
        setSuccessMessage("");
      } else {
        setErrorMessage("Fehler beim Speichern");
        setSuccessMessage("");
      }
    }
  };

  return (
    <>
      {!session?.user?.email && <Loading />}

      {!profileUpdated && session?.user?.email && (
        <div className={"w-full lg:w-2/3 xl:w-1/2"}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className={`w-full space-y-4  rounded-lg  bg-white transition-all`}
          >
            <div className="flex flex-col gap-2">
              <div className="">
                <label className="block text-sm font-medium">Email</label>
                <input
                  value={session?.user?.email}
                  disabled={true}
                  readOnly={true}
                  className="disabled bg-eul-lightgray block w-full appearance-none rounded-lg
              border border-solid border-gray-300
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline
              focus:outline-none"
                />
              </div>
              <div className={""}>
                <label className="block text-sm font-medium">
                  Firmenname{" "}
                  <span className={"text-xs text-gray-600"}>
                    (wird auf der Rechnung verwendet, wenn angegeben):
                  </span>
                </label>
                <input
                  placeholder={"optional zur Verwendung auf Rechnungen"}
                  {...register("companyName")}
                  className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                />
                {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
              </div>

              <>
                <div className={"flex  w-full flex-row gap-2"}>
                  <div className={"w-full "}>
                    <label className="block text-sm font-medium">Vorname*</label>
                    <input
                      {...register("name", { required: "Bitte einen Vornamen eingeben" })}
                      className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                    />
                    {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
                  </div>
                  <div className="w-full">
                    <label className="block text-sm font-medium">Nachname*</label>
                    <input
                      {...register("lastName", { required: "Bitte einen Nachnamen eingeben" })}
                      className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                    />
                    {errors.lastName && <p className={"text-red-500"}>{errors.lastName.message}</p>}
                  </div>
                </div>

                <>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Land*</label>
                      <select
                        {...register("country", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      >
                        <option value="Deutschland">Deutschland</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">PLZ*</label>
                      <input
                        {...register("zip", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Ort*</label>
                      <input
                        {...register("city", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2 md:col-span-2">
                      <label className="block text-sm font-medium">Straße*</label>
                      <input
                        {...register("street", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Hausnummer*</label>
                      <input
                        {...register("streetNr", { required: true })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                    </div>
                  </div>
                </>
              </>

              <Button
                disabled={(isSubmitting || isSubmitSuccessful) && !isDirty}
                className={"w-full sm:max-w-64"}
                type="submit"
              >
                Weiter {isSubmitting ? <FiLoader className="animate-spin" /> : ""}
              </Button>
            </div>
          </form>
          {successMessage && <span className={"text-green-600"}>{successMessage}</span>}
          {errorMessage && <span className={"text-red-600"}>{errorMessage}</span>}
        </div>
      )}

      {!clientSecret && <Loading />}
      {clientSecret && (
        <>
          <div className={"flex min-h-[75vh] flex-col"}>
            <Headline title={"Lastschriftmandat hinzufügen"} />

            <div className={"flex flex-col gap-5 rounded-xl bg-clip-border p-4 shadow-2xl"}>
              <span>
                Zum aktuellen Zeitpunkt unterstützen wir das Hinzufügen eines SEPA
                Lastschrift-Mandates zur monatlichen Abrechnung. Bitte nutze das folgende Formular,
                um ein Lastschriftmandat hinzuzufügen, sodass eine Ladekarte bestellt und aktiviert
                werden kann.
              </span>

              <PaymentWrapper clientSecret={clientSecret ?? ""} successUrl={"/emp/payment"} />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default Page;
