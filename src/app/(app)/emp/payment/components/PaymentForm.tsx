"use client";
import { IbanElement, PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import React, { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { StripeIbanElementChangeEvent } from "@stripe/stripe-js";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";

interface Props {
  clientSecret: string;
  successUrl: string;
  selectedTariffIds?: string[];
}

interface FormData {
  email: string;
  firstName: string;
  lastName: string;
  companyName: string;
}

export const PaymentForm = ({
  clientSecret: clientSecret,
  successUrl: successUrl,
  selectedTariffIds,
}: Props) => {
  const stripe = useStripe();
  const elements = useElements();
  const { data: session } = useSession();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitted, isSubmitSuccessful },
  } = useForm<FormData>({
    defaultValues: {
      lastName: session?.user.lastName ?? "",
      firstName: session?.user.name ?? "",
      companyName: session?.user?.companyName ?? "",
    },
  });

  const [errorMessageIban, setErrorMessageIban] = useState("");
  const [sepaErrorResult, setSepaErrorResult] = useState<string>("");
  const [sepaSuccess, setSepaSuccess] = useState<boolean>(false);
  const [disabled, setDisabled] = useState(true);
  const router = useRouter();
  const [ibanComplete, setIbanComplete] = useState<boolean>(false);

  useEffect(() => {
    setDisabled(!stripe || Object.keys(errors).length > 0 || errorMessageIban.length > 0);
  }, [stripe, errorMessageIban, errors]);

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    const iban = elements.getElement(IbanElement);
    if (iban) {
      const result = await stripe.confirmSepaDebitSetup(clientSecret, {
        payment_method: {
          sepa_debit: iban,
          billing_details: {
            name: `${data.companyName}${data.companyName ? " " : ""}${data.firstName} ${
              data.lastName
            }`,
            email: data.email,
          },
        },
      });

      if (result.error) {
        // Show error to your customer.

        setSepaErrorResult(result.error.message ?? "Fehler beim erteilen des Lastschriftmandates");
        return false;
      } else {
        setSepaErrorResult("");
        const response = await fetch(
          `/api/stripe/setDefaultPaymentMethod?paymentMethodId=${result.setupIntent.payment_method}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          },
        );

        if (response.ok && successUrl) {
          setSepaSuccess(true);

          // Create admin notification for SEPA mandate setup
          try {
            await fetch('/api/admin/notifications/send', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                message: `SEPA-Lastschriftmandat hinterlegt von ${data.firstName} ${data.lastName}`,
                type: 'INFO',
                targetType: 'role',
                targetId: 'ADMIN',
              }),
            });
          } catch (error) {
            console.error('Failed to send admin notification:', error);
          }

          setTimeout(() => {
            router.push(successUrl);
            router.refresh();
          }, 4000);
        }
      }
    }
  };
  const handleIbanChange = (event: StripeIbanElementChangeEvent) => {
    if (event.error) {
      setErrorMessageIban(event.error.message);
    } else {
      setErrorMessageIban(""); // clear any existing error message
      setSepaErrorResult("");
    }
    setIbanComplete(event.complete);
  };

  return (
    <form id={"payment-form"} onSubmit={handleSubmit(onSubmit)}>
      <div>
        <IbanElement
          onChange={handleIbanChange}
          className="w-full rounded-xl border-[1px] border-gray-300 bg-white px-1 py-3"
          options={{ supportedCountries: ["SEPA"] }}
        />
        {errorMessageIban && <span className="text-red-500">{errorMessageIban}</span>}
      </div>

      <div className="mt-5 flex flex-col gap-3 md:flex-row md:space-x-3">
        <div className="md:w-1/3">
          <label className="block text-sm font-medium">Vorname:</label>
          <input
            placeholder={"Vorname"}
            defaultValue={session?.user?.name ?? ""}
            {...register("firstName", { required: true })}
            className="w-full rounded-xl border-[1px] border-gray-300 p-2"
          />
          {errors.firstName && <span className="text-red-500">Dieses Feld ist erforderlich</span>}
        </div>

        <div className="md:w-1/3">
          <label className="block text-sm font-medium">Nachname:</label>
          <input
            placeholder={"Nachname"}
            defaultValue={session?.user?.lastName ?? ""}
            {...register("lastName", { required: true })}
            className="w-full rounded-xl border-[1px] border-gray-300 p-2"
          />
          {errors.lastName && <span className="text-red-500">Dieses Feld ist erforderlich</span>}
        </div>

        {session?.user?.companyName && (
          <div className="md:w-1/3">
            <label className="block text-sm font-medium">Firmenname:</label>
            <input
              placeholder={"Firmenname"}
              defaultValue={session?.user?.companyName ?? ""}
              {...register("companyName")}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2"
            />
          </div>
        )}

        <div className="md:w-1/3">
          <label className="block text-sm font-medium">Email:</label>
          <input
            placeholder={"E-Mail"}
            defaultValue={session?.user.email}
            readOnly={true}
            {...register("email", {
              required: true,
              pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
            })}
            className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200"
          />
          {errors.email && (
            <span className="text-red-500">Geben Sie eine gültige E-Mail-Adresse ein</span>
          )}
        </div>
      </div>
      <div className={"mt-4 block text-eul-gray"}>
        *Durch Angabe Ihrer Zahlungsinformationen und der Bestätigung der vorliegenden Zahlung
        ermächtigen Sie (A) Eulektro GmbH und Stripe, unseren Zahlungsdienstleister, Ihrem
        Kreditinstitut Anweisungen zur Belastung Ihres Kontos zu erteilen, und (B) Ihr
        Kreditinstitut, Ihr Konto gemäß diesen Anweisungen zu belasten. Im Rahmen Ihrer Rechte haben
        Sie, entsprechend den Vertragsbedingungen mit Ihrem Kreditinstitut, Anspruch auf eine
        Rückerstattung von Ihrem Kreditinstitut. Eine Rückerstattung muss innerhalb von 8 Wochen ab
        dem Tag, an dem Ihr Konto belastet wurde, geltend gemacht werden. Eine Erläuterung Ihrer
        Rechte können Sie von Ihrem Kreditinstitut anfordern. Sie erklären sich einverstanden,
        Benachrichtigungen über künftige Belastungen bis spätestens 2 Tage vor dem Buchungsdatum zu
        erhalten.
      </div>

      <div
        className={
          "sm:max-w-150 mt-3 flex w-full flex-col-reverse items-center justify-center sm:flex-row sm:justify-start"
        }
      >
        {!successUrl && isSubmitSuccessful ? (
          <></>
        ) : (
          <Button
            title={
              ibanComplete ? "Bestätigen" : "Bitte dem Bestätigen wird eine gültige Iban benötigt"
            }
            disabled={isSubmitting || !ibanComplete}
            className={"w-full sm:max-w-100"}
          >
            {isSubmitting || isSubmitSuccessful || sepaSuccess ? (
              <>
                {" "}
                prüfen.. <FiLoader className="animate-spin" />
              </>
            ) : (
              "Bestätigen"
            )}
          </Button>
        )}
        {sepaSuccess && (
          <div
            className="relative ml-3 rounded border border-green-400 bg-green-100 px-4 py-2 text-green-700"
            role="alert"
          >
            <strong className="font-bold">Erfolg! </strong>
            <span className="block sm:inline">
              {`Lastschriftmandat erfolgreich hinterlegt. ${
                successUrl && "Sie werden weitergeleitet..."
              }`}
            </span>
          </div>
        )}

        <span className={"ml-2 text-red-500"}> {sepaErrorResult}</span>
      </div>
    </form>
  );
};
