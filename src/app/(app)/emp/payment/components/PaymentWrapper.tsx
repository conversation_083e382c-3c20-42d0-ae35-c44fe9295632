"use client";
import { Elements } from "@stripe/react-stripe-js";
import getStripe from "~/stripe/get-stripejs";
import { PaymentForm } from "~/app/(app)/emp/payment/components/PaymentForm";

import { useSession } from "next-auth/react";

interface Props {
  clientSecret: string;
  successUrl: string;
  selectedTariffIds?: string[];
}

const stripePromise = getStripe();
export const PaymentWrapper = ({ clientSecret: clientSecret, successUrl: successUrl, selectedTariffIds }: Props) => {
  const { data: session } = useSession();
  return (
    <>
      {clientSecret && stripePromise && (
        <Elements
          stripe={stripePromise}
          options={{
            locale: "de",
            clientSecret: clientSecret,
            appearance: {
              theme: "flat",
              variables: {
                colorText: "#21697C",
                fontFamily: "Source Sans Pro",
              },
            },
          }}
        >
          {session?.user && (
            <>
              <PaymentForm successUrl={successUrl} clientSecret={clientSecret} selectedTariffIds={selectedTariffIds} />
            </>
          )}
        </Elements>
      )}
    </>
  );
};
