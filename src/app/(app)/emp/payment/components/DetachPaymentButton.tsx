"use client";

import { useState } from "react";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import { useRouter } from "next/navigation";
import Modal from "~/component/modal/modal";
import Button from "~/component/button";
import { FaTrash } from "react-icons/fa";

type DetachPaymentButtonProps = {
  paymentMethodId: string;
};

const DetachPaymentButton = ({ paymentMethodId }: DetachPaymentButtonProps) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const router = useRouter();
  const [error, setError] = useState<string>("");

  const detachPaymentMethodFromUser = async () => {
    try {
      const response = await fetch(
        `/api/stripe/detachPaymentMethodFromCustomer?paymentMethodId=${paymentMethodId}`,
      ); // Ersetzen Sie 'YOUR_USER_ID_HERE' durch die entsprechende ID.
      if (response.ok) {
        router.refresh();
        setError("");
      } else {
        setError((await response.json())?.message ?? "Unbekannter Fehler");
      }
    } catch (err) {
      const message = (err as Error).message;
      setError(message);
    } finally {
      setShowConfirmDialog(false);
    }
  };

  return (
    <>
      <Button title="Zahlungsmittel entfernen" onClick={() => setShowConfirmDialog(true)}>
        <FaTrash className="bg-white" />
      </Button>

      {showConfirmDialog && (
        <MessageDialog
          onNo={() => setShowConfirmDialog(false)}
          message={"Wollen Sie das Zahlungsmittel wirklich entfernen?"}
          title={"Zahlungsmittel entfernen"}
          onYes={() => detachPaymentMethodFromUser()}
        ></MessageDialog>
      )}

      <Modal title={"Fehler"} isOpen={!!error} onClose={() => setError("")}>
        <span>{error}</span>
      </Modal>
    </>
  );
};

export default DetachPaymentButton;
