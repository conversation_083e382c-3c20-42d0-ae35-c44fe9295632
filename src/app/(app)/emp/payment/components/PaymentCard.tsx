import { MdAccountBalance, MdPayment } from "react-icons/md";
import Button from "~/component/button";
import Loading from "~/app/(app)/loading";
import React from "react";
import card from "~/component/card";
import { className } from "postcss-selector-parser";
import SetDefaultPaymentButton from "~/app/(app)/emp/payment/components/SetDefaultButton";

interface PaymentData {
  name: string | null;
  email: string | null;
  last4: string | null | undefined;
  default: boolean;
  id: string;
}

const PaymentCard = ({ item }: { item: false | PaymentData }) => {
  return (
    <div
      className={`relative mb-3 flex h-48 min-w-0 max-w-100 flex-col break-words rounded-md border-0.4 bg-clip-border text-white shadow-soft-xl  ${className}`}
    >
      <MdAccountBalance className="absolute right-0 top-0 m-2 text-primary" size={20} />{" "}
      {/* MdPayment-Icon hinzufügen */}
      {item && (
        <div className="grid grid-cols-[1fr,2fr] gap-1 p-4 text-eul-gray">
          <div className="font-semibold">Name:</div>
          <div className={"truncate"} title={item.name ?? ""}>
            {item.name}
          </div>
          <div className="font-semibold">Email:</div>
          <div className="truncate" title={item.email ?? ""}>
            {item.email}
          </div>
          <div className="font-semibold">Kontonummer:</div>
          <div>xxx....{item.last4}</div>
          {item.default ? (
            <>
              <div className="font-semibold">Status:</div>
              <p
                title={"Dieses Zahlungsmittel wird bei anstehenden Rechnungen belastet"}
                className={
                  "max-w-20 rounded-full bg-green-200 text-center text-sm font-medium hover:cursor-help"
                }
              >
                Standard
              </p>
            </>
          ) : (
            <div className={"col-span-2 mt-3 flex justify-center"}>
              <SetDefaultPaymentButton paymentMethodId={item.id} />
            </div>
          )}

          {/* Eine Spalte, die sich innerhalb des Grids erstreckt und über beide Spalten geht */}
        </div>
      )}
    </div>
  );
};

export default PaymentCard;
