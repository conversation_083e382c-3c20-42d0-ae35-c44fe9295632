"use client";
import { FaArrowUp } from "react-icons/fa";
import { useState } from "react";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import { useRouter } from "next/navigation";
import Loading from "~/app/(app)/loading";
import Modal from "~/component/modal/modal";
import Button from "~/component/button";

type SetDefaultPaymentButtonProps = {
  paymentMethodId: string;
};

const SetDefaultPaymentButton = ({ paymentMethodId }: SetDefaultPaymentButtonProps) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const router = useRouter();
  const [error, setError] = useState<string>("");

  const setDefaultPaymentMethod = async () => {
    try {
      const response = await fetch(
        `/api/stripe/setDefaultPaymentMethod?paymentMethodId=${paymentMethodId}`,
      ); // Ersetzen Sie 'YOUR_USER_ID_HERE' durch die entsprechende ID.
      if (response.ok) {
        router.refresh(); //fetch upated server side page
        setError("");
      } else {
        setError((await response.json())?.message ?? "Unbekannter Fehler");
      }
    } catch (err) {
      const message = (err as Error).message;
      setError(message);
    } finally {
      setShowConfirmDialog(false);
    }
  };

  return (
    <>
      <Button
        title="Zahlungsmittel als Standard hinterlegen"
        className={"bg-gray-600 leading-none hover:bg-gray-700"}
        onClick={() => setShowConfirmDialog(true)}
      >
        als Standard setzen{" "}
      </Button>

      {showConfirmDialog && (
        <MessageDialog
          onNo={() => setShowConfirmDialog(false)}
          message={`Wollen Sie das Zahlungsmittel wirklich als neuen Standard setzen?
            Eine fällige Rechnung wird immer vom aktuellen Standard-Zahlungsmittel abgebucht.`}
          title={"Zahlungsmittel Standard ändern"}
          onYes={() => setDefaultPaymentMethod()}
        ></MessageDialog>
      )}

      <Modal title={"Fehler"} isOpen={!!error} onClose={() => setError("")}>
        <span>{error}</span>
      </Modal>
    </>
  );
};

export default SetDefaultPaymentButton;
