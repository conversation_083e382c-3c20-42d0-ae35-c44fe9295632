"use server";

import Card from "~/component/card";

import { InvoiceDataWrapper } from "~/app/(app)/invoice/component/InvoiceDataWrapper";
import React from "react";
import { UserInvoiceSelect } from "~/app/(app)/invoice/component/roamingInvoiceSelect";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice } from "@prisma/client";
import Headline from "~/component/Headline";
import NotFound from "~/app/(app)/not-found";
import prisma from "~/server/db/prisma";
import { ManagerInvoicesTable } from "~/app/(app)/emp/invoice/component/ManagerInvoicesTable";

const getCPOInvoices = async () => {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return [];
  }

  const invoices = await prisma?.invoice.findMany({
    where: {
      contact: { ou: { id: session.user.ou.id } },
      stateOfInvoice: { not: StateOfInvoice.PREVIEW },
    },
    include: { files: true },
  });
  return invoices ?? [];
};

const Page = async () => {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.CARD_MANAGER) {
    return <NotFound />;
  }
  const invoices = await getCPOInvoices();
  return (
    <Card>
      <Headline title={"Rechnungen"} />
      <ManagerInvoicesTable invoices={invoices} />
    </Card>
  );
};

export default Page;
