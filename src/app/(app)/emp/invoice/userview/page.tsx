"use server";
import Card from "~/component/card";
import prisma from "~/server/db/prisma";
import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice } from "@prisma/client";
import Headline from "~/component/Headline";
import { UserInvoicesTable } from "~/app/(app)/emp/invoice/component/UserInvoicesTable";
import NotFound from "~/app/(app)/not-found";

const getUserInvoices = async () => {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return [];
  }

  const invoices = await prisma?.invoice.findMany({
    where: { userId: session.user.id, stateOfInvoice: { not: StateOfInvoice.PREVIEW } },
    include: { paymentIntent: true, files: true },
  });
  return invoices ?? [];
};

const Page = async () => {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.CARD_HOLDER) {
    return <NotFound />;
  }
  const invoices = await getUserInvoices();

  return (
    <>
      <Headline title={"Rechnungen"} />

      <Card>
        <UserInvoicesTable invoices={invoices} />
      </Card>
    </>
  );
};

export default Page;
