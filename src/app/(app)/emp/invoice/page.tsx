"use server";

import Card from "~/component/card";

import { InvoiceDataWrapper } from "~/app/(app)/invoice/component/InvoiceDataWrapper";
import React from "react";
import { UserInvoiceSelect } from "~/app/(app)/invoice/component/roamingInvoiceSelect";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";

const Page = async () => {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.ADMIN) {
    return <NotFound />;
  }
  return (
    <>
      <h1 className={"mb-0 font-bold dark:text-white"}>Mitarbeiter/Clubmitglieder Rechnungen</h1>
      <div className="mb-3 flex justify-end">
        <UserInvoiceSelect />
      </div>
      <Card>
        <InvoiceDataWrapper filterCardHolder={true} />
      </Card>
    </>
  );
};

export default Page;
