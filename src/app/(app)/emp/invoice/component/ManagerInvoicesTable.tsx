"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaDownload } from "react-icons/fa";
import React from "react";
import { Invoice } from "@prisma/client";
import Table, { filterParams } from "~/utils/table/table";
import {
  dateRenderer,
  twoDecimalPlacesFormatter,
  twoDecimalPlacesFormatterWithCurrency,
} from "~/utils/table/formatter";
import type { BaseWithValueColDefParams } from "ag-grid-community/dist/lib/entities/colDef";
import Loading from "~/app/(app)/loading";
import { ImCross } from "react-icons/im";
import { env } from "~/env";

export const ManagerInvoicesTable = ({ invoices }: { invoices: Invoice[] }) => {
  const downloadInvoice = async (data: any) => {
    // Finden Sie die erste PDF-Datei
    const pdfFile = data.files.find((file: any) => file.contentType === "application/pdf");

    if (!pdfFile) {
      alert("Keine PDF-Datei gefunden");
      return;
    }

    // URL zu Ihrem API-Endpunkt
    const url = `/api/download/${pdfFile.id}`;

    // Laden Sie die Datei herunter
    const response = await fetch(url);
    if (response.ok) {
      const blob = await response.blob();

      // Erstellen Sie einen Link und klicken Sie darauf, um den Download zu starten
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${env.NEXT_PUBLIC_DEBUG == "true" ? "_DEV_" : ""}${pdfFile.name}`;
      link.click();

      // URL freigeben
      URL.revokeObjectURL(downloadUrl);
    } else {
      alert(`Fehler beim Download -> ${response.statusText}`);
    }
  };

  const columnDefs = [
    // { headerName: "Monat", field: "invoiceDate", cellRenderer: monthYearRenderer },
    { headerName: "RechnungsNr", field: "invoiceNumber" },

    {
      field: "invoiceDate",
      headerName: "Rechnungsdatum",
      cellRenderer: dateRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
    },

    {
      field: "sumGross",
      headerName: "Summe",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },

    {
      headerName: "Action",
      field: "paymentIntent.status",
      filter: "agSetColumnFilter",
      cellRenderer: (params: BaseWithValueColDefParams) => {
        return (
          <FaDownload
            className={"ml-5 cursor-pointer"}
            title={"Download Rechnung"}
            onClick={() => downloadInvoice(params?.data)}
          />
        );
      },
    },
  ];

  return !invoices ? (
    <Loading />
  ) : (
    <Table gridId={"managerInvoice"} columnDefs={columnDefs} rowData={invoices} />
  );
};
