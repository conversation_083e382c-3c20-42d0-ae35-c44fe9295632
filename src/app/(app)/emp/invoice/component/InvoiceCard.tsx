"use client";
import { BsLightningCharge } from "react-icons/bs";
import type { Invoice, PhysicalCard } from "@prisma/client";
import { EMPCard } from "@prisma/client";
import React, { useState } from "react";
import Button from "~/component/button";
import Loading from "~/app/(app)/loading";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import Modal from "~/component/modal/modal";
import { getMonthNameInLocale } from "~/utils/date/date";

const InvoiceCard = ({ invoice }: { invoice: Invoice }) => {
  const router = useRouter();

  return (
    <div
      className={` bg-elm-100  relative mb-3 flex h-48 min-w-60 max-w-80 flex-col break-words rounded-xl border-0.4 bg-clip-border shadow-soft-xl`}
    >
      <BsLightningCharge className="absolute right-0 top-0 m-3" size={24} />{" "}
      {/* MdPayment-Icon hinzufügen */}
      <div>
        <span>
          {invoice.servicePeriodFrom ? getMonthNameInLocale(invoice.servicePeriodFrom) : "monat"}
        </span>
      </div>
    </div>
  );
};

export default InvoiceCard;
