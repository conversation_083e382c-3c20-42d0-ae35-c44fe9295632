"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaDownload } from "react-icons/fa";
import React from "react";
import { Invoice } from "@prisma/client";
import Table, { filterParams } from "~/utils/table/table";
import {
  dateRenderer,
  twoDecimalPlacesFormatter,
  twoDecimalPlacesFormatterWithCurrency,
} from "~/utils/table/formatter";
import type { BaseWithValueColDefParams } from "ag-grid-community/dist/lib/entities/colDef";
import Loading from "~/app/(app)/loading";
import { ImCross } from "react-icons/im";
import { env } from "~/env";

export const UserInvoicesTable = ({ invoices }: { invoices: Invoice[] }) => {
  const downloadInvoice = async (data: any) => {
    // Finden Sie die erste PDF-Datei
    const pdfFile = data.files.find((file: any) => file.contentType === "application/pdf");

    if (!pdfFile) {
      alert("Keine PDF-Datei gefunden");
      return;
    }

    // URL zu Ihrem API-Endpunkt
    const url = `/api/download/${pdfFile.id}`;

    // Laden Sie die Datei herunter
    const response = await fetch(url);
    if (response.ok) {
      const blob = await response.blob();

      // Erstellen Sie einen Link und klicken Sie darauf, um den Download zu starten
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${env.NEXT_PUBLIC_DEBUG == "true" ? "_DEV_" : ""}${pdfFile.name}`;
      link.click();

      // URL freigeben
      URL.revokeObjectURL(downloadUrl);
    } else {
      alert(`Fehler beim Download -> ${response.statusText}`);
    }
  };

  const columnDefs = [
    // { headerName: "Monat", field: "invoiceDate", cellRenderer: monthYearRenderer },
    { headerName: "RechnungsNr", field: "invoiceNumber" },

    {
      field: "invoiceDate",
      headerName: "Rechnungsdatum",
      cellRenderer: dateRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
    },

    {
      field: "sumGross",
      headerName: "Summe",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "sumKwh",
      headerName: "kWh",
      aggFunc: "sum",
      enableValue: true,
      cellRenderer: twoDecimalPlacesFormatter,
    },
    {
      field: "sumSession",
      headerName: "Ladevorgänge",
      aggFunc: "sum",
      enableValue: true,
    },
    {
      headerName: "Lastschrift Status",
      field: "paymentIntent.status",
      filter: "agSetColumnFilter",
      cellRenderer: (params: BaseWithValueColDefParams) => {
        if (params?.data?.paymentIntent) {
          if (params?.data?.paymentIntent.status == "succeeded") {
            return (
              <>
                <FaCheck title={"Erfolgreich"} size={18} className={"text-green-500"} />{" "}
                <span>Erfolgreich</span>
              </>
            );
          }
          if (params?.data?.paymentIntent.status == "processing") {
            return (
              <>
                <FaClock title={"in bearbeitung"} size={18} className={"mr-1 text-gray-500"} />
                <span>in Bearbeitung</span>
              </>
            );
          }
          if (params?.data?.paymentIntent.status == "failed") {
            return (
              <>
                <ImCross title={"fehlgeschlagen"} size={18} className={"mr-1 text-red-500"} />
                <span>Fehlgeschlagen</span>
              </>
            );
          }
        } else {
          return "Noch kein Einzugsversuch"; // sollte nie sicharbar sein, denn nur rechnung mit
          //payment intent sollten hier in der tabelle sein
        }
      },
    },
    {
      headerName: "Actions",
      field: "paymentIntent.status",
      filter: "agSetColumnFilter",
      cellRenderer: (params: BaseWithValueColDefParams) => {
        return (
          <FaDownload
            title={"Download Rechnung"}
            className={"ml-5 cursor-pointer"}
            onClick={() => downloadInvoice(params?.data)}
          />
        );
      },
    },
  ];

  return !invoices ? (
    <Loading />
  ) : (
    <Table gridId={"userInvoice"} columnDefs={columnDefs} rowData={invoices} />
  );
};
