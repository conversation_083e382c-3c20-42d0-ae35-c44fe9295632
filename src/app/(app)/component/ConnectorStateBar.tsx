import React, { useState } from "react";

type WorkloadData = {
  Available: number;
  Charging: number;
  SuspendedEV: number;
  Preparing: number;
  SuspendedEVSE: number;
  Finishing: number;
  Unavailable: number;
  Faulted: number;
};

interface WorkloadPieChartProps {
  data: WorkloadData;
}

const ConnectorStateBar: React.FC<WorkloadPieChartProps> = ({ data }) => {
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const [tooltipValue, setTooltipValue] = useState<string>("");

  const total: number | typeof NaN =
    data.Available +
    data.Charging +
    data.SuspendedEV +
    data.Preparing +
    data.SuspendedEVSE +
    data.Finishing +
    data.Faulted +
    data.Unavailable;

  const colorMapping: Record<keyof WorkloadData, string> = {
    Available: "bg-green-300", // freundliches, etwas wärmeres Grün
    Charging: "bg-yellow-300", // gleichmäßiges Gelb
    SuspendedEV: "bg-orange-300", // identisch mit EVSE für Einheitlichkeit
    Preparing: "bg-gray-300", // neutraleres Grau
    SuspendedEVSE: "bg-orange-400", // gleich wie SuspendedEV für Klarheit
    Finishing: "bg-gray-200", // heller als Preparing → visuelle Abgrenzung
    Unavailable: "bg-red-300", // etwas heller als vorher, weniger aggressiv
    Faulted: "bg-red-400",
  };

  return Number.isNaN(total) ? (
    <h5 className={"font-bold dark:text-white"}>Loading...</h5>
  ) : (
    <>
      <div className="md-shadow-all flex h-5 w-full overflow-hidden rounded-xl text-center ">
        {Object.entries(data).map(([key, value]) => {
          const widthPercent = total > 0 ? Math.round((value / total) * 100) : 0;
          if (widthPercent <= 0) return null;
          const widthStyle = `${widthPercent}%`;
          return (
            <div
              key={key}
              onMouseOver={() => {
                setTooltipValue(`${key}: ${value}`);
                setShowTooltip(true);
              }}
              onMouseLeave={() => {
                setTooltipValue("");
                setShowTooltip(false);
              }}
              onTouchStart={() => {
                setTooltipValue(`${key}: ${value}`);
                setShowTooltip(true);
              }}
              onTouchEnd={() => {
                setShowTooltip(false);
              }}
              style={{ width: widthStyle, minWidth: "10px" }}
              className={`${
                colorMapping[key as keyof WorkloadData]
              } text-sm font-bold hover:cursor-pointer hover:brightness-90`}
              title={`${key}: ${value}`}
            >
              {value}
            </div>
          );
        })}
      </div>
      {showTooltip && (
        <div className="absolute bottom-1/2 left-1/2 mt-2 -translate-x-1/2 rounded px-3 py-2 text-sm font-bold text-primary">
          {tooltipValue}
        </div>
      )}
    </>
  );
};
export default ConnectorStateBar;
