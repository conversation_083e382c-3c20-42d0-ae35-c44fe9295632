import { TouchEvent, useEffect, useState } from "react";
import ReactECharts from "echarts-for-react";
import { userStore } from "~/server/zustand/store";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { getDateLabel } from "~/utils/date/date";

interface StockPriceShort {
  power: number;
  amount: number;
  hour: number;
}

const StockPriceChart = () => {
  const [stockPricesToday, setStockPricesToday] = useState<StockPriceShort[]>([]);
  const [minPrice, setMinPrice] = useState<number>(-1);
  const [maxPrice, setMaxPrice] = useState<number>(15);
  const [requestedDate, setRequestedDate] = useState<Date>(new Date());
  const [title, setTitle] = useState<string>("heute");
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [maxKw, setMaxKw] = useState<number>(300);
  const [avgKw, setAvgKw] = useState<number>(0);
  const state = userStore();
  useEffect(() => {
    const fetchData = async () => {
      const response = await fetch(
        `/api/voltego/today?customDate=${requestedDate.toISOString().split("T")[0]}`,
      );
      if (response.ok) {
        const data: StockPriceShort[] = await response.json();
        setStockPricesToday(data);

        const maxPower = Math.round(Math.max(...data.map((entry) => entry.power ?? 0)));

        if (maxPower > 300) {
          setMaxKw(maxPower);
        }

        const priceSum = data.reduce(
          (sum, item) => sum + (item.power ? item.power * item.amount : 0),
          0,
        );

        const powerSum = data.reduce((sum, item) => sum + (item.power ?? 0), 0);
        const avgPrice = priceSum / powerSum;

        // teile nur durch die anzahl an stunden due bereits vergangen sind (also wo es power im array gibt als key)
        const averagePower = powerSum / data.filter((obj) => obj.hasOwnProperty("power")).length;
        setAvgKw(parseFloat(averagePower.toFixed(2)));

        const minAmount = Math.round(Math.min(...data.map((entry) => entry.amount)));
        const maxAmount = Math.round(Math.max(...data.map((entry) => entry.amount)));
        if (minAmount < -1) {
          setMinPrice(minAmount);
        }
        if (minPrice < -1 && minAmount >= -1) {
          setMinPrice(-1);
        }
        if (maxAmount > 15) {
          setMaxPrice(maxAmount);
        }
        if (maxPrice > 15 && maxAmount <= 15) {
          setMaxPrice(15);
        }
        setTitle(`${getDateLabel(requestedDate)} ⌀ ${avgPrice.toFixed(1)}ct`);
      }
    };
    setTimeout(() => {
      //add a small delay for ou change to be done on server side
      fetchData();
    }, 1000);
  }, [state, requestedDate]); // Empty dependency array ensures this runs only once

  // Define the ECharts options
  const options = {
    title: {
      text: `${title}`,
      left: "center",
      textStyle: {
        fontSize: 10,
        color: "#696d82",
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      data: stockPricesToday.map((item) => `${item.hour}`),
    },
    yAxis: [
      {
        type: "value",
        name: "kW ",
        nameTextStyle: { fontSize: 10, fontWeight: "bold" },
        max: maxKw,
        axisLabel: {
          formatter: "{value}",
          fontSize: 10,
        },
        splitLine: {
          show: true, // Verhindert doppelte Linien
        },
      },
      {
        type: "value",
        nameTextStyle: { fontSize: 10, fontWeight: "bold" },
        min: minPrice,
        max: maxPrice,
        name: "ct/kWh",
        axisLabel: {
          formatter: "{value}",
          fontSize: 10,
        },
        splitLine: {
          show: false, // Verhindert doppelte Linien
        },
      },
    ],

    series: [
      {
        yAxisIndex: 1,
        name: "Price",
        type: "bar",
        data: stockPricesToday.map((item) => item.amount),
        itemStyle: {
          color: (params: { dataIndex: number; value: number }) => {
            const currentHour = new Date().getHours();
            const itemHour = stockPricesToday[params.dataIndex]?.hour;
            if (itemHour === currentHour) {
              return "orange";
            }
            return params.value >= 0 ? "#696d82" : "#91CC75";
          },
        },
      },
      {
        name: "Power",
        type: "line",
        yAxisIndex: 0,
        data: stockPricesToday.map((item) => item.power),
        itemStyle: {
          color: "green",
        },
        lineStyle: { width: 2 },
        symbolSize: 3,
      },
      {
        name: "Average",
        type: "line",
        yAxisIndex: 0,
        data: stockPricesToday.map((item) => avgKw),
        itemStyle: {
          color: "gray",
        },
        lineStyle: { width: 1 },
        symbol: "none",
      },
    ],
  };
  const handleTouchStart = (e: TouchEvent<HTMLDivElement>) => {
    const touches = e.targetTouches;
    if (touches && touches.length > 0) {
      setTouchStartX(touches[0]?.clientX ?? 0);
    }
  };

  const handleTouchMove = (e: TouchEvent<HTMLDivElement>) => {
    const touches = e.targetTouches;
    if (touches && touches.length > 0) {
      setTouchEndX(touches[0]?.clientX ?? 0);
    }
  };

  const handleTouchEnd = (e: TouchEvent<HTMLDivElement>) => {
    if (touchStartX == 0 || touchEndX == 0) {
      return;
    }
    if (touchStartX - touchEndX > 50) {
      goToNextDay();
    }

    if (touchEndX - touchStartX > 50) {
      goToPrevDay();
    }
    setTouchStartX(0);
    setTouchEndX(0);
  };

  const goToPrevDay = () => {
    const prevDay = new Date(requestedDate);
    prevDay.setDate(requestedDate.getDate() - 1);
    setRequestedDate(prevDay);
  };
  const goToNextDay = () => {
    const nextDay = new Date(requestedDate);
    nextDay.setDate(requestedDate.getDate() + 1);
    setRequestedDate(nextDay);
  };

  return (
    // Extract hours and amounts from the data
    <div
      className={"m-0 flex w-full flex-row p-0"}
      onTouchStart={(e) => handleTouchStart(e)}
      onTouchMove={(e) => handleTouchMove(e)}
      onTouchEnd={(e) => handleTouchEnd(e)}
    >
      <div className={"flex w-5 items-center text-center"}>
        <IoIosArrowBack className={"hover:cursor-pointer"} size={20} onClick={goToPrevDay} />
      </div>
      <ReactECharts option={options} style={{ height: "400px", width: "100%" }} />
      <div className={"flex w-5 items-center text-center "}>
        <IoIosArrowForward className={"hover:cursor-pointer"} size={20} onClick={goToNextDay} />
      </div>
    </div>
  );
};

export default StockPriceChart;
