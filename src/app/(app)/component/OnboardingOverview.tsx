import React from "react";

import { FaArrowAltCircleLeft, FaCheckCircle } from "react-icons/fa";
import StyledLink from "~/app/(app)/util/StyledLink";

interface Props {
  cardOrdered: boolean;
  cardsActive: boolean;
  paymentMethodActive: boolean;
  preDeliveredNotActive: boolean;
}

const OnboardingOverview = ({
  cardOrdered,
  cardsActive,
  paymentMethodActive,
  preDeliveredNotActive,
}: Props) => {
  return (
    <>
      {preDeliveredNotActive && (
        <div className={"md:max-w-70/100"}>
          <span>
            Sie haben bei der Registrierung bereits eine Ladekarte hinterlegt, die Sie im Vorfeld
            erhalten haben. Um diese Karte nutzen zu können, müssen Sie ein Zahlungsmittel
            hinterlegen und ihr Benutzerprofil vervollständigen.
          </span>
        </div>
      )}
      <div className={"flex flex-col gap-4"}>
        <div className={"flex flex-row items-center gap-2 sm:gap-4"}>
          <StyledLink
            href={preDeliveredNotActive ? "/emp/payment/newWithCard" : "/emp/payment/new"}
            disabled={paymentMethodActive}
            className={"disabled:brightness-80 w-full sm:w-auto"}
          >
            Zahlungsmittel hinterlegen
          </StyledLink>

          {paymentMethodActive && (
            <span className={"flex items-center gap-1"}>
              <FaCheckCircle size={18} color={"green"} />{" "}
              <span className={"invisible sm:visible"}>Erledigt</span>{" "}
            </span>
          )}
          {!paymentMethodActive && (
            <span className={"flex flex-col-reverse sm:flex-row sm:items-center sm:gap-1"}>
              <FaArrowAltCircleLeft size={18} />{" "}
              <span className={"text-xs sm:text-base"}>Nächster Schritt</span>
            </span>
          )}
        </div>

        <div className={"flex flex-row items-center gap-2 sm:gap-4"}>
          <StyledLink
            href={"/emp/card/new"}
            disabled={cardOrdered}
            className={"disabled:brightness-80 w-full sm:w-auto"}
          >
            {preDeliveredNotActive ? "Mit Ladekarte registrieren" : "Ladekarte bestellen"}
          </StyledLink>
          {cardOrdered && (
            <span className={"flex items-center gap-1"}>
              <FaCheckCircle className={""} size={18} color={"green"} />{" "}
              <span className={"invisible sm:visible"}>Erledigt</span>{" "}
            </span>
          )}
          {paymentMethodActive && !cardOrdered && (
            <span className={"flex flex-col-reverse sm:flex-row sm:items-center sm:gap-1"}>
              <FaArrowAltCircleLeft size={18} />{" "}
              <span className={"text-xs sm:text-base"}>Nächster Schritt</span>
            </span>
          )}
        </div>

        {/*Wenn jemand zuerst die Karte erhalten hat (also kein Zahlungsmittel, aber schon eine Karte),
         dann blende das "Ladekarte aktivieren" aus, da es nur verwirren könnte*/}
        {!(cardsActive && !paymentMethodActive) && (
          <div className={"flex w-full flex-row items-center gap-2 sm:gap-4"}>
            <StyledLink
              className={"disabled:brightness-80 w-full sm:w-auto"}
              href={"/emp/card"}
              disabled={cardsActive || !paymentMethodActive || !cardOrdered}
            >
              Ladekarte aktivieren
            </StyledLink>
            {cardsActive && (
              <span className={"flex items-center gap-1"}>
                <FaCheckCircle size={18} color={"green"} />{" "}
                <span className={"invisible sm:visible"}>Erledigt</span>{" "}
              </span>
            )}
            {paymentMethodActive && cardOrdered && !cardsActive && (
              <span className={"flex flex-col-reverse sm:flex-row sm:items-center sm:gap-1"}>
                <FaArrowAltCircleLeft size={18} />{" "}
                <span className={"text-xs sm:text-base"}>Nächster Schritt</span>
              </span>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default OnboardingOverview;
