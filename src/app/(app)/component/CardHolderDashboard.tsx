"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";
import Headline from "~/component/Headline";
import React from "react";
import prisma from "~/server/db/prisma";

const getStatistics = async () => {
  const session = await getServerSession(authOptions);
  if (session && session?.user && session?.user.role == Role.CARD_HOLDER) {
    const cards = await prisma?.eMPCard.findMany({
      where: { userId: session?.user?.id },
    });
    const tokens =
      cards
        ?.map((card) => card.physicalCardId)
        ?.filter((item): item is string => item !== undefined && item !== null) ?? [];

    const cdrs = await prisma.cdr.findMany({
      where: { Authentication_ID: { in: tokens } },
    });

    const numCdrs = cdrs?.length ?? 0;
    const cdrStatistic = cdrs?.reduce(
      (akku, cdr) => {
        const vol = cdr?.Volume ?? 0;
        return {
          totalkWh: akku.totalkWh + vol,
        };
      },
      { totalkWh: 0 },
    );
    return { totalkWh: cdrStatistic?.totalkWh ?? 0, numCdrs: numCdrs };
  }

  return { totalkWh: 0, numCdrs: 0 };
};

export const CardHolderDashboard = async () => {
  const { totalkWh, numCdrs } = await getStatistics();
  //const getCdrs = await getCdrs();

  return (
    <>
      <Headline title={"Ihr persönliches Dashboard"} />
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4 ">
        <RealtimeWidget
          caption={"Energie Gesamt"}
          valueUnit={"kWh"}
          primaryValue={totalkWh.toFixed(2) ?? 0}
          loading={false}
        />
        <RealtimeWidget
          caption={"Anzahl Ladevorgänge"}
          valueUnit={""}
          primaryValue={numCdrs ?? 0}
          loading={false}
        />
      </div>
    </>
  );
};
