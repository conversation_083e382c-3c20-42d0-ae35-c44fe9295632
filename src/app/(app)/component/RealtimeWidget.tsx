import React, { ReactElement, ReactNode } from "react";
import { env } from "~/env";

export interface RealtimeWidgetProps {
  caption: string;
  loading: boolean;
  list?: string[] | number[] | ReactElement[];
  primaryValue?: string | number;
  primaryTitle?: string;
  secondaryValue?: string | number;
  secondaryTitle?: string;
  icon?: ReactNode;
  valueUnit?: string;
  children?: ReactNode;
  captionTitle?: string;
  className?: string;
}

const RealtimeWidget = ({
  caption,
  loading,
  list,
  primaryValue,
  primaryTitle,
  secondaryValue,
  secondaryTitle,
  icon,
  valueUnit,
  children,
  captionTitle,
  className = "",
}: RealtimeWidgetProps) => {
  return (
    <div className={`mb-3 w-full ${className}`}>
      <div className="relative flex min-w-0 flex-col break-words rounded-md bg-white bg-clip-border shadow-md-all dark:bg-gray-950 dark:shadow-soft-dark-xl">
        {icon && (
          <div className=" absolute right-0 top-0 scale-50 rounded-lg  bg-primary p-2 text-white sm:top-2 sm:scale-75 md:right-2 md:top-4 md:scale-75 lg:scale-100">
            {icon}
          </div>
        )}

        <div className=" flex-auto p-2 sm:p-4">
          <div className="flex w-full flex-wrap">
            <div title={captionTitle ?? ""} className={"w-full"}>
              <p className="font-sans mb-0 mr-4 text-sm font-semibold leading-normal text-primary dark:opacity-60">
                {caption}
              </p>

              {loading && <h5 className={"font-bold dark:text-white"}>Loading...</h5>}
              {primaryValue != null && primaryValue != undefined && (
                <h5 className="mb-0 font-bold dark:text-white">
                  {!loading && (
                    <span title={primaryTitle ?? caption}>
                      {primaryValue} {valueUnit}
                    </span>
                  )}
                  {!loading && secondaryValue != null && secondaryValue != undefined && (
                    <span
                      className={"font-weight-bolder ml-1 text-sm leading-normal "}
                      title={secondaryTitle ?? caption}
                    >
                      ({secondaryValue})
                    </span>
                  )}
                </h5>
              )}
              {!loading && list && (
                <h5 className="mb-0 font-bold dark:text-white">
                  <ul className={" text-sm"}>
                    {list?.map((item, index) => {
                      return (
                        <li key={index} className={`truncate ${index === 0 ? "mr-7" : ""}`}>
                          {item}
                        </li>
                      );
                    })}
                    {list.length === 0 && <li>Keine</li>}
                  </ul>
                </h5>
              )}
              {!loading && children != undefined && children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealtimeWidget;
