"use client";
import React, { useState } from "react";
import <PERSON><PERSON> from "~/component/button";
import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import Loading from "~/app/(app)/loading";
import { FiLoader } from "react-icons/fi";

interface Props {
  searchParams: {
    error?: string;
    callbackUrl?: string;
  };
}
interface LoginFormValues {
  email: string;
  password: string;
}
const Page = (props: Props) => {
  const {
    register,
    formState: { isSubmitting, isSubmitted, errors },
    handleSubmit,
  } = useForm<LoginFormValues>();
  const onSubmit = async (data: LoginFormValues) => {
    //setLoading(true);
    await signIn("credentials", {
      ...data,
      redirect: true,
      callbackUrl: "/",
    });
  };

  return (
    <div className={"flex flex-col justify-center gap-1"}>
      <h1 className={"text-primary"}>Willkommen im Ladeportal</h1>
      <div className={"flex w-full justify-center"}>
        <form role="form" className={"w-full"} method="post" onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <input
              {...register("email")}
              type="email"
              name={"email"}
              placeholder="E-Mail"
              className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
            />
          </div>
          <div className="mb-4">
            <input
              {...register("password")}
              type="password"
              name={"password"}
              placeholder="Passwort"
              className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500  focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
            />
          </div>

          {props?.searchParams?.error && (
            <div className={"text-center text-red-400"}>
              Login fehlgeschlagen, Benutzername oder Passwort falsch
            </div>
          )}
          <div className="flex flex-col gap-1  text-center">
            <Button
              disabled={isSubmitting || (isSubmitted && Object.keys(errors).length == 0)}
              className={" w-full bg-eul-gray"}
            >
              Login
              {isSubmitting ||
                (isSubmitted && Object.keys(errors).length == 0 && (
                  <FiLoader className="ml-1 animate-spin" />
                ))}
            </Button>

            <a className={"text-right"} href={"/passwortReset"}>
              Passwort vergessen?
            </a>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Page;
