import { Source_Sans_3 } from "next/font/google";
import "~/styles/globals.css";
import { ouColors, ouLogos } from "~/styles/oucolors/oucolors";
import React from "react";

export const metadata = {
  title: "Ladeportal",
  icons: {
    icon: "/favicon/favicon-32x32.png",
    apple: "favicon/favicon-32x32.png",
  },
  description: "Ladeportal",
};

const sansnew = Source_Sans_3({ display: "swap", variable: "--font-sansnew", subsets: ["latin"] });

const LoginLayout = async ({ children }: { children: React.ReactNode }) => {
  return (
    <html lang="de" className={`${sansnew.variable}`}>
      <body
        style={ouColors.Default}
        className={
          "m-0 bg-eul-main text-left font-sansnew text-base font-normal leading-default text-primary antialiased dark:bg-slate-950 dark:text-white"
        }
      >
        <main className="relative h-full max-h-screen rounded-xl transition-all duration-200 ease-soft-in-out">
          <div className={"mx-auto my-4 flex min-h-70-screen w-full justify-center p-6"}>
            {children}
          </div>
        </main>
      </body>
    </html>
  );
};

export default LoginLayout;
