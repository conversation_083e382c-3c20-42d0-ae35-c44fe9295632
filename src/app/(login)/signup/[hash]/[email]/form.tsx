"use client";

import React, { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { UserWithOu } from "~/app/(app)/users/page";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { User } from "@prisma/client";

interface FormProps {
  name: string;
  lastName: string;
  password: string;
  passwordRepeat: string;
  country: string;
  postalCode: string;
  city: string;
  street: string;
  houseNumber: string;
  phone?: string;
  signUpHash: string;
}

const SignupForm = ({ user }: { user: UserWithOu }) => {
  const [submissionSuccess, setSubmissionSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors, isSubmitting },
  } = useForm<FormProps>({
    defaultValues: {
      name: user?.name || "",
      lastName: user?.lastName || "",
    },
  });
  const onSubmit: SubmitHandler<FormProps> = async (data) => {
    try {
      const res = await fetch("/api/user/verify-invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          email: user?.email,
          signUpHash: user?.signUpHash,
        }),
      });
      const resData = await res.json();
      // Hier könnten Sie einen Erfolgshandler hinzufügen
      setSubmissionSuccess(true);
    } catch (error) {
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };

  return (
    <div className={``}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className={`w-full space-y-4  rounded-lg  bg-white transition-all ${
          submissionSuccess ? "hidden opacity-0" : "opacity-100"
        }`}
      >
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <label className="block text-sm font-medium">Email</label>
            <input
              value={user?.email}
              disabled={true}
              readOnly={true}
              className="disabled bg-eul-lightgray block w-full appearance-none rounded-lg
              border border-solid border-gray-300
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline
              focus:outline-none"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">Vorname</label>
            <input
              {...register("name", { required: "Bitte einen Vornamen eingeben" })}
              className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
            {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">Nachname</label>
            <input
              {...register("lastName", { required: "Bitte einen Nachnamen eingeben" })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
            {errors.lastName && <p className={"text-red-500"}>{errors.lastName.message}</p>}
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">Passwort</label>
            <input
              {...register("password", {
                validate: (value) => {
                  if (value.length < 8) {
                    return "Passwort muss mindeszens 8 Zeichen lang sein";
                  }
                  return (
                    [/[a-z]/, /[A-Z]/, /[0-9]/, /[^a-zA-Z0-9]/].every((pattern) =>
                      pattern.test(value),
                    ) ||
                    "Passwort muss Kleinbuchstaben, Großbuchstaben, Zahlen und Sonderzeichen enthalten"
                  );
                },
              })}
              type="password"
              className={`w-full rounded-md border p-2 ${
                errors.password ? "border-red-500" : "border-gray-300"
              } focus:border-blue-500 focus:ring-1 focus:ring-blue-500`}
            />
            {errors.password && (
              <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">Passwort wiederholen</label>
            <input
              {...register("passwordRepeat", {
                validate: (value) =>
                  value === getValues("password") || "Passwörter stimmen nicht überein",
              })}
              type="password"
              className={` ${
                errors.passwordRepeat ? "border-red-500" : "border-gray-300"
              }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
            />
            {errors.passwordRepeat && (
              <p className="mt-1 text-xs text-red-500">{errors.passwordRepeat.message}</p>
            )}
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium">Land</label>
            <select
              {...register("country", { required: true })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            >
              <option value="Deutschland">Deutschland</option>
            </select>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">PLZ</label>
            <input
              {...register("postalCode", { required: true })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">Ort</label>
            <input
              {...register("city", { required: true })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="space-y-2 md:col-span-2">
            <label className="block text-sm font-medium">Straße</label>
            <input
              {...register("street", { required: true })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium">Hausnummer</label>
            <input
              {...register("houseNumber", { required: true })}
              className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
            />
          </div>
        </div>
        <div className="space-y-2">
          <label className="block text-sm font-medium">Telefon (optional)</label>
          <input
            {...register("phone")}
            className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
          />
        </div>
        <Button className={"w-full sm:max-w-64"} type="submit">
          Registrieren {isSubmitting ? <FiLoader className="animate-spin" /> : "Bestätigen"}
        </Button>
      </form>
      {submissionSuccess && (
        <div className="mt-4 opacity-100 transition-all">
          <p>Danke für Ihre Registrierung!</p>
          <a href="/" className="text-blue-500 underline">
            Zur Login-Seite
          </a>
        </div>
      )}
    </div>
  );
};

export default SignupForm;
