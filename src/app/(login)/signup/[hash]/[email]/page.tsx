import React from "react";
import prisma from "../../../../../server/db/prisma";
import SignupForm from "~/app/(login)/signup/[hash]/[email]/form";

interface Params {
  email: string;
  hash: string;
}

const getValid = async (params: Params) => {
  const user = await prisma.user.findUnique({
    where: {
      email: decodeURIComponent(params.email),
    },
    include: {
      ou: true,
    },
  });
  if (!user) {
    throw new Error("User not found");
  }
  if (user.signUpHash !== params.hash) {
    throw new Error("Invalid hash");
  }

  return user;
};

const RegistrationForm = async ({ params }: { params: Params }) => {
  try {
    const user = await getValid(params);

    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="w-full rounded-xl p-10 shadow-lg ">
          <h2 className="mb-4 font-bold text-primary"><PERSON><PERSON><PERSON><PERSON>, {user.name}</h2>
          <p className="mb-4">
            Du wurdest von <span className="font-semibold">{user.ou.name}</span> eingeladen, dich zu
            registrieren.
          </p>
          <p className="mb-6">
            Nach der Registrierung hast du die Möglichkeit, ein Zahlungsmittel zu hinterlegen und
            eine Ladekarte zu bestellen.
          </p>

          <SignupForm user={user}></SignupForm>
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-soft-gray flex min-h-screen items-center justify-center">
        <div className="w-1/3 rounded-xl bg-white p-10 text-center shadow-lg dark:bg-gray-800">
          <h2 className="mb-4 text-2xl font-bold text-red-600">Ungültiger Link</h2>
          <p className="text-gray-700">
            Der Registrierungslink ist ungültig oder abgelaufen. Bitte wende dich an den Absender
            der Einladung, um einen neuen Link zu erhalten.
          </p>
        </div>
      </div>
    );
  }
};

export default RegistrationForm;
