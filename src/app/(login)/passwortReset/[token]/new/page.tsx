"use client";

import React, { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { UserWithOu } from "~/app/(app)/users/page";
import <PERSON><PERSON> from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import Loading from "~/app/(app)/loading";

interface FormProps {
  token: string;
  password: string;
  passwordRepeat: string;
}

const PasswortResetPage = ({ params }: { params: { token: string } }) => {
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    getValues,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<FormProps>({
    defaultValues: {
      token: params.token,
    },
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [tokenValid, setTokenValid] = useState<boolean>(false);
  const checkToken = async () => {
    const res = await fetch("/api/reset-password/checktoken", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token: params.token }),
    });
    setLoading(false);
    if (res.status == 200) {
      setTokenValid(true);
    }
  };
  useEffect(() => {
    void checkToken();
  }, []);
  const onSubmit: SubmitHandler<FormProps> = async (data) => {
    try {
      console.log(data, "<<<");
      const res = await fetch("/api/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data }),
      });

      if (res.status == 200) {
        setSubmissionSuccess(true);
        reset();
      }
      // Hier könnten Sie einen Erfolgshandler hinzufügen
    } catch (error) {
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };
  const [showPassword, setShowPassword] = useState<boolean>(false);

  return (
    <div className={``}>
      <h2 className={"text-primary"}>Passwort zurücksetzen </h2>
      {loading && <Loading />}
      {tokenValid && (
        <form
          onSubmit={handleSubmit(onSubmit)}
          className={"flex w-full  flex-col gap-3 rounded-lg bg-white transition-all"}
        >
          <div className="flex flex-row items-end gap-2">
            <div className="">
              <label className="block text-sm font-medium">Passwort</label>
              <input
                {...register("password", {
                  validate: (value) => {
                    if (value.length < 8) {
                      return "Passwort muss mindeszens 8 Zeichen lang sein";
                    }
                    return (
                      [/[a-z]/, /[A-Z]/, /[0-9]/, /[^a-zA-Z0-9]/].every((pattern) =>
                        pattern.test(value),
                      ) ||
                      "Passwort muss Kleinbuchstaben, Großbuchstaben, Zahlen und Sonderzeichen enthalten"
                    );
                  },
                })}
                type={`${showPassword ? "text" : "password"}`}
                className={` ${
                  errors.password ? "border-red-500" : "border-gray-300"
                }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
              />
            </div>
            <div className="">
              <label className="block text-sm font-medium">Passwort wiederholen</label>
              <input
                {...register("passwordRepeat", {
                  validate: (value) =>
                    value === getValues("password") || "Passwörter stimmen nicht überein",
                })}
                type={`${showPassword ? "text" : "password"}`}
                className={` ${
                  errors.passwordRepeat ? "border-red-500" : "border-gray-300"
                }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
              />
            </div>
            <div className={""}>
              {showPassword ? (
                <FaEye onClick={() => setShowPassword(false)} size={20} />
              ) : (
                <FaEyeSlash onClick={() => setShowPassword(true)} size={20} />
              )}
            </div>
          </div>
          <div>
            {errors.passwordRepeat && (
              <p className="text-xs text-red-500">{errors.passwordRepeat.message}!</p>
            )}
            {errors.password && <p className="text-xs text-red-500">{errors.password.message}!</p>}
          </div>
          <Button className={"w-full sm:max-w-100"} type="submit">
            Neues Passwort setzen {isSubmitting && <FiLoader className="animate-spin" />}
          </Button>
        </form>
      )}
      {!tokenValid && <span className={"text-red-400"}>Token ungültig</span>}
      {submissionSuccess && (
        <div className="mt-4 text-green-600 transition-all">
          <p>Passwort aktualisiert!</p>
          <a href="/" className="text-blue-500 underline">
            Zur Login-Seite
          </a>
        </div>
      )}
    </div>
  );
};

export default PasswortResetPage;
