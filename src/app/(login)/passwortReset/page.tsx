"use client";
import { FormEvent, useState } from "react";
import <PERSON><PERSON> from "~/component/button";

export default function ResetPassword() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    const response = await fetch("/api/reset-password/request", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });
    const data = await response.json();
    setMessage(data.message);
  };

  return (
    <div>
      <h1 className={""}>Passwort zurücksetzen</h1>
      <form className={"flex flex-col justify-between gap-5 sm:flex-row"} onSubmit={handleSubmit}>
        <input
          type="email"
          value={email}
          className={
            "block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-gray-600"
          }
          onChange={(e) => setEmail(e.target.value)}
          required
          placeholder="Geben Sie Ihre E-Mail-Adresse ein"
        />
        <Button className={"w-full"} type="submit">
          Passwort zurücksetzen
        </Button>
      </form>
      <p className={"mt-2 p-1"}>{message}</p>
    </div>
  );
}
