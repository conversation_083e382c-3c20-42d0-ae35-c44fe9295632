"use client";

import React, { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import But<PERSON> from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { FaCheckCircle } from "react-icons/fa";
import { CompanyTarif } from "@prisma/client";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";

interface FormProps {
  password: string;
  passwordRepeat: string;
  email: string;
  visualNumber?: string;
  registrationSlug?: string;
}

const MinimalRegistrationForm = ({
  registrationslug,
  includeCard,
}: {
  registrationslug: string;
  includeCard: boolean;
}) => {
  const {
    register,
    handleSubmit,
    getValues,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<FormProps>({
    defaultValues: {
      registrationSlug: registrationslug,
    },
  });

  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const watchedFields = watch();

  const [cardValid, setCardValid] = useState<boolean>(false);
  const [cardNumber, setCardNumber] = useState<string>("");
  const [verifying, setVerifying] = useState<boolean>(false);
  const [cardNotFoundMessage, setCardNotFoundMessage] = useState<string>("");

  const onSubmit: SubmitHandler<FormProps> = async (data) => {
    try {
      const res = await fetch("/api/user/createUserMinimal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data, visualNumber: cardNumber }),
      });

      if (res.ok) {
        setErrorMessage("");
        setSubmissionSuccess(true);
      } else {
        const resData = await res.json();
        setErrorMessage(resData.error);
      }
    } catch (error) {
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };
  const checkCardNumber = async () => {
    setVerifying(true);
    try {
      const res = await fetch(
        `/api/emp/verify-card?cardnumber=${cardNumber}&cpo=${registrationslug}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      if (res.status == 200) {
        setCardValid(true);
        setCardNotFoundMessage("");
      } else {
        setCardNotFoundMessage(await res.text());
      }
      setVerifying(false);
      // Hier könnten Sie einen Erfolgshandler hinzufügen
    } catch (error) {
      setVerifying(false);
      setCardNotFoundMessage("Fehler beim Prüfen der Kartennummer");
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };

  return (
    <>
      {includeCard && (
        <div className={"flex flex-col gap-1"}>
          <div className="row mt-3 flex  w-full flex-col items-center gap-0 sm:w-auto sm:flex-row sm:gap-1 md:max-w-70/100 ">
            <label className="block self-start text-sm font-medium sm:self-auto">
              Kartennummer:
            </label>
            <div className={"flex w-full flex-row items-center gap-1 sm:w-auto"}>
              <input
                disabled={verifying || cardValid}
                onChange={(e) => setCardNumber(e.target.value)}
                className={`disabled sm:max-w-52 ${
                  cardValid ? "bg-green-100" : "bg-eul-lightgray"
                }  block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none
              `}
              />
              {cardValid && <FaCheckCircle color={"green"} size={18} />}
            </div>

            {!cardValid && (
              <Button
                className={"mt-4 w-full self-start sm:mt-0 sm:max-w-32 sm:self-auto "}
                onClick={checkCardNumber}
                disabled={verifying}
              >
                Prüfen {verifying && <FiLoader className="animate-spin" />}
              </Button>
            )}
          </div>

          {cardNotFoundMessage && (
            <div>
              <span className={"text-red-400"}>Fehler beim Prüfen: {cardNotFoundMessage}!</span>
            </div>
          )}
        </div>
      )}

      {!includeCard ||
        (includeCard && cardValid && (
          <form
            onSubmit={handleSubmit(onSubmit)}
            className={`w-full rounded-lg  bg-white transition-all ${
              submissionSuccess ? "hidden opacity-0" : "opacity-100"
            }`}
          >
            <div className="flex w-full flex-col gap-2 py-2 sm:flex-row">
              <div className="w-full">
                <label className="block text-sm font-medium">Email:</label>
                <input
                  type={"email"}
                  {...register("email", {
                    required: "Bitte eine E-Mail adresse eingeben",
                    pattern: {
                      value: /\S+@\S+\.\S+/,
                      message: "Bitte ein gültige Email-Adresse eingeben",
                    },
                  })}
                  className="bg-eul-lightgray block w-full appearance-none rounded-lg
              border border-solid border-gray-300
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline
              focus:outline-none"
                />
                {errors.email && <p className={"text-red-500"}>{errors.email.message}</p>}
              </div>
              <div className="w-full">
                <label className="block text-sm font-medium">Passwort:</label>
                <input
                  {...register("password", {
                    validate: (value) => {
                      if (value.length < 8) {
                        return "Passwort muss mindeszens 8 Zeichen lang sein";
                      }
                      return (
                        [/[a-z]/, /[A-Z]/, /[0-9]/, /[^a-zA-Z0-9]/].every((pattern) =>
                          pattern.test(value),
                        ) ||
                        "Passwort muss Kleinbuchstaben, Großbuchstaben, Zahlen und Sonderzeichen enthalten"
                      );
                    },
                  })}
                  type="password"
                  className={`block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80`}
                />
                {errors.password && <p className="mt-1 text-red-500">{errors.password.message}</p>}
              </div>
              <div className="w-full">
                <label className="block text-sm font-medium">Passwort wiederholen:</label>
                <input
                  {...register("passwordRepeat", {
                    validate: (value) =>
                      value === getValues("password") || "Passwörter stimmen nicht überein",
                  })}
                  type="password"
                  className={` ${
                    errors.passwordRepeat ? "border-red-500" : "border-gray-300"
                  }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
                />
                {errors.passwordRepeat && (
                  <p className="mt-1  text-red-500">{errors.passwordRepeat.message}</p>
                )}
              </div>
            </div>

            <Button className={"w-full sm:max-w-64"} type="submit">
              Registrieren {isSubmitting ? <FiLoader className="animate-spin" /> : "abschließen"}
            </Button>
          </form>
        ))}
      {submissionSuccess && (
        <div className="mt-4 opacity-100 transition-all">
          <p>Danke für Ihre Registrierung!</p>
          <p>
            Zur Bestätigung der E-Mail Adresse wurde eine E-Mail an {watchedFields.email} gesendet.
          </p>
        </div>
      )}

      {errorMessage && (
        <div className="mt-4 flex flex-col gap-0 opacity-100 transition-all">
          <span>Fehler bei der Registrierung:</span>
          <span className={"text-red-400"}>{errorMessage}</span>
        </div>
      )}
    </>
  );
};

export default MinimalRegistrationForm;
