import React from "react";

import Image from "next/image";
import { getBackgroundColor, getCompanyLogo, getCPOOu } from "~/utils/register/whitelabelHelper";
import MinimalRegistrationForm from "~/app/(login)/component/MinimalRegistrationForm";
interface Params {
  registrationslug: string;
}

const RegistrationPage = async ({ params }: { params: Params }) => {
  try {
    const cpoOu = await getCPOOu(params.registrationslug);

    return (
      <div style={getBackgroundColor(cpoOu?.name)} className="flex justify-center text-primary">
        <div className="w-full rounded-xl p-10 shadow-lg ">
          <div className={"flex flex-col-reverse items-center gap-4 text-center sm:flex-row"}>
            <span className="size mb-5 text-2xl font-bold text-primary sm:mb-0 sm:text-2xl">
              Willkommen im Ladeportal
            </span>
            <Image
              src={getCompanyLogo(cpoOu?.name ?? "") ?? ""}
              priority={false}
              width={170}
              height={150}
              alt={`${cpoOu?.name} Logo`}
              className={
                "inline-block h-full max-h-12 max-w-full transition-all duration-200 ease-soft-in-out dark:hidden"
              }
            />
          </div>
          <p className="mb-6">
            Zum Aktivieren und Bestellen von Ladekarten für die Ladeinfrastruktur von{" "}
            <b>{cpoOu?.Contact?.companyName}</b> geben Sie bitte ihre Ladekartennummer ein.
          </p>
          <p className="">
            Sollten Sie sich bereits im Ladeportal registriert haben, dann {""}
            <a className={"underline"} href={"/"}>
              loggen {""}
            </a>
            Sie sich bitte mit ihrem Benutzerprofil ein und aktivieren Sie dort diese eine weitere
            Ladekarte.
          </p>
          <p>
            Nach erfolgreicher Prüfung können Sie ihre Registrierung bestätigen. Nach Verifizierung
            Ihrer E-Mail Adresse kann im Ladeportal ein SEPA Lastschriftmandat hinterlegt werden zur
            monatlichen Abrechnung der Ladevorgänge.
          </p>

          <MinimalRegistrationForm
            includeCard={true}
            registrationslug={params?.registrationslug}
          ></MinimalRegistrationForm>
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-soft-gray flex min-h-screen items-center justify-center">
        <div className="w-1/3 rounded-xl bg-white p-10 text-center shadow-lg dark:bg-gray-800">
          <h2 className="mb-4 text-2xl font-bold text-red-600">Ungültiger Link</h2>
          <p className="text-gray-700">
            Der Registrierungslink ist ungültig oder abgelaufen. Bitte wende dich an den
            Ladesäulen-Betreiber um einen korrekten Link zu erhalten.
          </p>
        </div>
      </div>
    );
  }
};

export default RegistrationPage;
