"use client";

import React, { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { FaCheckCircle } from "react-icons/fa";
import { CompanyTarif } from "@prisma/client";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";

interface FormProps {
  name: string;
  lastName: string;
  password: string;
  passwordRepeat: string;
  country: string;
  postalCode: string;
  city: string;
  street: string;
  houseNumber: string;
  phone?: string;
  email: string;
  visualNumber: string;
  registrationSlug: string;
  selectedTarifIds: string[];
}

const ActivateForm = ({
  registrationslug,
  companyTariffs,
}: {
  registrationslug: string;
  companyTariffs: CompanyTarif[];
}) => {
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [cardValid, setCardValid] = useState<boolean>(false);
  const [noCard, setNoCard] = useState<boolean>(false);
  const [cardNumber, setCardNumber] = useState<string>("");
  const [verifying, setVerifying] = useState<boolean>(false);
  const [cardNotFoundMessage, setCardNotFoundMessage] = useState<string>("");
  const {
    register,
    handleSubmit,
    getValues,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<FormProps>({
    defaultValues: {
      registrationSlug: registrationslug,
      selectedTarifIds: companyTariffs.map((t) => t.id),
    },
  });
  const watchedFields = watch();
  const onSubmit: SubmitHandler<FormProps> = async (data) => {
    try {
      const res = await fetch("/api/user/createByCard", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data, visualNumber: cardNumber }),
      });
      const resData = await res.json();
      // Hier könnten Sie einen Erfolgshandler hinzufügen
      setSubmissionSuccess(true);
    } catch (error) {
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };
  const checkCardNumber = async () => {
    setVerifying(true);
    try {
      const res = await fetch(
        `/api/emp/verify-card?cardnumber=${cardNumber}&cpo=${registrationslug}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      if (res.status == 200) {
        setCardValid(true);
        setCardNotFoundMessage("");
      } else {
        setCardNotFoundMessage(await res.text());
      }
      setVerifying(false);
      // Hier könnten Sie einen Erfolgshandler hinzufügen
    } catch (error) {
      setVerifying(false);
      setCardNotFoundMessage("Fehler beim Prüfen der Kartennummer");
      console.error(error);
      // Hier könnten Sie einen Fehlerhandler hinzufügen
    }
  };

  return (
    <>
      <div className={"flex flex-col gap-1"}>
        <div className="row mt-3 flex  w-full items-center gap-1 md:max-w-70/100 ">
          <label className="blocktext-sm font-medium">Kartennummer:</label>
          <input
            disabled={verifying || cardValid}
            onChange={(e) => setCardNumber(e.target.value)}
            className={`disabled sm:max-w-52 ${
              cardValid ? "bg-green-100" : "bg-eul-lightgray"
            }  block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none
              `}
          />

          {cardValid ? (
            <FaCheckCircle color={"green"} size={18} />
          ) : (
            <Button className={"min-w-32"} onClick={checkCardNumber} disabled={verifying}>
              Prüfen {verifying && <FiLoader className="animate-spin" />}
            </Button>
          )}
        </div>
        <div>
          {cardNotFoundMessage && (
            <span className={"text-red-400"}>Fehler beim Prüfen: {cardNotFoundMessage}!</span>
          )}
        </div>
      </div>
      {noCard ||
        (cardValid && (
          <div className={``}>
            {!submissionSuccess && (
              <>
                <div
                  className={`border-primary   relative my-3 flex-col rounded-xl border bg-gray-100 px-4 py-2 text-primary sm:w-70/100`}
                  role="alert"
                >
                  <span>
                    Prüfung erfolgreich. Sie können sich nun registrieren und diese Karte somit
                    einem Benutzerkonto zuordnen!{" "}
                  </span>
                </div>

                <hr className={"my-2 mt-3 h-px border-1 bg-gray-950 dark:bg-gray-700"} />
              </>
            )}
            <form
              onSubmit={handleSubmit(onSubmit)}
              className={`w-full space-y-4  rounded-lg  bg-white transition-all ${
                submissionSuccess ? "hidden opacity-0" : "opacity-100"
              }`}
            >
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Email:</label>
                  <input
                    type={"email"}
                    {...register("email", {
                      required: "Bitte eine E-Mail adresse eingeben",
                      pattern: {
                        value: /\S+@\S+\.\S+/,
                        message: "Bitte ein gültige Email-Adresse eingeben",
                      },
                    })}
                    className="bg-eul-lightgray block w-full appearance-none rounded-lg
              border border-solid border-gray-300
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline
              focus:outline-none"
                  />
                  {errors.email && <p className={"text-red-500"}>{errors.email.message}</p>}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium">Telefon (optional)</label>
                  <input
                    {...register("phone")}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Vorname:</label>
                  <input
                    {...register("name", { required: "Bitte einen Vornamen eingeben" })}
                    className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                  {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Nachname:</label>
                  <input
                    {...register("lastName", { required: "Bitte einen Nachnamen eingeben" })}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                  {errors.lastName && <p className={"text-red-500"}>{errors.lastName.message}</p>}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Passwort:</label>
                  <input
                    {...register("password", {
                      validate: (value) => {
                        if (value.length < 8) {
                          return "Passwort muss mindeszens 8 Zeichen lang sein";
                        }
                        return (
                          [/[a-z]/, /[A-Z]/, /[0-9]/, /[^a-zA-Z0-9]/].every((pattern) =>
                            pattern.test(value),
                          ) ||
                          "Passwort muss Kleinbuchstaben, Großbuchstaben, Zahlen und Sonderzeichen enthalten"
                        );
                      },
                    })}
                    type="password"
                    className={`block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80`}
                  />
                  {errors.password && (
                    <p className="mt-1 text-red-500">{errors.password.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Passwort wiederholen:</label>
                  <input
                    {...register("passwordRepeat", {
                      validate: (value) =>
                        value === getValues("password") || "Passwörter stimmen nicht überein",
                    })}
                    type="password"
                    className={` ${
                      errors.passwordRepeat ? "border-red-500" : "border-gray-300"
                    }   dark:placeholder:text-white/80" block w-full appearance-none rounded-lg
              border border-solid border-gray-300 bg-white
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950
              dark:text-white/80`}
                  />
                  {errors.passwordRepeat && (
                    <p className="mt-1  text-red-500">{errors.passwordRepeat.message}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Land:</label>
                  <select
                    {...register("country", { required: true })}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  >
                    <option value="Deutschland">Deutschland</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">PLZ:</label>
                  <input
                    {...register("postalCode", { required: "Bitte eine Postleitzahl eingeben" })}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                  {errors.postalCode && (
                    <p className={"text-red-500"}>{errors.postalCode.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Ort:</label>
                  <input
                    {...register("city", { required: "Bitte einen Ort eingeben" })}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                  {errors.city && <p className={"text-red-500"}>{errors.city.message}</p>}
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="space-y-2 md:col-span-2">
                  <label className="block text-sm font-medium">Straße:</label>
                  <input
                    {...register("street", { required: "Bitte eine Straße eingeben" })}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                  {errors.street && <p className={"text-red-500"}>{errors.street.message}</p>}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Hausnummer:</label>
                  <input
                    {...register("houseNumber", { required: "Bitte eine Hausnummer eingeben" })}
                    className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                  />
                  {errors.houseNumber && (
                    <p className={"text-red-500"}>{errors.houseNumber.message}</p>
                  )}
                </div>
              </div>

              {!submissionSuccess && (
                <>
                  <h5 className={"pt-5 text-secondary "}>
                    Ladetarife die mit der Karte gebucht werden:
                  </h5>
                  <div className={"flex flex-col gap-1 sm:flex-row sm:gap-4"}>
                    {companyTariffs.map((tarif: CompanyTarif) => (
                      <TarifCard
                        blockingFee={tarif.blockingFee}
                        blockingFeeMax={tarif.blockingFeeMax}
                        blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                        size={"normal"}
                        oneTimeFeePayer={tarif.oneTimeFeePayer}
                        vat={19}
                        oneTimeFee={tarif.oneTimeFee}
                        basicFee={tarif.basicFee}
                        currentType={tarif.currentType ?? ""}
                        key={tarif.id}
                        internal={false}
                        tarifId={tarif.id}
                        interactive={true}
                        optional={false}
                        tarifName={tarif.name ?? ""}
                        pricekWh={tarif.energyPrice}
                        priceSession={tarif.sessionPrice}
                        title={tarif.name ?? ""}
                        description={
                          tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"
                        }
                      />
                    ))}
                  </div>
                </>
              )}
              <Button className={"w-full sm:max-w-64"} type="submit">
                Registrieren {isSubmitting ? <FiLoader className="animate-spin" /> : "Bestätigen*"}
              </Button>
            </form>
            {!submissionSuccess && (
              <span className={"my-3"}>
                *Mit dem Bestätigen der Registrierung stimme ich den unten vorausgewählten Tarifen
                zu, und akzeptiere die AGB (Todo hinterlegen). Nach der Registrierung können Sie
                eine Zahlungsmethode hinterlegen. Erst nach Hinterlegung eines Zahlungsmittel wird
                die Karte aktiviert und kann zum Laden verwendet werden.
              </span>
            )}

            {submissionSuccess && (
              <div className="mt-4 opacity-100 transition-all">
                <p>Danke für Ihre Registrierung!</p>
                <p>
                  Zur Bestätigung der E-Mail Adresse wurden eine E-Mail an {watchedFields.email}{" "}
                  gesendet.
                </p>
              </div>
            )}
          </div>
        ))}
    </>
  );
};

export default ActivateForm;
