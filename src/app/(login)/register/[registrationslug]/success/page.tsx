import React from "react";

import prisma from "~/server/db/prisma";
import { ouColors, ouLogos } from "~/styles/oucolors/oucolors";
import ActivateForm from "~/app/(login)/register/[registrationslug]/form";
import { env } from "~/env";
import Image from "next/image";
import StyledLink from "~/app/(app)/util/StyledLink";
import { getCPOOu } from "~/utils/register/whitelabelHelper";
interface Params {
  registrationslug: string;
}

const SuccessPage = async ({ params }: { params: Params }) => {
  const cpoOu = await getCPOOu(params?.registrationslug);

  const getCompanyLogo = (ouname: string): string => {
    const defaultLogo = ouLogos?.Default?.logo ?? "";
    if (Object.keys(ouLogos).includes(ouname)) {
      return ouLogos[ouname]?.logo ?? defaultLogo;
    }
    return defaultLogo;
  };

  const getBackgroundColor = (ouName?: string) => {
    if (!ouName) {
      return "";
    }

    if (ouColors[ouName]) {
      const oucol = ouColors[ouName];
      if (oucol) {
        return oucol;
      }
    }
    const oucol = ouColors.Default;
    if (oucol) {
      return oucol;
    }
    return "";
  };
  return (
    <div
      style={getBackgroundColor(cpoOu?.name)}
      className="flex w-full justify-center text-primary"
    >
      <div className="w-full rounded-xl p-6  shadow-lg sm:p-10 ">
        <div
          className={"flex flex-col-reverse items-center gap-1 text-center md:flex-row md:gap-4"}
        >
          <h2 className="mb-0  font-bold text-primary">Email efolgreich verifiziert</h2>
          <Image
            src={getCompanyLogo(cpoOu?.name ?? "") ?? ""}
            priority={false}
            width={170}
            height={150}
            alt={`${cpoOu?.name} Logo`}
            className={
              "inline-block h-full max-h-12 max-w-full transition-all duration-200 ease-soft-in-out dark:hidden"
            }
          />
        </div>
        <p className="mb-6">
          Ihre Email Adresse wurde erfolgreich verifiziert. Das Benutzerkonto ist nun
          freigeschaltet.
        </p>
        <p>Sie können sich nun in unserem Portal mit E-Mail-Adresse und Passwort einloggen.</p>

        <StyledLink className={"font-bold sm:w-72"} href={`${env.NEXT_PUBLIC_SITE_URL}/login`}>
          Hier gehts zum Login
        </StyledLink>
      </div>
    </div>
  );
};

export default SuccessPage;
