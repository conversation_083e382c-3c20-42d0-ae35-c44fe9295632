import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { toDataURL } from "qrcode";
import <PERSON><PERSON> from "jimp";

export const GET = async (request: NextRequest) => {
  const { searchParams } = new URL(request.url);
  let evse = searchParams.get("evse");

  // Remove '.png' from the evse if present
  if (evse?.endsWith(".png")) {
    evse = evse.slice(0, -4);
  }

  const qrDataUrl = await toDataURL(`https://adhoc.eulektro.de/qr/${evse}`, {
    errorCorrectionLevel: "H",
    margin: 0,
  });
  const qrImage = await Jimp.read(
    Buffer.from(qrDataUrl.replace(/^data:image\/png;base64,/, ""), "base64"),
  );
  let logoImage = await Jimp.read("./public/logo/Ladeeule_Bildmarke.png");

  const qrWidth = qrImage.bitmap.width;
  const logoWidth = logoImage.bitmap.width;

  // define white border size
  const borderSize = 4;

  // resize logo to fit in the QR Code
  const scaleFactor = qrWidth / logoWidth / 5; // increase the division factor to make the logo smaller
  logoImage.scale(scaleFactor);

  // create a white background
  const whiteBackground = new Jimp(
    logoImage.bitmap.width + borderSize * 2,
    logoImage.bitmap.height + borderSize * 2,
    "#FFFFFFFF",
  ); // Added border for 2 pixel border
  whiteBackground.blit(logoImage, borderSize, borderSize); // put the logo on the white background with border
  logoImage = whiteBackground; // update the logo image

  const logoPosition = (qrWidth - logoImage.bitmap.width) / 2; // updated the calculation of logoPosition

  qrImage.blit(logoImage, logoPosition, logoPosition);

  const imageBuffer = await qrImage.getBufferAsync(Jimp.MIME_PNG);

  return new NextResponse(imageBuffer, {
    headers: {
      "Content-Type": "image/png",
      //"Content-Disposition": `attachment; filename=${evse}.png`,
    },
  });
};
