import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import type { ApiResponse } from "~/types/api/apiType";
import { fetchMails } from "~/app/api/voltego/matcher/fetchMails";
import { fetchTransactions } from "~/app/api/voltego/matcher/fetchTransactions";

export async function GET(request: NextRequest): Promise<NextResponse> {
  const mailResult = await fetchMails();
  const transactionResult = await fetchTransactions();

  return NextResponse.json({
    status: "success",
    message: "Successfully process voltego invoices.",
    payload: {
      mailResult,
      transactionResult,
    },
  } as ApiResponse<{ mailResult: any; transactionResult: any }>);
}
