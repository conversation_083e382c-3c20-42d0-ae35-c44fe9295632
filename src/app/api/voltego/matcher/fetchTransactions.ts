import axios from "axios";
import path from "path";
import fs from "fs";
import FormData from "form-data";
import { env } from "~/env";
import { getDocument } from "pdfjs-dist";

const organizationId = env.QONTO_LOGIN;
const secretKey = env.QONTO_SECRET;
const iban = env.QONTO_MAIN_IBAN;

const pdfFolder = env.VOLTEGO_MATCHER_PDF_SAVE_FOLDER;

async function uploadPdfToQonto(transactionId: string, pdfFilePath: string): Promise<void> {
  const formData = new FormData();
  const data = fs.readFileSync(pdfFilePath);
  const fileName = path.basename(pdfFilePath);
  formData.append("file", data, fileName);

  const options = {
    method: "POST",
    url: `https://thirdparty.qonto.com/v2/transactions/${transactionId}/attachments`,
    headers: {
      "X-Qonto-Idempotency-Key": fileName, // Optional: ID to upload once if desired
      "Content-Type": `multipart/form-data; boundary=${(formData as any)._boundary}`,
      Accept: "application/json",
      Authorization: `${organizationId}:${secretKey}`,
    },
    data: formData,
  };

  try {
    const { data } = await axios.request(options);
    console.log(`PDF successfully uploaded to Qonto for transaction ${transactionId}`);
    console.log(data); // Optional: Output response data from Qonto
  } catch (error: any) {
    if (error.response) {
      // Server sent a response with an error status code (e.g. 4xx, 5xx)
      console.error("Error during request to Qonto:", error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error("No response from Qonto received:", error.request);
    } else {
      // Another error occurred
      console.error("An error occurred:", error.message);
    }
  }
}

async function checkIfRepaymentPdf(pdfFilePath: string): Promise<boolean> {
  try {
    const dataBuffer = fs.readFileSync(pdfFilePath);
    const loadingTask = getDocument({ data: dataBuffer });
    const pdfDocument = await loadingTask.promise;

    let fullText = "";
    for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
      const page = await pdfDocument.getPage(pageNum);
      const textContent = await page.getTextContent();
      const pageText = textContent.items.map((item) => ("str" in item ? item.str : "")).join(" ");
      fullText += pageText;
    }

    return fullText.includes("Stornorechnung für Stromlieferung");
  } catch (error: any) {
    if (error.code === "ENOENT") {
      console.error("File not found:", error.message);
    } else if (error instanceof SyntaxError) {
      console.error("Syntax error:", error.message);
    } else if (error instanceof TypeError) {
      console.error("Type error:", error.message);
    } else if (error instanceof RangeError) {
      console.error("Range error:", error.message);
    } else {
      console.error("Unknown error:", error);
    }
    return false;
  }
}

async function findRelevantPdf(reference: string): Promise<string | null> {
  const numPattern = /E-(\S+)/; // Pattern to find the specific number
  const match = numPattern.exec(reference);
  const files = fs.readdirSync(pdfFolder);
  const isRepayment = reference.includes("Rueckzahlung");

  const isInvoices = reference.includes("Rechnung");

  if (isRepayment) {
    const numPattern = /C1001(\S+)/; // Pattern to find the specific number
  }

  // Ensure 'specificNumber' is always correctly initialized
  let specificNumber = null;
  // ToDo handling for storno invoices
  if (match && isInvoices) {
    specificNumber = match[1];
    if (!specificNumber) {
      console.log("No specific number found in the transaction reference.");
      return null;
    }

    for (const file of files) {
      if (file.includes(specificNumber)) {
        const isRepaymentPdf = await checkIfRepaymentPdf(path.join(pdfFolder, file));
        if ((isRepayment && isRepaymentPdf) || (!isRepayment && !isRepaymentPdf)) {
          console.log(`Suitable PDF file found: ${file}`);
          return file; // Return the matching filename
        }
      }
    }
  }

  console.log(
    `No suitable PDF file found that matches the reference number and type: Repayment=${isRepayment}, specific number=${specificNumber}`,
  );
  return null; // Return 'null' if no file is found
}

async function uploadMissingAttachments(filteredTransactions: any[]): Promise<void> {
  for (const transaction of filteredTransactions) {
    const pdfFileName = await findRelevantPdf(transaction.reference);
    if (pdfFileName) {
      const pdfFilePath = path.join(pdfFolder, pdfFileName);
      if (fs.existsSync(pdfFilePath)) {
        await uploadPdfToQonto(transaction.id, pdfFilePath);
      } else {
        console.log(`PDF file for transaction ${transaction.id} not found: ${pdfFileName}`);
      }
    } else {
      console.log(`No PDF file found for transaction ${transaction.id}.`);
    }
  }
}

async function fetchTransactions(): Promise<void> {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 40);
  const startDateString = startDate.toISOString().split("T")[0]; // Date 30 days ago
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 1); // Set the end date to tomorrow
  const endDateString = endDate.toISOString().split("T")[0]; // Convert the date to ISO format

  const options = {
    method: "GET",
    url: `https://thirdparty.qonto.com/v2/transactions?iban=${iban}&settled_at_from=${startDateString}&settled_at_to=${endDateString}`,
    headers: {
      Accept: "application/json, text/plain",
      Authorization: `${organizationId}:${secretKey}`,
    },
  };

  try {
    const { data } = await axios.request(options);
    if (data && data.transactions) {
      const filteredTransactions = data.transactions.filter((transaction: any) => {
        // Filter transactions without income object
        if (!transaction.label) {
          return false;
        }
        // Further filtering, e.g. by IBAN
        return transaction.label === "Voltego GmbH";
      });

      // Total amount of filtered transactions
      const totalAmount = filteredTransactions.reduce(
        (sum: number, transaction: any) => sum + parseFloat(transaction.amount),
        0,
      );

      // Total amount of transactions with attachments
      const totalWithAttachments = filteredTransactions.reduce((sum: number, transaction: any) => {
        if (transaction.attachments && transaction.attachments.length > 0) {
          return sum + parseFloat(transaction.amount);
        }
        return sum;
      }, 0);

      // Total amount of transactions without attachments
      const totalWithoutAttachments = filteredTransactions.reduce(
        (sum: number, transaction: any) => {
          if (!transaction.attachments || transaction.attachments.length === 0) {
            return sum + parseFloat(transaction.amount);
          }
          return sum;
        },
        0,
      );

      console.log(`Number of transactions found: ${filteredTransactions.length}`);
      console.log(
        `Number of transactions with attachments: ${
          filteredTransactions.filter(
            (transaction: any) =>
              transaction.attachment_ids && transaction.attachment_ids.length > 0,
          ).length
        }`,
      );
      console.log(
        `Number of transactions without attachments: ${
          filteredTransactions.filter(
            (transaction: any) =>
              !transaction.attachment_ids || transaction.attachment_ids.length === 0,
          ).length
        }`,
      );

      await uploadMissingAttachments(
        filteredTransactions.filter(
          (transaction: any) =>
            !transaction.attachment_ids || transaction.attachment_ids.length === 0,
        ),
      );
    } else {
      console.log("No transactions found or incorrect data format received.");
    }
  } catch (error) {
    console.error("Error fetching transactions:", error);
  }
}

export { fetchTransactions };
