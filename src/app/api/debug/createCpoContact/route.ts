import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Überprüfen, ob bereits Contacts mit cpo=true existieren
    const existingCpoContacts = await prisma.contact.findMany({
      where: { cpo: true },
      select: {
        id: true,
        name: true,
        companyName: true,
        cpo: true,
      },
    });

    if (existingCpoContacts.length > 0) {
      return NextResponse.json({
        message: "CPO Contacts already exist",
        contacts: existingCpoContacts,
      });
    }

    // <PERSON><PERSON><PERSON><PERSON> eines Test-Contacts mit cpo=true
    const newContact = await prisma.contact.create({
      data: {
        name: "Test CPO Contact",
        companyName: "Test CPO Company",
        cpo: true,
        invoiceMail: "<EMAIL>",
      },
    });

    return NextResponse.json({
      message: "Created new CPO contact",
      contact: newContact,
    });
  } catch (error) {
    console.error("Error creating CPO contact:", error);
    return NextResponse.json(
      { error: "Failed to create CPO contact" },
      { status: 500 }
    );
  }
}
