import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import * as z from "zod";
import { ContactType } from "~/server/types/types";
import { Cdr, CdrPayout, Contact } from "@prisma/client";

const requestSchema = z.object({
  startDate: z.string().transform((value) => new Date(value)),
  endDate: z.string().transform((value) => new Date(value)),
  kindOfContact: z.nativeEnum(ContactType),
});

export type ContactWithNumTarifs = Contact & { numTarifs: number };

export async function POST(request: NextRequest) {
  const requestBody = await request.json();

  // ZOD-Validierung
  const validationResult = requestSchema.safeParse(requestBody);
  if (!validationResult.success) {
    return NextResponse.json(validationResult.error.message);
  }
  const { startDate, endDate, kindOfContact } = validationResult.data;

  if (!startDate || !endDate) return NextResponse.json("no start or end date provided");

  let cdrs;
  if (kindOfContact === ContactType.CPO) {
    cdrs = await prisma.cdr.findMany({
      select: {
        OU_Code: true,
      },
      distinct: ["OU_Code"],
      where: {
        Start_datetime: { gte: startDate },
        End_datetime: { lte: endDate },
        creditInvoiceId: null,
        billable: true,
      },
    });
    if (cdrs && cdrs.length > 0) {
      const ouIds = cdrs.map((cdr) => cdr.OU_Code);
      // sucht alle contacts (CPOs) die passend zu den gefundenen CDRs mindestens einen CDR haben
      // im angegebenen Zeitraum, inklusive wie viele tarife für den CPO gemapped sind und ob
      // ein die tarife im zeitraum des cdrs gültig sein
      const sqlStart = startDate.toISOString().split("T")[0];
      const sqlEnd = endDate.toISOString().split("T")[0];
      const contacts: ContactWithNumTarifs[] = await prisma.$queryRawUnsafe(
        `SELECT CreditTarif.validTo, Contact.*, CAST(count(ContactCreditTarif.creditTarifId) AS INTEGER) as numTarifs
         from Contact
                  left join Ou on Ou.id = Contact.ouId
                  left join ContactCreditTarif on Contact.id = ContactCreditTarif.contactId
                  left join CreditTarif on ContactCreditTarif.creditTarifId = CreditTarif.id
         where Ou.code in (${ouIds})
           and Contact.cpo = true
           and (((CreditTarif.validFrom between '${sqlStart}' and '${sqlEnd}' ) or CreditTarif.validFrom  <= '${sqlStart}')
           and (((CreditTarif.validTo between '${sqlStart}' and '${sqlEnd}' ) or CreditTarif.validTo  >= '${sqlEnd}') or CreditTarif.validTo is null))
         group by contactId`,
      );

      //Number cast becasue prisma bug which returns bigint which breaks json
      return NextResponse.json(
        contacts.map((contact) => {
          return { ...contact, numTarifs: Number(contact.numTarifs) };
        }),
      );
    }
    return NextResponse.json([]);
  } else {
    cdrs = await prisma.cdr.findMany({
      select: {
        Service_Provider_ID: true,
      },
      distinct: ["Service_Provider_ID"],
      where: {
        Start_datetime: { gte: startDate },
        End_datetime: { lte: endDate },
        invoiceId: null,
        billable: true,
      },
    });

    const providerList = cdrs.map((cdr) => {
      const countryCode = cdr.Service_Provider_ID?.slice(0, 2);
      const providerId = cdr.Service_Provider_ID?.slice(2);
      return { providerCountryId: countryCode, providerId: providerId };
    });

    const contacts = await prisma.contact.findMany({
      where: {
        providers: {
          some: {
            OR: providerList.map((provider) => ({
              providerId: provider.providerId,
              providerCountryId: provider.providerCountryId,
            })),
          },
        },
      },
      // Include weitere Felder oder Relationen nach Bedarf
    });
    return NextResponse.json(contacts);
  }
}
