import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
  const { contact, tarif, subscribed } = await request.json();

  try {
    if (subscribed) {
      await prisma.contact.update({
        where: {
          id: contact.id,
        },
        data: {
          creditTarifs: {
            create: {
              creditTarifId: tarif.id,
            },
          },
        },
      });
    } else {
      await prisma.contactCreditTarif.delete({
        where: {
          contactId_creditTarifId: {
            creditTarifId: tarif.id,
            contactId: contact.id,
          },
        },
      });
    }
    return NextResponse.json(contact);
  } catch (err) {
    return NextResponse.json(err, { status: 500 });
  }
}
