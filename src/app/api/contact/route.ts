import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { CdrMapping, Provider } from "@prisma/client";

export async function GET(request: NextRequest) {
  const contacts = await prisma.contact.findMany({ include: { providers: true } });

  return NextResponse.json(contacts);
}

/**
 * For creating and updating contacts
 * @param request
 * @constructor
 */
export async function POST(request: NextRequest) {
  const { contact } = await request.json();

  if (contact?.contactAddress) {
    delete contact.contactAddress;
  }

  if (contact?.contactEnergyResellers) {
    delete contact.contactEnergyResellers;
  }
  if (contact?.tarifs) {
    delete contact.tarifs;
  }
  try {
    let contactId = "";
    const { providers, ...contactWithoutProviders } = contact;
    const providerObjs = providers?.map((provider: string) => {
      const [country, id] = provider.split("*");
      return { providerCountryId: country, providerId: id };
    });
    if (contact?.id) {
      contactId = contact.id;
      const updatedContact = await prisma.contact.update({
        where: {
          id: contact?.id || null,
        },
        data: {
          ...contactWithoutProviders,
          cdrMapping: contactWithoutProviders.cpo
            ? CdrMapping.Standard_Credit
            : CdrMapping.Standard,
        },
      });
      if (updatedContact) {
        await prisma.provider.updateMany({
          where: { contactId: contact.id },
          data: { contactId: null },
        });
      }
    } else {
      const newContact = await prisma.contact.create({
        data: contactWithoutProviders,
      });
      contactId = newContact.id;
    }

    await prisma.provider.updateMany({
      where: {
        OR: providerObjs.map((provider: Provider) => ({
          AND: [
            { providerId: provider.providerId },
            { providerCountryId: provider.providerCountryId },
          ],
        })),
      },
      data: { contactId: contactId },
    });
  } catch (err) {
    return NextResponse.json(err);
  }
  return NextResponse.json(contact);
}
