import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
  const payload = await request.json();
  const { contact, address } = payload;

  try {
    const defaultValidFrom = new Date("2023-01-01");
    const defaultValidTo = new Date("2100-01-01");

    if (address?.id) {
      await prisma.contactAddress.update({
        where: {
          id: address.id,
        },
        data: {
          ...address,
          validFrom: address.validFrom ? new Date(address.validFrom) : defaultValidFrom,
          validTo: address.validTo ? new Date(address.validTo) : defaultValidTo,
        },
      });
    } else {
      await prisma.contact.update({
        where: {
          id: contact.id,
        },
        data: {
          contactAddress: {
            create: {
              ...address,
              validFrom: address.validFrom ? new Date(address.validFrom) : defaultValidFrom,
              validTo: address.validTo ? new Date(address.validTo) : defaultValidTo,
            },
          },
        },
      });
    }
    return NextResponse.json(address);
  } catch (err) {
    return NextResponse.json(err, { status: 500 });
  }
}
