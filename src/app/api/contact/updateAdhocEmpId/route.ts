import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { z } from "zod";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

const UpdateAdhocEmpIdSchema = z.object({
  contactId: z.string(),
  adhocEmpId: z.string(),
});

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const validated = UpdateAdhocEmpIdSchema.safeParse(body);

    if (!validated.success) {
      return NextResponse.json({ error: validated.error.format() }, { status: 400 });
    }

    const { contactId, adhocEmpId } = validated.data;

    // Überprüfen, ob der Contact existiert und cpo=true ist
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    if (!contact.cpo) {
      return NextResponse.json({ error: "Only CPO contacts can be mapped to EMPs" }, { status: 400 });
    }

    // Aktualisieren der adhocEmpId des Contacts
    const updatedContact = await prisma.contact.update({
      where: { id: contactId },
      data: { adhocEmpId },
      select: {
        id: true,
        name: true,
        companyName: true,
        adhocEmpId: true,
      },
    });

    return NextResponse.json(updatedContact);
  } catch (error) {
    console.error("Error updating contact adhocEmpId:", error);
    return NextResponse.json(
      { error: "Failed to update contact adhocEmpId" },
      { status: 500 }
    );
  }
}
