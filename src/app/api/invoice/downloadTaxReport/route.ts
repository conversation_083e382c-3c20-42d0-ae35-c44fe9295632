import type { NextRequest } from "next/server";
import fs, { readFileSync } from "fs";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { z } from "zod";

import { env } from "~/env.js";
import { createObjectCsvWriter } from "csv-writer";
import { checkAndCreateDir, getTypeOfInvoiceShortcut } from "~/utils/invoice/invoiceHelper";
import { getDateStringFromDateObject } from "~/utils/date/date";
import { KindOfInvoice } from "@prisma/client";
import { ISO3166_country_codes } from "~/utils/format/countrycodes";

const DateSchema = z.object({
  startDate: z.string().refine((date) => /^\d{4}-\d{2}-\d{2}$/.test(date), {
    message: "Das Startdatum muss im Format yyyy-mm-dd vorliegen.",
  }),
  endDate: z.string().refine((date) => /^\d{4}-\d{2}-\d{2}$/.test(date), {
    message: "Das Enddatum muss im Format yyyy-mm-dd vorliegen.",
  }),
});
type SelectedResult = {
  invoiceDate: Date;
  servicePeriodFrom: Date;
  servicePeriodTo: Date;
  kindOfInvoice: KindOfInvoice;
  invoiceNumber?: string;
  customerNumber?: string;
  sumGross: number;
  sumTax: number;
  companyName?: string;
  street?: string;
  streetNr?: string;
  city?: string;
  country?: string;
  zip?: string;
  ustId?: string;
  validFrom: Date;
  supplierNumber?: string;
};

export async function GET(request: NextRequest) {
  const start = request.nextUrl.searchParams.get("startDate");
  const end = request.nextUrl.searchParams.get("endDate");
  const result = await DateSchema.safeParse({ startDate: start, endDate: end });

  if (result.success) {
    const startDate = new Date(result.data.startDate.toString());
    startDate.setUTCHours(0, 0, 0, 0);
    const endDate = new Date(result.data.endDate.toString());
    endDate.setUTCHours(23, 59, 59, 999);

    const sql = `
            SELECT inv.invoiceDate,
                   inv.servicePeriodFrom,
                   inv.servicePeriodTo,
                   inv.kindOfInvoice,
                   inv.invoiceNumber,
                   contact.customerNumber,
                   contact.supplierNumber,
                   inv.sumGross,
                   inv.sumTax,
                   contact.companyName,
                   addr.street,
                   addr.streetNr,
                   addr.city,
                   addr.country,
                   addr.zip,
                   addr.ustId,
                   addr.validFrom
            FROM Invoice inv
                     JOIN
                 Contact contact ON inv.contactId = contact.id
                     LEFT JOIN
                 ContactAddress addr ON contact.id = addr.contactId
            WHERE addr.validFrom <= inv.invoiceDate
              AND addr.validTo >= inv.invoiceDate
              AND inv.createDate BETWEEN '${start}' AND '${end}';`;

    const invoices: SelectedResult[] = await prisma.$queryRawUnsafe(sql);

    if (invoices) {
      let summe = 0;
      const csv_steuerberater = invoices.map((invoice) => {
        summe += invoice?.sumGross ?? 0;

        let debitorennummer = "";
        if (
          invoice.kindOfInvoice == KindOfInvoice.CREDIT ||
          invoice.kindOfInvoice == KindOfInvoice.CREDIT_STORNO
        ) {
          debitorennummer = invoice.supplierNumber?.split("-")[1] ?? "";
        } else {
          const [_, year, number] = invoice.customerNumber?.split("-") ?? [];

          debitorennummer = `${year?.substring(2, 4)}${number}`;
        }

        let gegenkonto = "";
        if (invoice.country?.trim() == "Deutschland") {
          gegenkonto = !invoice.sumTax ? "8337" : "8400";
        }

        return {
          invoiceDate: getDateStringFromDateObject(invoice.invoiceDate),
          deliveryDate: `${getDateStringFromDateObject(
            invoice.servicePeriodFrom,
          )} - ${getDateStringFromDateObject(invoice.servicePeriodTo)}`,
          startDate: getDateStringFromDateObject(invoice.servicePeriodFrom),
          endDate: getDateStringFromDateObject(invoice.servicePeriodTo),
          art: getTypeOfInvoiceShortcut(invoice.kindOfInvoice),
          belegnummer1: invoice?.invoiceNumber?.replaceAll("_", "-"),
          belenummer2: "",
          kundennummer:
            invoice.kindOfInvoice == KindOfInvoice.CREDIT ||
            invoice.kindOfInvoice == KindOfInvoice.CREDIT_STORNO
              ? invoice.supplierNumber
              : invoice.customerNumber,
          debnummer: debitorennummer,
          whrg: "EUR",
          gesamt:
            invoice.kindOfInvoice == KindOfInvoice.CREDIT ||
            invoice.kindOfInvoice == KindOfInvoice.CREDIT_STORNO
              ? (invoice?.sumGross * -1).toFixed(2)
              : invoice.sumGross.toFixed(2),
          gegenkonto: gegenkonto,
          leer: "",
          salutation: "Firma",
          customerName: invoice.companyName,
          street: `${invoice.street} ${invoice.streetNr}`,
          zip: invoice.zip,
          city: invoice.city,
          country: invoice.country,
          countryCode: invoice.country ? ISO3166_country_codes[invoice.country.trim()] : "--",
          taxid: invoice.ustId,
          address_valid_since: getDateStringFromDateObject(invoice.validFrom),
        };
      });

      const csv_head = [
        { id: "invoiceDate", title: "Datum Rechnung" },
        { id: "deliveryDate", title: "Liefer-/Leistungsdatum" },
        { id: "startDate", title: "Startdatum" },
        { id: "endDate", title: "Enddatum" },
        { id: "art", title: "Art" },
        { id: "belegnummer1", title: "Belegnr.1" },
        { id: "belenummer2", title: "Belegnr.2" },
        { id: "kundennummer", title: "Kd.-Nr." },
        { id: "debnummer", title: "Deb.-Nr." },
        { id: "whrg", title: "Whrg" },
        { id: "gesamt", title: "Gesamt" },
        { id: "gegenkonto", title: "Gegenkonto" },
        { id: "leer", title: "" },
        { id: "salutation", title: "Anrede" },
        { id: "customerName", title: "Kunde" },
        { id: "street", title: "Straße und Hausnummer" },
        { id: "zip", title: "PLZ" },
        { id: "city", title: "Ort" },
        { id: "country", title: "Land" },
        { id: "countryCode", title: "Länderkennung" },
        { id: "taxid", title: "Ust-ID Kunde" },
        { id: "address_valid_since", title: "Adresse gültig seit" },
      ];

      const filename = `eulektro_export_roaming_${start}_bis_${end}.csv`;
      const path = `${env.ROAMING_TAX_EXPORT_DIR}/${filename}`;

      const csvWriter = createObjectCsvWriter({
        path: path,
        header: csv_head,
      });
      checkAndCreateDir(env.ROAMING_TAX_EXPORT_DIR);
      try {
        await csvWriter.writeRecords(csv_steuerberater);
      } catch (e) {
        console.log("Error");
      }

      // Prüfen, ob die Datei existiert
      if (fs.existsSync(path)) {
        // Read file in order to add the BOM header that excel can read Ä Ö ß etc..
        const data = readFileSync(path, "utf8");
        const bom = "\ufeff";

        fs.writeFileSync(path, bom + data, { encoding: "utf8" });
        const fileStream = readFileSync(path, "utf8");
        return new NextResponse(fileStream, {
          headers: {
            "Content-Type": "text/csv",
            "Content-Disposition": `attachment; filename=${filename}`,
          },
        });
      } else {
        // Fehlermeldung, wenn die Datei nicht gefunden wurde
        NextResponse.json({ status: 404 });
      }
    }
  } else {
    NextResponse.json("invalid params", { status: 500 });
  }
}
