import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { KindOfInvoice } from "@prisma/client";
import path from "path";
import { checkAndCreateDir, createCdrCsv, getSha1 } from "~/app/api/invoice/finish/[slug]/route";
import { env } from "~/env";

interface Props {
  params: {
    slug: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const invoiceId = params.slug;

  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: {
      contact: {
        include: {
          contactAddress: true,
          providers: true, // Diese <PERSON> hi<PERSON>ufü<PERSON>
        },
      },
      user: { include: { address: true, ou: true } },
      invoiceChilds: true,
      invoiceParent: true,
      invoicePositions: {
        include: {
          tarif: true,
        },
      },
      files: true,
      creditCdrs: { include: { creditPayout: true } },
      cdrs: {
        include: {
          cost: true,
        },
      },
      contract: true,
    },
  });
  const currentYear = new Date().getFullYear();
  let dir = "";
  if (invoice?.contact) {
    const provider_name =
      invoice?.contact?.name?.replaceAll(" ", "_").toLowerCase() ?? "unknown_contact_name";
    dir = `${env.INVOICE_FOLDER}/${currentYear}/${provider_name}`;
  } else if (invoice?.user) {
    const userOuName = invoice.user?.ou?.name;
    dir = `${env.INVOICE_FOLDER}/${currentYear}/${userOuName}/user`;
  }

  checkAndCreateDir(dir);

  if (invoice?.kindOfInvoice == KindOfInvoice.INVOICE) {
    const cdrFileName = await createCdrCsv(invoice, dir);

    await prisma.fileRef.create({
      data: {
        name: cdrFileName,
        path: path.join(dir, cdrFileName),
        sha1: getSha1(path.join(dir, cdrFileName)),
        invoiceId: invoice.id,
        contentType: "text/csv",
      },
    });
  }

  return NextResponse.json(invoice);
}
