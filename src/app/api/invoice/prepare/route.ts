import type { NextRequest } from "next/server";
import type { InvoiceFormValues } from "~/app/(app)/invoice/create/page";
import { prepareInvoice } from "~/server/invoice/invoiceUtils";
import { Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  try {
    const { contactIds, contactType, dateRange, rangeMod } =
      (await request.json()) as InvoiceFormValues;

    if (!contactIds || contactIds.length === 0) {
      return new Response("No contact IDs provided", { status: 400 });
    }

    const results = await Promise.allSettled(
      contactIds.map((contactId) =>
        prepareInvoice({
          contactId,
          contactType,
          dateRange,
          rangeMod,
        }),
      ),
    );

    const successfulInvoices = results.filter(
      (result) =>
        result.status === "fulfilled" &&
        result.value && // Sicherstellen, dass value vorhanden ist
        "id" in result.value, // Prüfen, ob die id-Eigenschaft existiert
    );

    const errors = results.filter((result) => result.status === "rejected");

    if (successfulInvoices.length > 0) {
      return new Response(
        JSON.stringify({
          message: "Some or all invoices were successfully created.",
          successCount: successfulInvoices.length,
          errorCount: errors.length,
        }),
        { status: 200, headers: { "Content-Type": "application/json" } },
      );
    } else {
      return new Response(
        JSON.stringify({
          message: "No invoices were created.",
          errorCount: errors.length,
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  } catch (error) {
    console.error(error);
    return new Response("Server error", { status: 500 });
  }
}
