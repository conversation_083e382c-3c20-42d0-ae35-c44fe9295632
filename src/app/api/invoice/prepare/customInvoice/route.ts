import { NextRequest, NextResponse } from "next/server";

import { ClientInvoiceFormData } from "~/app/(app)/invoice/component/CustomInvoiceForm";
import prisma from "~/server/db/prisma";
import { Prisma } from "@prisma/client";
import { prepareInvoiceByPositions } from "~/server/invoice/invoiceUtils";

export async function POST(request: NextRequest) {
  const { invoicePositions, startDate, endDate, contactId, totalSumNet, totalSumGross, kindOfInvoice } =
    (await request.json()) as ClientInvoiceFormData;

  if (!contactId) {
    return new Response("no contact found", { status: 500 });
  }

  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  const invoice = await prepareInvoiceByPositions({
    contactId,
    invoicePositions,
    startDate: startDateObj,
    endDate: endDateObj,
    totalSumNet,
    totalSumGross,
    kindOfInvoice,
  });
  if (invoice && invoice?.id) {
    return NextResponse.json({ invoiceId: invoice?.id });
  }

  return new Response("no invoice", { status: 500 });
}

/*export async function GET(request: NextRequest) {
  const cpoContract = await prisma?.cPOContract.findUnique({
    where: { id: "cej20rf3n2903eo" },
    include: { contractItems: true },
  });

  const evses = await prisma?.evse.findMany({ where: { ouId: cpoContract.ouId } });
  const cards = await prisma?.eMPCard.findMany({
    where: { invoiceId: null },
    include: { tarifs: { include: { tarif: { include: { ou: true } } } } },
  });

  const tarifsWithOu = cards
    .flatMap((userCard) => userCard.tarifs.map((tarif) => tarif.tarif))
    .filter(
      (
        tarif,
      ): tarif is Prisma.CompanyTarifGetPayload<{
        include: { ou: true };
      }> => tarif.ouId == cpoContract.ouId,
    );
  const numCards = tarifsWithOu.length;
  console.log("xxx");

  const xx = cpoContract.contractItems.map((contractItem) => {
    if (contractItem.type == ContractItemType.AC_HOTLINE) {
      return;
    }
  });
}*/
