import type { NextRequest } from "next/server";
import fs from "fs";

import nodemailer from "nodemailer";
import prisma from "~/server/db/prisma";
import { Prisma, Role } from "@prisma/client";
import { env } from "~/env.js";
import { getMitarbeiterClubInvoiceMailMessage } from "~/app/api/invoice/sendMail/InvoiceMail";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  const { invoiceId } = await request.json();

  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: {
      files: true,
      contact: true,
      user: true,
    },
  });
  if (!invoice || !invoice.files.find((x) => x.contentType == "application/pdf")) {
    return new Response("no invoice found", { status: 500 });
  }

  const transporter = nodemailer.createTransport({
    port: 465,
    host: env.EMAIL_SERVER_HOST,
    auth: {
      user: env.EMAIL_SERVER_USER,
      pass: env.EMAIL_SERVER_PASSWORD,
    },
    secure: true,
  });

  let attachments = invoice.files.map((file) => {
    return {
      filename: file.name,
      content: fs.createReadStream(file.path),
      contentType: file?.contentType || "application/octet-stream",
    };
  });

  attachments = attachments.filter((x) => x.contentType == "application/pdf");

  const [subject, message] = getMitarbeiterClubInvoiceMailMessage({
    invoice: invoice,
  });

  if (subject == "" || message == "") {
    return new Response("Warning - no subject or message - mail not sent", { status: 500 });
  }
  const mailAddress = invoice.user?.email;
  const invoiceMailData = {
    from: env.EMAIL_FROM,
    to: env.NODE_ENV == "production" ? mailAddress : "<EMAIL>",
    replyTo: "<EMAIL>",
    bcc: "<EMAIL>",
    subject: subject,
    text: message,
    attachments: attachments,
  };
  let sendStatus = "prepare";
  let history = "";
  const now = new Date().toLocaleString();
  transporter.sendMail(invoiceMailData, (err: any, info: any) => {
    if (err) {
      console.log(err);
      history += `\n\n${now}\nFail to send User Invoice to ${mailAddress}`;
      sendStatus = "fail";
    } else {
      console.log(info);
      history += `\n\n${now}\nMail sent to ${mailAddress}`;
    }
    return;
  });

  await prisma.invoice.update({
    where: {
      id: invoice.id,
    },
    data: {
      sendAsMail: true,
      history: invoice.history ? invoice.history + history : history,
    },
  });
  if (sendStatus == "fail") {
    return new Response("Fehler beim Senden der Rechnung", { status: 500 });
  }
  return new Response("ok", { status: 200 });
}
