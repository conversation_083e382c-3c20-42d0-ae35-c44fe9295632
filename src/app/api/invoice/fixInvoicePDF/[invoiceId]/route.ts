import { NextRequest, NextResponse } from "next/server";

import prisma from "~/server/db/prisma";
import { createInvoicePDF } from "~/utils/invoice/createInvoicePDF";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { env } from "~/env";
import { checkAndCreateDir } from "~/app/api/invoice/finish/[slug]/route";

interface Props {
  params: {
    invoiceId: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  const invoiceId = params.invoiceId;
  if (!session || !invoiceId) {
    return NextResponse.json("Invalid");
  }
  const foundInvoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      contact: {
        include: {
          providers: true,
          contactAddress: true,
        },
      },
      user: { include: { address: true, ou: true } },
      cdrs: true,
      creditCdrs: true,
      invoicePositions: true,
      invoiceParent: true,
    },
  });
  if (foundInvoice) {
    let dir = "";
    const currentYear = new Date().getFullYear();
    if (foundInvoice.contact) {
      const provider_name =
        foundInvoice?.contact?.name?.replaceAll(" ", "_").toLowerCase() ?? "unknown_contact_name";
      dir = `${env.INVOICE_FOLDER}/${currentYear}/${provider_name}`;
    } else if (foundInvoice?.user) {
      const userOuName = foundInvoice.user.ou.name;
      dir = `${env.INVOICE_FOLDER}/${currentYear}/${userOuName}/user`;
    }

    checkAndCreateDir(dir);
    const path = await createInvoicePDF({ invoice: foundInvoice, dir: dir });
    return NextResponse.json({ state: "ok", path: path });
  }
  return NextResponse.json({ state: "error", message: "invoice not found" });
}
