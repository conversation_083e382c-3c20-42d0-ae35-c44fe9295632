import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { Prisma, Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export type InvoiceEndpointType = Prisma.InvoiceGetPayload<{
  include: {
    contact: {
      include: {
        contactAddress: true;
      };
    };
    files: true;
    invoicePositions: {
      include: {
        tarif: true;
      };
    };
    bankTransactions: true;
  };
}>;

export type ContactProviders = Prisma.ContactGetPayload<{
  include: {
    providers: true;
  };
}>;

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  const user = session?.user;
  if (!user || user?.role !== Role.CARD_HOLDER) {
    return new Response("no login or endpoint not available for role", { status: 404 });
  }
  const userOuId = user?.ou.id;
  if (!userOuId) {
    return new Response("No OU found for logged in user", { status: 404 });
  }
  const queryObject = {
    where: { userId: user.id, contactId: { equals: null } },
    include: {
      files: true,
      invoicePositions: {
        include: {
          tarif: true,
        },
      },
      user: true,
    },
  };

  const invoices = await prisma.invoice.findMany(queryObject);
  return NextResponse.json(invoices);
}
