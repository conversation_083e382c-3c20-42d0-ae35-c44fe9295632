import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { Prisma, Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export type InvoiceEndpointType = Prisma.InvoiceGetPayload<{
  include: {
    contact: {
      include: {
        contactAddress: true;
      };
    };
    files: true;
    invoicePositions: {
      include: {
        tarif: true;
      };
    };
    bankTransactions: true;
    creditCdrs: {
      select: {
        Volume: true;
      };
    };
  };
}>;

export type ContactProviders = Prisma.ContactGetPayload<{
  include: {
    providers: true;
  };
}>;

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  const userRole = session?.user?.role;
  if (!userRole || userRole !== Role.ADMIN) {
    return new Response("not login", { status: 402 });
  }
  const queryObject = {
    include: {
      contact: {
        include: {
          contactAddress: true,
        },
      },
      files: true,
      invoicePositions: {
        include: {
          tarif: true,
        },
      },
      user: true,
      bankTransactions: true,
      paymentIntent: true,
      creditCdrs: {
        select: {
          Volume: true,
        },
      },
    },
  };

  const invoices = await prisma.invoice.findMany(queryObject);
  return NextResponse.json(invoices);
}

export async function POST(request: NextRequest) {
  const { invoice } = await request.json();
  try {
    const data = await prisma.invoice.findUnique({
      where: {
        id: invoice,
      },
      include: {
        contact: true,
        invoicePositions: {
          include: {
            tarif: true,
          },
        },
      },
    });
  } catch (e) {
    return new Response("cant update", { status: 500 });
  }

  return new Response("ok", { status: 200 });
}
