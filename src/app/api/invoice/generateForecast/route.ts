import type { NextRequest } from "next/server";
import prisma from "../../../../server/db/prisma";
import type { CdrWithIncludes } from "~/server/invoice/calculateCost";

export interface ForecastReportEntry {
  contact?: string;
  Tariff_Name: string;
  kwh: number;
  sessions: number;
  price: number;
  invalidSessions: number;
  invalidKwh: number;
  energyCost: number;
  grossMargin: number;
}
interface GroupDataEntry {
  Volume: number;
  sessions: number;
  Calculated_Cost: number;
  invalidSessions: number;
  invalidKwh: number;
  Tariff_Name: string;
  EnergyCost: number;
  GrossMargin: number;
  kWhCost: number;
  sessionCost: number;
  description: "";
}

import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Prisma } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";

export const createForecastReportData = (cdrs: CdrWithIncludes[]): ForecastReportEntry[] => {
  // Gruppieren der Daten nach contactId und Owner

  const groupedData = cdrs.reduce((acc: { [key: string]: GroupDataEntry }, cdr) => {
    const tarifId = cdr.tarif?.id ? `${cdr.tarif.id}` : "";
    const empId = cdr.Service_Provider_ID;

    const key = cdr.tarif ? `${empId}-${tarifId}` : `${empId} (not billed)`;

    if (!acc[key]) {
      acc[key] = {
        Volume: 0,
        sessions: 0,
        Calculated_Cost: 0,
        invalidSessions: 0,
        invalidKwh: 0,
        Tariff_Name: "",
        EnergyCost: 0,
        GrossMargin: 0,
        kWhCost: 0,
        sessionCost: 0,
        description: "",
      };
    }

    if (!cdr.billable) {
      acc[key]!.invalidSessions++;
      acc[key]!.invalidKwh += cdr.Volume ?? 0;
    } else {
      acc[key]!.Volume += cdr.Volume ?? 0;
      acc[key]!.sessions++;
      acc[key]!.Calculated_Cost += cdr.Calculated_Cost ?? 0;
    }
    acc[key]!.Tariff_Name = cdr.Tariff_Name ?? "";
    acc[key]!.EnergyCost += cdr?.cost?.cost ?? 0;

    return acc;
  }, {});

  const reportForecastData = Object.entries(groupedData).map(([key, value]) => {
    return {
      contact: key.split("-")[0],
      Tariff_Name: value.Tariff_Name,
      kwh: value.Volume,
      sessions: value.sessions,
      price: value.Calculated_Cost,
      invalidSessions: value.invalidSessions,
      invalidKwh: value.invalidKwh,
      energyCost: value.EnergyCost,
      grossMargin: value.Calculated_Cost - value.EnergyCost,
    };
  });

  return reportForecastData;
};

export async function GET(request: NextRequest) {
  const startDate = request.nextUrl.searchParams.get("startDate");
  const endDate = request.nextUrl.searchParams.get("endDate");

  const session = await getServerSession(authOptions);

  if (!startDate || !endDate || !session?.user?.selectedOu?.code) {
    return null;
  }
  const fromDateString = new Date(startDate.toString());
  fromDateString.setHours(0);
  const toDateString = new Date(endDate.toString());
  toDateString.setDate(toDateString.getDate() + 1);
  toDateString.setHours(0);

  //toDateString.setUTCHours(fromDateString.getTimezoneOffset() / 60);

  if (session.user.selectedOu.code != "0000") {
    const ouWithChildren = await getOusBelowOu(session.user.selectedOu);
    const ouCodes = ouWithChildren?.map((ou) => `'${ou.code}'`);
    const cdrs = await prisma.cdr.findMany({
      where: {
        End_datetime: {
          gte: fromDateString,
          lte: toDateString,
        },
        OU_Code: { in: ouCodes },
      },
      include: {
        cost: true,
        tarif: true,
      },
    });
    const total = createForecastReportData(cdrs);
    return NextResponse.json(total);
  } else {
    const cdrs = await prisma.cdr.findMany({
      where: {
        End_datetime: {
          gte: fromDateString,
          lte: toDateString,
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
    });
    const total = createForecastReportData(cdrs);

    return NextResponse.json(total);
  }
}
