import { type NextApiRequest, type NextApiResponse } from "next";

import prisma from "../../../server/db/prisma";

const generate = async (req: NextApiRequest, res: NextApiResponse) => {
  const startDate = new Date();
  const endDate = new Date();
  startDate.setUTCHours(0, 0, 0, 0);
  endDate.setUTCHours(23, 59, 59, 999);

  const today_cdrs = await prisma.cdr.findMany({
    where: {
      End_datetime: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      cost: true,
      tarif: true,
    },
  });

  //const today_calculatedCdrs = await computeRoamingCostByEmpPrice(today_cdrs);

  const todayRevenue = today_cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
  const todayEnergyCost = today_cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);

  const yesterday = new Date(
    startDate.getFullYear(),
    startDate.getMonth(),
    startDate.getDate() - 1,
  );

  const startDatetime = new Date(
    yesterday.getFullYear(),
    yesterday.getMonth(),
    yesterday.getDate(),
    0,
    0,
    0,
    0,
  );
  const endDatetime = new Date(
    yesterday.getFullYear(),
    yesterday.getMonth(),
    yesterday.getDate(),
    23,
    59,
    59,
    999,
  );

  const yesterday_cdrs = await prisma.cdr.findMany({
    where: {
      End_datetime: {
        gte: startDatetime,
        lte: endDatetime,
      },
    },
    include: {
      cost: true,
      tarif: true,
    },
  });

  //const yesterday_calculatedCdrs = await computeRoamingCostByEmpPrice(yesterday_cdrs);

  const yesterdayRevenue = yesterday_cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
  const yesterdayEnergyCost = yesterday_cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);

  res.status(200).json({
    todayRevenue: todayRevenue.toFixed(2),
    yesterdayRevenue: yesterdayRevenue.toFixed(2),
    todayEnergyCost: todayEnergyCost.toFixed(2),
    todayGrossMargin: (todayRevenue - todayEnergyCost).toFixed(2),
    yesterdayEnergyCost: yesterdayEnergyCost.toFixed(2),
    yesterdayGrossMargin: (yesterdayRevenue - yesterdayEnergyCost).toFixed(2),
  });
};

export default generate;
