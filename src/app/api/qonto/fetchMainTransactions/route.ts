import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { fetchMainTransactionsFromQonto } from "../../../../utils/qonto/transactions";
import { autoUploadInvoices } from "~/utils/qonto/uploader";

export async function GET(request: NextRequest) {
  const result = await fetchMainTransactionsFromQonto();

  if (result) {
    return NextResponse.json("success");
  }
  return NextResponse.json("error", { status: 500 });
}
