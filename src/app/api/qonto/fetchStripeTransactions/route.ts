import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { fetchStripeTransactionsFromQonto } from "../../../../utils/qonto/transactions";

export async function GET(request: NextRequest) {
  const result = await fetchStripeTransactionsFromQonto();
  if (result) {
    return NextResponse.json("success");
  }
  return NextResponse.json("error", { status: 500 });
}
