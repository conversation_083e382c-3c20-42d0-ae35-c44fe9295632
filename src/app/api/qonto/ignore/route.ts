import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";

import { z } from "zod";
import { LogType, Prisma } from "@prisma/client";
import Logger from "~/server/logger/logger";

/**
 * Endpoint will map multiple invoices to 1 transaction or multiple transactions to 1  invoice
 * @param request invoiceId(s), transactionId(s), multipleInvoicesFlag
 * @constructor
 */
export async function GET(request: NextRequest) {
  const transaction_id = request.nextUrl.searchParams.get("transaction_id");
  if (transaction_id) {
    let updatedRow;
    try {
      updatedRow = await prisma.qontoTransaction.update({
        where: { transaction_id: transaction_id },
        data: { ignore_for_mapping: true },
      });
      if (updatedRow) {
        return NextResponse.json("success", { status: 200 });
      } else {
        Logger(
          `Prisma Update Transaction: no updated row received`,
          "Prisma",
          "qonto",
          LogType.ERROR,
        );
        return NextResponse.json("Error updating DB", { status: 500 });
      }
    } catch (e) {
      if (e instanceof Prisma.PrismaClientKnownRequestError) {
        Logger(`Prisma Update Transaction failed ${e.message}`, "Prisma", "qonto", LogType.ERROR);
      } else {
        Logger(`Prisma Update Transaction failed `, "Prisma", "qonto", LogType.ERROR);
      }

      return NextResponse.json("Error updating transaction", {
        status: 500,
      });
    }
  }
  return NextResponse.json("No transaction_id provided", {
    status: 500,
  });
}
