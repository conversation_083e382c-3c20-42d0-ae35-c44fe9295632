import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const onlyStripe = request.nextUrl.searchParams.get("onlyStripe");
  const onlyUnpaid = request.nextUrl.searchParams.get("onlyUnpaid");
  const onlyForMapping = request.nextUrl.searchParams.get("onlyForMapping");

  if (onlyUnpaid) {
    const transactions = await prisma.qontoTransaction.findMany({
      where: {
        NOT: [
          { InvoiceToQontoTransaction: { some: {} } },
          { StripePayoutToQontoTransaction: { some: {} } },
        ],
      },
    });
    return NextResponse.json(transactions);
  }
  if (onlyForMapping) {
    const transactions = await prisma.qontoTransaction.findMany({
      where: {
        NOT: [
          { InvoiceToQontoTransaction: { some: {} } },
          { StripePayoutToQontoTransaction: { some: {} } },
        ],
        ignore_for_mapping: false,
      },
    });
    return NextResponse.json(transactions);
  }

  if (onlyStripe) {
    const transations = await prisma.qontoTransaction.findMany({
      where: {
        OR: [
          {
            label: {
              contains: "STRIPE",
            },
          },
          {
            label: {
              contains: "stripe",
            },
          },
          {
            label: {
              contains: "Stripe",
            },
          },
        ],
      },
    });
    return NextResponse.json(transations);
  } else {
    const transations = await prisma.qontoTransaction.findMany();
    return NextResponse.json(transations);
  }
}
