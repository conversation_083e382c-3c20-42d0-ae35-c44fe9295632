import { z } from "zod";

import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const MappingSchema = z.object({
  payoutId: z.string(),
  transactionId: z.string(),
});

export async function POST(request: NextRequest) {
  // Parse and validate the request payload
  const zodCheckedPayload = MappingSchema.safeParse(await request.json());
  if (zodCheckedPayload.success) {
    const { payoutId, transactionId } = zodCheckedPayload.data;

    try {
      const newMapping = await prisma.stripePayoutToQontoTransaction.create({
        data: {
          stripePayoutId: payoutId,
          qontoTransaction_id: transactionId,
        },
      });

      return NextResponse.json(newMapping);
    } catch (error) {
      Logger(
        "Error creating StripePayoutToQontoTransaction",
        "StripePayoutToQontoTransaction",
        LogType.ERROR,
      );
      return NextResponse.json("An error occurred while processing your request.", {
        status: 500,
      });
    }
  } else {
    Logger("Invalid request payload", "StripePayoutToQontoTransaction", LogType.ERROR);
    return NextResponse.json("Invalid request payload.", {
      status: 400,
    });
  }
}
