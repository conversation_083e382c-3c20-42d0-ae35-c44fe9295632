import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

import { fetchErrorsFromChargepoints, softResetTechnagons } from "~/utils/longship";

const job = CronJob(
  "api/cronjobs/softResetTechnagon",
  "0 0 * * * ", // (see https://crontab.guru/)
  async () => {
    Logger("Reseting all non charging technagon", "Error Monitoring", "cron", LogType.INFO);
    await softResetTechnagons();
  },
);

export const POST = job;
