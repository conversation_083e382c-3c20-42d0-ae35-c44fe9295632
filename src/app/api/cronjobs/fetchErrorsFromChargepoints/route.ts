import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

import { fetchErrorsFromChargepoints } from "~/utils/longship";

const job = CronJob(
  "api/cronjobs/fetchErrorsFromChargepoints",
  "*/30 * * * *", // (see https://crontab.guru/)
  async () => {
    Logger("fetch ErrorsList config values", "Error Monitoring", "cron", LogType.INFO);
    await fetchErrorsFromChargepoints();
  },
);

export const POST = job;
