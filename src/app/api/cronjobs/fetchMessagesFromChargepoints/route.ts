import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

import { fetchErrorsFromChargepoints, fetchMessagesFromChargepoints } from "~/utils/longship";

const job = CronJob(
  "api/cronjobs/fetchMessagesFromChargepoints",
  "*/30 * * * *", // (see https://crontab.guru/)
  async () => {
    Logger("fetchMessagesFromChargepoints", "Error Monitoring", "cron", LogType.INFO);
    await fetchMessagesFromChargepoints();
  },
);

export const POST = job;
