import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { findAllCsvFiles, importEnerchargeSessionCSV } from "~/utils/csv/enerchargeSessionCSV";
import { env } from "~/env";
import { fetchTransactionIDsFromSessionsByDirectPaymentTokenOfCPOContract } from "~/utils/transactionsync/synctransactionIds";

const job = CronJob(
  "api/cronjobs/syncLocalSessionsWithPortalSessions",
  "0 7 * * *", // (see https:", // (see https://crontab.guru/)
  async () => {
    Logger(
      `Look for Enercharge CSV Files in ${env.KEBA_BELEGE_FOLDER}`,
      "Fetch stock price from voltego",
      "cron",
      LogType.DEBUG,
    );

    const allCsv = findAllCsvFiles(env.KEBA_BELEGE_FOLDER);
    for (const csvpath of allCsv) {
      await importEnerchargeSessionCSV(csvpath);
    }

    await fetchTransactionIDsFromSessionsByDirectPaymentTokenOfCPOContract();
  },
);

export const POST = job;
