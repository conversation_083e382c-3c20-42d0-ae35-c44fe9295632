import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { sendMail } from "~/utils/email";
import { env } from "~/env";

const job = CronJob(
  "api/cronjobs/checkDGUVReminders",
  "0 8 * * 1", // Every Monday at 8 AM (see https://crontab.guru/)
  async () => {
    Logger("Checking DGUV-V3 reminders", "DGUV-V3 Reminder Check", "cron", LogType.INFO);
    
    try {
      // Get current date and date 30 days from now for early warning
      const now = new Date();
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(now.getDate() + 30);

      // Find DGUV-V3 maintenance records that are due soon or overdue
      const dueRecords = await prisma.maintenanceRecord.findMany({
        where: {
          category: {
            name: "DGUV-V3"
          },
          nextDueDate: {
            lte: thirtyDaysFromNow
          }
        },
        include: {
          category: true,
          location: {
            select: {
              id: true,
              name: true,
              city: true
            }
          },
          evse: {
            select: {
              uid: true,
              evse_id: true
            }
          },
          ou: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          nextDueDate: 'asc'
        }
      });

      if (dueRecords.length === 0) {
        Logger("No DGUV-V3 reminders due", "DGUV-V3 Reminder Check", "cron", LogType.INFO);
        return;
      }

      // Separate overdue and due soon
      const overdueRecords = dueRecords.filter(record => 
        record.nextDueDate && new Date(record.nextDueDate) < now
      );
      
      const dueSoonRecords = dueRecords.filter(record => 
        record.nextDueDate && new Date(record.nextDueDate) >= now
      );

      // Create notifications for overdue records
      for (const record of overdueRecords) {
        const targetDescription = getTargetDescription(record);
        const daysOverdue = Math.ceil((now.getTime() - new Date(record.nextDueDate!).getTime()) / (1000 * 60 * 60 * 24));
        
        await createSystemNotificationForAdmins({
          nachricht: `DGUV-V3 Prüfung überfällig: ${targetDescription} (${record.ou.name}) - ${daysOverdue} Tage überfällig`,
          type: NotificationType.ERROR
        });
      }

      // Create notifications for due soon records
      for (const record of dueSoonRecords) {
        const targetDescription = getTargetDescription(record);
        const daysUntilDue = Math.ceil((new Date(record.nextDueDate!).getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        await createSystemNotificationForAdmins({
          nachricht: `DGUV-V3 Prüfung fällig in ${daysUntilDue} Tagen: ${targetDescription} (${record.ou.name})`,
          type: NotificationType.WARNING
        });
      }

      // Send email summary if there are any due records
      if (dueRecords.length > 0) {
        await sendDGUVReminderEmail(overdueRecords, dueSoonRecords);
      }

      Logger(
        `Processed ${dueRecords.length} DGUV-V3 reminders (${overdueRecords.length} overdue, ${dueSoonRecords.length} due soon)`,
        "DGUV-V3 Reminder Check",
        "cron",
        LogType.INFO
      );

    } catch (error) {
      Logger(
        `Error checking DGUV-V3 reminders: ${error}`,
        "DGUV-V3 Reminder Check",
        "cron",
        LogType.ERROR
      );
      console.error("Error in DGUV-V3 reminder check:", error);
    }
  }
);

function getTargetDescription(record: any): string {
  switch (record.targetType) {
    case "LOCATION":
      return record.location?.name || "Unbekannter Standort";
    case "EVSE":
      return record.evse?.evse_id || "Unbekannte EVSE";
    case "CONNECTOR":
      return `${record.evse?.evse_id || "Unbekannte EVSE"} - Connector ${record.connectorId || "?"}`;
    case "ALL_EVSES":
      return "Alle Ladesäulen";
    default:
      return "Unbekannt";
  }
}

async function sendDGUVReminderEmail(overdueRecords: any[], dueSoonRecords: any[]) {
  try {
    const subject = `DGUV-V3 Prüfungen - ${overdueRecords.length} überfällig, ${dueSoonRecords.length} fällig`;
    
    let emailContent = `
      <h2>DGUV-V3 Prüfungen Erinnerung</h2>
      <p>Automatische Erinnerung für anstehende und überfällige DGUV-V3 Prüfungen:</p>
    `;

    if (overdueRecords.length > 0) {
      emailContent += `
        <h3 style="color: #dc2626;">Überfällige Prüfungen (${overdueRecords.length})</h3>
        <ul>
      `;
      
      for (const record of overdueRecords) {
        const targetDescription = getTargetDescription(record);
        const daysOverdue = Math.ceil((new Date().getTime() - new Date(record.nextDueDate!).getTime()) / (1000 * 60 * 60 * 24));
        
        emailContent += `
          <li style="color: #dc2626;">
            <strong>${targetDescription}</strong> (${record.ou.name}) - ${daysOverdue} Tage überfällig
            <br><small>Letzte Prüfung: ${new Date(record.date).toLocaleDateString('de-DE')}</small>
          </li>
        `;
      }
      
      emailContent += `</ul>`;
    }

    if (dueSoonRecords.length > 0) {
      emailContent += `
        <h3 style="color: #d97706;">Anstehende Prüfungen (${dueSoonRecords.length})</h3>
        <ul>
      `;
      
      for (const record of dueSoonRecords) {
        const targetDescription = getTargetDescription(record);
        const daysUntilDue = Math.ceil((new Date(record.nextDueDate!).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        
        emailContent += `
          <li style="color: #d97706;">
            <strong>${targetDescription}</strong> (${record.ou.name}) - fällig in ${daysUntilDue} Tagen
            <br><small>Letzte Prüfung: ${new Date(record.date).toLocaleDateString('de-DE')}</small>
          </li>
        `;
      }
      
      emailContent += `</ul>`;
    }

    emailContent += `
      <p>
        <a href="${env.NEXT_PUBLIC_SITE_URL}/maintenance">Zur Maintenance-Verwaltung</a>
      </p>
      <p><small>Diese E-Mail wurde automatisch generiert.</small></p>
    `;

    const textContent = `
DGUV-V3 Prüfungen Erinnerung

${overdueRecords.length} überfällige Prüfungen
${dueSoonRecords.length} anstehende Prüfungen

Link zur Maintenance-Verwaltung: ${env.NEXT_PUBLIC_SITE_URL}/maintenance
    `;

    await sendMail(
      "<EMAIL>",
      subject,
      textContent,
      emailContent
    );

  } catch (error) {
    console.error("Error sending DGUV reminder email:", error);
  }
}

export const POST = job;
