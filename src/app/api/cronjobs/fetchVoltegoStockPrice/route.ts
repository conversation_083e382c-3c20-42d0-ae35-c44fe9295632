import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import getExchangeElectricityPrice, { COPY_MODE } from "~/server/task/getExchangeElectricityPrice";

const job = CronJob(
  "api/cronjobs/fetchVoltegoStockPrice",
  "*/15 * * * *", // (see https://crontab.guru/)
  async () => {
    Logger("fetch stock price", "Fetch stock price from voltego", "cron", LogType.DEBUG);
    await getExchangeElectricityPrice(COPY_MODE.DELTA);
  },
);

export const POST = job;
