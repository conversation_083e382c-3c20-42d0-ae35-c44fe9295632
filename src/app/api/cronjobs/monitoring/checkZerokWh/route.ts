import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { checkZerokWh } from "~/utils/monitoring/chargepointErrors";

const job = CronJob(
  "api/cronjobs/monitoring/checkZerokWh",
  "*/30 * * * *", // (see https://crontab.guru/)
  async () => {
    Logger("Checking 0 kWh ", "Error Monitoring", "cron", LogType.INFO);
    await checkZerokWh();
  },
);

export const POST = job;
