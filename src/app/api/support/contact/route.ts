import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { NotificationType } from "@prisma/client";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import prisma from "~/server/db/prisma";

interface ContactRequest {
  name: string;
  email: string;
  subject: string;
  message: string;
  priority: "low" | "medium" | "high";
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  // Allow both authenticated and unauthenticated users to send contact messages
  
  try {
    const { name, email, subject, message, priority }: ContactRequest = await request.json();

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: "Alle Pflichtfelder müssen ausgefüllt werden" },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "Ungültige E-Mail-Adresse" },
        { status: 400 }
      );
    }

    // Store contact message in database (optional - you might want to create a ContactMessage model)
    // For now, we'll just send the notification

    // Determine notification type based on priority
    let notificationType = NotificationType.INFO;
    let priorityEmoji = "🔵";
    
    switch (priority) {
      case "high":
        notificationType = NotificationType.WARNING;
        priorityEmoji = "🔴";
        break;
      case "medium":
        notificationType = NotificationType.INFO;
        priorityEmoji = "🟡";
        break;
      case "low":
        notificationType = NotificationType.INFO;
        priorityEmoji = "🟢";
        break;
    }

    // Get user information if authenticated
    let userInfo = "";
    if (session?.user) {
      userInfo = ` (Benutzer: ${session.user.name || session.user.email})`;
    }

    // Create admin notification
    await createSystemNotificationForAdmins({
      nachricht: `${priorityEmoji} Neue Kontaktanfrage von ${name} (${email}): "${subject}"${userInfo}. Nachricht: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`,
      type: notificationType,
    });

    // Optional: Store in database for tracking
    // You might want to create a ContactMessage model for this
    /*
    await prisma.contactMessage.create({
      data: {
        name,
        email,
        subject,
        message,
        priority,
        userId: session?.user?.id || null,
        status: "NEW",
      },
    });
    */

    return NextResponse.json({
      success: true,
      message: "Ihre Nachricht wurde erfolgreich gesendet. Wir werden uns bald bei Ihnen melden.",
    });

  } catch (error) {
    console.error("Error processing contact form:", error);
    return NextResponse.json(
      { error: "Interner Serverfehler beim Verarbeiten Ihrer Anfrage" },
      { status: 500 }
    );
  }
}
