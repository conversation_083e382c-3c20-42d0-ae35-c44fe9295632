import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { attachTariffToCdrs } from "~/server/task/attachTariffToCdrs";
import type { ApiResponse } from "~/types/api/apiType";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({});
  }
  try {
    const mode = request.nextUrl.searchParams.get("mode");
    const ignoreCosts = request.nextUrl.searchParams.get("ignoreCosts");

    let minDate = new Date("01.01.2023");
    if (mode === "all") {
      minDate = new Date("01.01.2022");
    }

    const cdrs = await prisma.cdr.findMany({
      where: {
        AND: [
          { End_datetime: { gt: minDate } },
          {
            OR: [
              ...(ignoreCosts ? [] : [{ cost: null }]),
              {
                AND: [{ tarif: null }, { Tariff_Name: null }],
              },
            ],
          },
        ],
      },
      include: {
        cost: true,
        tarif: true,
      },
      take: 5000,
    });

    if (cdrs.length > 0) {
      await attachTariffToCdrs(cdrs);
      const response: ApiResponse<number> = {
        status: "success",
        message: `${cdrs.length} CDRs processed.`,
        payload: cdrs.length,
      };
      return NextResponse.json(response);
    } else {
      const response: ApiResponse<null> = {
        status: "error",
        message: "No CDRs found.",
        payload: null,
      };
      return NextResponse.json(response);
    }
  } catch (error: any) {
    const response: ApiResponse<null> = {
      status: "error",
      message: "An error occurred while processing CDRs.",
      errorDetails: error.message,
      payload: null,
    };
    return NextResponse.json(response);
  }
}
