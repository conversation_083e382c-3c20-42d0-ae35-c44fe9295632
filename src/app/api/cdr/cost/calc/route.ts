import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { ComputeEnergyCostForCdrs } from "~/server/task/cost";
import { attachTariffToCdrs } from "~/server/task/attachTariffToCdrs";
import { computeCardHolderCosts, computeDirectPaymentCosts } from "~/server/invoice/calculateCost";
import { computeAdhocCosts } from "~/server/invoice/calculateCost";
import type { ApiResponse } from "~/types/api/apiType";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({});
  }
  try {
    const mode = request.nextUrl.searchParams.get("mode");
    const onlyCardHolder = request.nextUrl.searchParams.get("onlyCardHolder");
    let minDate = new Date("01.01.2023");

    if (mode === "all") {
      minDate = new Date("01.01.2022");
    }

    if (onlyCardHolder) {
      const cdrs = await prisma.cdr.findMany({
        where: {
          OR: [{ companyTarif: null }, { Tariff_Name: null }],
          End_datetime: {
            gt: minDate,
          },
          Service_Provider_ID: { equals: "" },
        },
        include: {
          companyTarif: true,
        },
        take: 5000,
      });

      if (cdrs.length > 0) {
        const computedCdrs = await computeCardHolderCosts(cdrs);
        await attachTariffToCdrs(computedCdrs);
        const response: ApiResponse<number> = {
          status: "success",
          message: `${cdrs.length} CDRs processed for card holders.`,
          payload: cdrs.length,
        };
        return NextResponse.json(response);
      } else {
        const response: ApiResponse<null> = {
          status: "error",
          message: "No CDRs found for card holders.",
          payload: null,
        };
        return NextResponse.json(response);
      }
    }

    const cdrs = await prisma.cdr.findMany({
      where: {
        OR: [{ cost: null }, { tarif: null }, { Tariff_Name: null }],
        End_datetime: {
          gt: minDate,
        },
        Tariff_Name: {
          notIn: ["Adhoc", "Eulektro Adhoc"],
        },
        Service_Provider_ID: {
          notIn: ["YYCAM", "YYPPD", "YYBPD", "DEHAN", "YYZEI", "YYESP"],
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
      take: 5000,
    });

    if (cdrs.length > 0) {
      await ComputeEnergyCostForCdrs(cdrs);
      await computeAdhocCosts(cdrs);
      await computeDirectPaymentCosts(cdrs);
      await attachTariffToCdrs(cdrs);
      const response: ApiResponse<number> = {
        status: "success",
        message: `${cdrs.length} CDRs processed.`,
        payload: cdrs.length,
      };
      return NextResponse.json(response);
    } else {
      const response: ApiResponse<null> = {
        status: "error",
        message: "No CDRs found.",
        payload: null,
      };
      return NextResponse.json(response);
    }
  } catch (error: any) {
    const response: ApiResponse<null> = {
      status: "error",
      message: "An error occurred while processing CDRs.",
      errorDetails: error.message,
      payload: null,
    };
    return NextResponse.json(response);
  }
}
