import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { computeAdhocCosts } from "~/server/invoice/calculateCost";
import { Prisma, Role } from "@prisma/client";
import type { ApiResponse } from "~/types/api/apiType";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    const response: ApiResponse<null> = {
      status: "error",
      message: "not login",
      errorDetails: "no admin login",
      payload: null,
    };
    return NextResponse.json(response);
  }
  try {
    const mode = request.nextUrl.searchParams.get("mode");
    const ignoreCosts = request.nextUrl.searchParams.get("ignoreCosts");
    let minDate = new Date("01.01.2023");
    if (mode === "all") {
      minDate = new Date("01.01.2022");
    }

    const cdrs = await prisma.cdr.findMany({
      where: {
        OR: [...(ignoreCosts ? [] : [{ cost: null }]), { Tariff_Name: null }],
        End_datetime: {
          gt: minDate,
        },
        Service_Provider_ID: {
          equals: "DEEUL",
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
      take: 5000,
    });

    if (cdrs.length > 0) {
      await computeAdhocCosts(cdrs);
      const queries = cdrs.map((cdr) =>
        prisma.cdr.update({
          where: {
            CDR_ID: cdr.CDR_ID,
          },
          data: {
            Calculated_Cost: cdr.Calculated_Cost,
            EnergyCosts: cdr.EnergyCosts,
            Service_Provider_ID: cdr.Service_Provider_ID,
            tarifId: cdr.tarifId,
            billable: cdr.billable,
            Tariff_kWh: cdr.Tariff_kWh,
            Start_Tariff: cdr.Start_Tariff,
            Tariff_Name: cdr.Tariff_Name,
            Parking_Time_Cost: cdr.Parking_Time_Cost,
          },
        }),
      );

      await prisma.$transaction(queries, {
        isolationLevel: Prisma.TransactionIsolationLevel.Serializable, // optional, default defined by database configuration
      });

      const response: ApiResponse<number> = {
        status: "success",
        message: `${cdrs.length} CDRs processed.`,
        payload: cdrs.length,
      };
      return NextResponse.json(response);
    } else {
      const response: ApiResponse<null> = {
        status: "error",
        message: "No CDRs found.",
        payload: null,
      };
      return NextResponse.json(response);
    }
  } catch (error: any) {
    const response: ApiResponse<null> = {
      status: "error",
      message: "An error occurred while processing CDRs.",
      errorDetails: error.message,
      payload: null,
    };
    return NextResponse.json(response);
  }
}
