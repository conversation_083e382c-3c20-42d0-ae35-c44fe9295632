import prisma from "~/server/db/prisma";
import { CdrMapping } from "@prisma/client";
import { generateCSV } from "~/utils/csv/generateCSV";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { NextRequest, NextResponse } from "next/server";
import { getOusBelowOu } from "~/server/model/ou/func";
import { env } from "~/env";

export async function POST(request: NextRequest, res: NextResponse) {
  const { startDate, endDate, contact, tarifName } = await request.json();
  if (!startDate || !endDate || !tarifName || !(typeof contact === "string")) {
    return null;
  }
  const fromDateString = new Date(startDate);
  fromDateString.setUTCHours(0, 0, 0, 0);
  const toDateString = new Date(endDate);
  toDateString.setUTCHours(23, 59, 59, 999);

  const session = await getServerSession(authOptions);
  if (!session) {
    return {};
  }

  const ouWithChildren = await getOusBelowOu(session.user.selectedOu);
  const ouCodes = ouWithChildren?.map((ou) => `'${ou.code}'`);
  const cdrs = await prisma.cdr.findMany({
    where: {
      OU_Code: { in: ouCodes },
      End_datetime: {
        gte: fromDateString,
        lte: toDateString,
      },
      Service_Provider_ID: {
        equals: contact,
      },
      Volume: {
        gt: 0.2,
      },
      DurationInSec: {
        gt: 120,
      },
    },
    include: {
      cost: true,
      tarif: true,
    },
  });

  //const calculatedCdrs = await computeRoamingCostByEmpPrice(cdrs);

  const cdrFilterByTarifName = cdrs.filter((x) => x.Tariff_Name == tarifName);
  const csv = generateCSV(cdrFilterByTarifName, CdrMapping.Standard);
  const csv_file_name = `cdr_${contact}_${startDate}-${endDate}.csv`;
  return new NextResponse(csv, {
    headers: {
      "Content-Type": "text/csv",
      "Content-Disposition": `attachment; filename="${
        env.NODE_ENV != "production" ? "_DEV_" : ""
      }$${csv_file_name}"`,
    },
  });
}
