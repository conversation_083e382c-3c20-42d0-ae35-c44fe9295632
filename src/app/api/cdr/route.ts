import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { Prisma, Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

const getData = async (startDate?: string, endDate?: string) => {
  const whereClause: Prisma.CdrWhereInput = {};

  if (startDate && endDate) {
    const endDateObj = new Date(endDate);
    endDateObj.setHours(23, 59, 59, 999);
    whereClause.End_datetime = {
      gte: new Date(startDate),
      lte: endDateObj,
    };
  }

  return prisma.cdr.findMany({
    where: whereClause,
    include: {
      cost: true,
    },
  });
};

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({});
  }

  const url = new URL(request.url);
  const startDate = url.searchParams.get("startDate");
  const endDate = url.searchParams.get("endDate");

  if (!startDate || !endDate) {
    return NextResponse.json({
      status: 3000,
      statusText: "Bad Request, missing startDate or endDate",
      timestamp: new Date().toISOString(),
    });
  }

  const data = await getData(startDate, endDate);

  return NextResponse.json(data);
}

export async function POST(request: NextRequest) {
  const { ou_code } = await request.json();

  const cdrs = await prisma.cdr.findMany({
    where: {
      OU_Code: `'${ou_code}'`,
    },
  });

  return NextResponse.json(cdrs);
}
