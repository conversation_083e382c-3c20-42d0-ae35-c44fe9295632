import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import type { ApiResponse } from "~/types/api/apiType";
import { fetchTransactionIDsFromSessionsByDirectPaymentTokenOfCPOContract } from "~/utils/transactionsync/synctransactionIds";

export const revalidate = 0;
export async function GET(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    const response: ApiResponse<null> = {
      status: "error",
      message: "not login",
      errorDetails: "no admin login",
      payload: null,
    };
    return NextResponse.json(response);
  }

  const start = request.nextUrl.searchParams.get("startdate");
  const end = request.nextUrl.searchParams.get("enddate");
  if (start && end) {
    const responseMsg = await fetchTransactionIDsFromSessionsByDirectPaymentTokenOfCPOContract(
      start,
      end,
    );
    const apiresponse: ApiResponse<null> = {
      status: "success",
      message: responseMsg,
      errorDetails: "",
      payload: null,
    };
    return NextResponse.json(apiresponse);
  } else {
    const response: ApiResponse<null> = {
      status: "error",
      message: "start and end params missing",
      errorDetails: "No startdate and enddate defined as GET params",
      payload: null,
    };
    return NextResponse.json(response);
  }
}
