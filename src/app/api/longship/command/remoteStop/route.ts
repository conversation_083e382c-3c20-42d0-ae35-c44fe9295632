import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

import { env } from "~/env";
import { LongshipHeaders } from "~/utils/longship";
import { LogType, Role } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user) {
    return NextResponse.json("no auth", { status: 401 });
  }

  // get all ou ids of the user in order to check whether it is allowed to stop a transaction for
  // the evseID which was provided as parameter of the request
  const ouWithChildren = await getOusBelowOu(session?.user.ou);
  const userOuIds = ouWithChildren.map((ou) => ou.id);

  const { evseId } = await request.json();
  const headers = LongshipHeaders({});
  const evse = await prisma.evse.findUnique({ where: { evse_id: evseId } });

  //check evse ou is part of all user ou ids (parent and children)
  if (evse?.ouId && !userOuIds.includes(evse?.ouId)) {
    return NextResponse.json("error - ou for user not allowed ", { status: 401 });
  }

  if (evse) {
    const chargepointId = evse.chargePointId;
    const connector = evse.connector;

    // fetch running session to get transactionId to be able to stop remote
    if (chargepointId && connector) {
      const url = new URL(`sessions`, env.LONGSHIP_API);
      url.searchParams.set("connectorNumber", connector);
      url.searchParams.set("runningOnly", "true");
      url.searchParams.set("take", "1");
      url.searchParams.set("chargepointId", chargepointId);

      const fetchRunningTransactionIdResponse = await fetch(url, {
        method: "GET",
        headers: headers,
      });
      if (fetchRunningTransactionIdResponse.ok) {
        const sessions = await fetchRunningTransactionIdResponse.json();

        if (sessions?.length == 1) {
          const transactionId = sessions[0].transactionId;
          const remoteStopResponse = await fetch(
            `${env.LONGSHIP_API}chargepoints/${chargepointId}/remoteStop`,
            {
              method: "POST",
              headers: headers,
              body: JSON.stringify({ transactionId: transactionId }),
            },
          );

          if (remoteStopResponse.ok) {
            Logger(
              `${evseId} stopped by user ${session?.user?.email}`,
              "RemoteStop from Portal",
              "RemoteControl",
              LogType.INFO,
            );
            return NextResponse.json("ok", { status: 200 });
          } else {
            Logger(
              `Unable to stop ${evseId} by user ${session?.user?.email}`,
              "RemoteStop from Portal",
              "RemoteControl",
              LogType.INFO,
            );
            return NextResponse.json("error", { status: 500 });
          }
        }
        return NextResponse.json("error - no transaction found for connector ", { status: 404 });
      }
      return NextResponse.json("error - fetching transaction", { status: 500 });
    }
    return NextResponse.json("error - no chargepoint and connector found", { status: 404 });
  }

  return NextResponse.json("error - evse not found", { status: 404 });
}
