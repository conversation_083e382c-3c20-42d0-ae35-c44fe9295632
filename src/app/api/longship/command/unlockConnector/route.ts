import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

import { env } from "~/env";
import { LongshipHeaders } from "~/utils/longship";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user) {
    return NextResponse.json("no auth", { status: 401 });
  }

  // get all ou ids of the user in order to check whether it is allowed to unlock
  // the evseID which was provided as parameter of the request
  const ouWithChildren = await getOusBelowOu(session?.user.ou);
  const userOuIds = ouWithChildren.map((ou) => ou.id);

  const { evseId } = await request.json();

  const evse = await prisma.evse.findUnique({ where: { evse_id: evseId } });
  //check evse ou is part of all user ou ids (parent and children)
  if (evse?.ouId && !userOuIds.includes(evse?.ouId)) {
    return NextResponse.json("error - ou for user not allowed ", { status: 401 });
  }

  if (!session?.user?.remoteStartToken) {
    return NextResponse.json("error - user has no remoteStartToken", { status: 401 });
  }

  if (evse) {
    const chargepointId = evse.chargePointId;
    const connector = evse.connector;

    const headers = LongshipHeaders({});

    const fetchRequest = await fetch(
      `${env.LONGSHIP_API}chargepoints/${chargepointId}/unlockconnector`,
      {
        method: "POST",
        headers: headers,
        body: JSON.stringify({ connectorId: connector }),
      },
    );
    if (fetchRequest.ok) {
      Logger(
        `${evseId} unlocked by user ${session?.user?.email}`,
        "Unlock from Portal",
        "RemoteControl",
        LogType.INFO,
      );
      return NextResponse.json("ok", { status: 200 });
    } else {
      Logger(
        `Unable to unlock ${evseId} by user ${session?.user?.email}`,
        "Unlock from Portal",
        "RemoteControl",
        LogType.INFO,
      );
      return NextResponse.json("error", { status: 500 });
    }
  }

  return NextResponse.json("error - evse not found", { status: 404 });
}
