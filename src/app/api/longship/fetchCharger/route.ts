import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import Logger from "../../../../server/logger/logger";
import { LogType } from "@prisma/client";
import getCharger from "~/server/task/charger";
import { ApiResponse } from "~/types/api/apiType";

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const data = await getCharger();

    // Loggen des erfolgreichen Vorgangs
    Logger("Import charger from longshop", "Charger Importer", "cron", LogType.INFO);

    // Erfolgreiche Antwort
    return NextResponse.json({
      status: "success",
      payload: data,
      message: `${data.length} Charger successfully imported`,
    } as ApiResponse<any>);
  } catch (error) {
    // Fehler loggen
    Logger(`Failed to import charger: ${error}`, "Charger Importer", "cron", LogType.ERROR);

    // Fehlerantwort
    return NextResponse.json({
      status: "error",
      errorCode: "IMPORT_FAILED",
      message: "Failed to import charger",
      errorDetails: error,
    } as ApiResponse<null>);
  }
}
