import type { NextRequest } from "next/server";
import fs from "fs";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { env } from "~/env.js";
import { createPayoutExportFile } from "~/utils/stripe/reportHelper";

export async function GET(request: NextRequest) {
  const payoutId = request.nextUrl.searchParams.get("payoutId");
  const createFile = !!request.nextUrl.searchParams.get("createFile") ?? false;
  const createPayoutItems = !!request.nextUrl.searchParams.get("createPayoutItems") ?? false;

  if (payoutId && payoutId.startsWith("po_")) {
    // Pfade zur Datei und zum Dateinamen festlegen
    const fullPath = `${env.STRIPE_PAYOUT_REPORTS}/raw_report_object_${payoutId}.json`;

    // Prüfen, ob die Datei existiert
    if (fs.existsSync(fullPath)) {
      const succes = createPayoutExportFile(null, fullPath, createFile, createPayoutItems);
      if (succes) {
        return NextResponse.json({ status: 200 });
      } else {
        return NextResponse.json(`Creating export file failed`, { status: 500 });
      }
    } else {
      // Fehlermeldung, wenn die Datei nicht gefunden wurde
      return NextResponse.json(`Path: ${fullPath} not found`, { status: 404 });
    }
  } else {
    return NextResponse.json("no valid payoutId provided", { status: 500 });
  }
}
