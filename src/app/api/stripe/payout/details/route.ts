import type { NextRequest } from "next/server";
import prisma from "../../../../../server/db/prisma";
import { NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const payoutId = request.nextUrl.searchParams.get("payoutId");
  if (payoutId) {
    const payoutItems = await prisma.stripePayoutItem.findMany({
      where: { automaticPayoutId: payoutId },
    });

    return NextResponse.json(payoutItems);
  }
  return NextResponse.json("No PayoutId", { status: 500 });
}
