import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";

import { createStripeCompany } from "~/server/stripe/paymentUtils";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const sepaHash = request.nextUrl.searchParams.get("sepaHash");
  if (!sepaHash) {
    return NextResponse.next({
      status: 401,
      statusText: "Invalid",
    });
  }
  const contacts = await prisma.contact.findMany({
    where: { sepaHash: sepaHash },
    include: { contactAddress: true },
  });
  if (!contacts || contacts?.length == 0) {
    return NextResponse.json({ success: false, message: "Invalid sepaHash" }, { status: 401 });
  }

  if (contacts?.length > 1) {
    return NextResponse.json(
      { success: false, message: "Multiple contacts found" },
      { status: 404 },
    );
  }

  let contact = contacts?.length == 1 ? contacts[0] : null;
  if (contact && !contact.stripeCustomerId) {
    try {
      contact = await createStripeCompany(contact);
    } catch (e) {
      const errorMsg = (e as Error)?.message;
      return NextResponse.json({ success: false, message: errorMsg }, { status: 500 });
    }
  }

  if (contact?.stripeCustomerId) {
    const setupIntent = await stripe.setupIntents.create({
      payment_method_types: ["sepa_debit"],
      customer: contact.stripeCustomerId,
    });
    return NextResponse.json({
      clientSecret: setupIntent.client_secret,
      contact: contact,
    });
  }

  return NextResponse.json(
    { message: "User has no stripe customer id" },
    {
      status: 404,
    },
  );
}
