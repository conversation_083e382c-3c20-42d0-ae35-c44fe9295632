import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 404 });
  }
  //todo zod post verfiy
  const id = session?.user.id;
  const user = await prisma.user.findUnique({
    where: {
      id: id,
    },
  });
  if (!user) {
    return NextResponse.json({ success: false, message: "User not found" }, { status: 404 });
  }
  if (user.stripeCustomerId) {
    const stripeUser = await stripe.customers.retrieve(user.stripeCustomerId);
    const paymentMethods = await stripe.customers.listPaymentMethods(
      user.stripeCustomerId,
      { type: "sepa_debit" }, //todo add more
    );
    if (!stripeUser.deleted) {
      const extractedData = paymentMethods.data.map((item) => ({
        name: item.billing_details.name,
        email: item.billing_details.email,
        last4: item?.sepa_debit?.last4,
        default: item.id === stripeUser.invoice_settings?.default_payment_method,
        id: item.id,
      }));
      return NextResponse.json(extractedData);
    } else {
      return NextResponse.next({
        status: 500,
        statusText: "User was deleted at stripe",
      });
    }
  }

  /*const paymentIntent = await stripe.paymentIntents.create({
        payment_method_types: ["sepa_debit"],
        currency: "EUR",
        capture_method: "automatic",
        amount: 50

    });*/
  return NextResponse.next({
    status: 404,
    statusText: "User has no stripe customer id",
  });
}
