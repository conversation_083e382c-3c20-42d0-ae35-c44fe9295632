import type { NextRequest } from "next/server";
import fs from "fs";
import { NextResponse } from "next/server";
import { env } from "~/env.js";
import { createPayoutExportFile } from "~/utils/stripe/reportHelper";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import type { Stripe } from "stripe";

/**
 * Endpoint to create a payout export file from an existing CSV file in the STRIPE_PAYOUT_REPORTS directory
 *
 * @param payoutId - The Stripe payout ID (must start with 'po_')
 * @param csvFilename - The name of the CSV file in the STRIPE_PAYOUT_REPORTS directory (must start with 'frr_' and end with '.csv')
 *
 * This endpoint creates a dummy Stripe.Reporting.ReportRun object and passes it to the createPayoutExportFile function
 * to generate a payout export file without requiring an actual Stripe report.
 */
export async function GET(request: NextRequest) {
  const payoutId = request.nextUrl.searchParams.get("payoutId");
  const csvFilename = request.nextUrl.searchParams.get("csvFilename");

  if (!payoutId || !payoutId.startsWith("po_")) {
    return NextResponse.json("No valid payoutId provided", { status: 400 });
  }

  if (!csvFilename || !csvFilename.startsWith("frr_") || !csvFilename.endsWith(".csv")) {
    return NextResponse.json("No valid CSV filename provided", { status: 400 });
  }

  // Check if the CSV file exists in the STRIPE_PAYOUT_REPORTS directory
  const csvFilePath = `${env.STRIPE_PAYOUT_REPORTS}/${csvFilename}`;
  if (!fs.existsSync(csvFilePath)) {
    return NextResponse.json(`CSV file not found: ${csvFilePath}`, { status: 404 });
  }

  // Create a dummy Stripe.Reporting.ReportRun object
  const timestamp = Math.floor(Date.now() / 1000);
  const dummyReport: Stripe.Reporting.ReportRun = {
    id: `frr_dummy_${Date.now()}`,
    object: "reporting.report_run",
    created: timestamp,
    error: null,
    livemode: true,
    parameters: {
      payout: payoutId,
      columns: [
        "reporting_category",
        "automatic_payout_id",
        "payment_intent_id",
        "gross",
        "net",
        "fee",
        "customer_facing_amount",
        "payment_metadata[evse]",
        "payment_metadata[cdr_id]"
      ]
    },
    report_type: "payout_reconciliation.by_id.itemized.4",
    result: {
      id: `file_dummy_${Date.now()}`,
      object: "file",
      created: timestamp,
      expires_at: timestamp + 31536000, // One year from now
      filename: csvFilename,
      links: {
        object: "list",
        data: [],
        has_more: false,
        url: "/v1/file_links?file=file_dummy"
      },
      purpose: "finance_report_run",
      size: 445825, // Dummy size
      title: `FinanceReportRun ${csvFilename}`,
      type: "csv",
      url: `file://${env.STRIPE_PAYOUT_REPORTS}/${csvFilename}`
    },
    status: "succeeded",
    succeeded_at: timestamp
  };

  Logger(
    `Creating payout export file from CSV ${csvFilename} for payout ${payoutId}`,
    "Create Payout Export File",
    "stripe",
    LogType.INFO
  );

  try {
    const success = createPayoutExportFile(dummyReport);

    if (success) {
      return NextResponse.json({
        status: 200,
        message: "Successfully created payout export file"
      });
    } else {
      return NextResponse.json({
        status: 500,
        message: "Failed to create payout export file. See logs for details."
      });
    }
  } catch (error) {
    Logger(
      `Error creating payout export file: ${error instanceof Error ? error.message : String(error)}`,
      "Create Payout Export File Error",
      "stripe",
      LogType.ERROR
    );

    return NextResponse.json({
      status: 500,
      message: "Error creating payout export file",
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
