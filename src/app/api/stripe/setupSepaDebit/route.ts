import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { createStripeUser } from "~/server/stripe/paymentUtils";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  //todo zod post verfiy

  const session = await getServerSession(authOptions);

  const id = session?.user.id; //"clmp9yg1000018co6xnhvgk0a"
  let user = await prisma.user.findUnique({
    where: {
      id: id,
    },
  });
  if (!user) {
    return NextResponse.json({ success: false, message: "User not found" }, { status: 404 });
  }
  // in case user was not registered via invitation, there is no stripe account
  // so check and create if necessary
  if (!user.stripeCustomerId) {
    user = await createStripeUser(user);
  }

  // if there still is no stripeCustomerId attached, there is a problem
  if (user.stripeCustomerId) {
    const setupIntent = await stripe.setupIntents.create({
      payment_method_types: ["sepa_debit"],
      customer: user.stripeCustomerId,
    });
    return NextResponse.json({
      clientSecret: setupIntent.client_secret,
    });
  }

  return NextResponse.next({
    status: 404,
    statusText: "User has no stripe customer id",
  });
}
