import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { hasher } from "~/server/hasher/hasher";

import { env } from "~/env.js";
import <PERSON><PERSON> from "stripe";
import { Role } from "@prisma/client";
import { sendVerifyEmailMail } from "~/utils/register/mails";
import crypto from "crypto";

const MinimalRegisterUserWithCardSchema = z.object({
  password: z.string().min(8),
  passwordRepeat: z.string().min(8),

  email: z.string().email(),
  visualNumber: z.string(),
  registrationSlug: z.string(),
});

export async function POST(request: NextRequest) {
  const result = MinimalRegisterUserWithCardSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const { email, password, visualNumber, registrationSlug } = result.data;

  try {
    const physicalCard = await prisma.physicalCard.findUnique({
      where: {
        visualNumber: visualNumber,
      },
      include: {
        EMPCard: true,
        userGroup: true,
      },
    });

    if (physicalCard && physicalCard?.EMPCard) {
      return NextResponse.json({ error: "Karte schon einem User zugeordnet" }, { status: 400 });
    }
    const ou = await prisma.ou.findUnique({
      where: { registrationSlug: registrationSlug },
    });
    let user;
    if (ou && physicalCard) {
      // Check if physical card's userGroup belongs to the same OU
      let userGroupId = null;
      if (physicalCard.userGroup) {
        if (physicalCard.userGroup.ouId === ou.id) {
          userGroupId = physicalCard.userGroup.id;
        }
      }

      try {
        user = await prisma.user.create({
          data: {
            name: "",
            lastName: "",
            email,
            ouId: ou.id,
            selectedOuId: ou.id,
            password: await hasher(password),
            role: Role.CARD_HOLDER,
            signUpHash: crypto.randomBytes(32).toString("hex"),
            userGroupId: userGroupId,
          },
          include: { ou: true },
        });
      } catch {
        return NextResponse.json({ error: "Anlegen des Benutzers nicht möglich" }, { status: 402 });
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const defaultTarifs = await prisma.companyTarif.findMany({
        where: {
          ouId: ou.id,
          optional: false,
          internal: false,
          validFrom: {
            lte: today, // validFrom muss kleiner oder gleich heute sein
          },
          validTo: {
            gte: today, // validTo muss größer oder gleich heute sein
          },
        },
      });

      await prisma?.eMPCard.create({
        data: {
          userId: user.id,
          active: false,
          physicalCardId: physicalCard.uid,
          note: "Created with pre delivered card",
          preDelivered: true,
          tarifs: {
            // connect current default tarifs (optional = false)
            create: defaultTarifs.map((tarif) => {
              return { tarif: { connect: { id: tarif.id } } };
            }),
          },
        },
      });
      await sendVerifyEmailMail(user);

      return NextResponse.json({}, { status: 201 });
    }

    return NextResponse.json({ error: "Unknown error occurred." }, { status: 400 });
  } catch (e: unknown) {
    if (e instanceof Error) {
      return NextResponse.json({ error: e.message }, { status: 500 });
    }
    return NextResponse.json({ error: "Unknown error occurred." }, { status: 500 });
  }
}
