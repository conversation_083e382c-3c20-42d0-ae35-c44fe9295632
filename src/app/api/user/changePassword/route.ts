import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

import generator from "generate-password";
import nodemailer from "nodemailer";
import prisma from "../../../../server/db/prisma";
import { hasher } from "~/server/hasher/hasher";
import { env } from "~/env.js";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

const ChangePasswordSchema = z.object({
  currentPassword: z.string(),
  password: z.string(),
  passwordRepeat: z.string(),
});

export async function POST(request: NextRequest) {
  const result = ChangePasswordSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const session = await getServerSession(authOptions);
  if (!session || !session?.user?.id) {
    return NextResponse.json("no valid session", { status: 401 });
  }

  const { currentPassword, password, passwordRepeat } = result.data;

  const existingUser = await prisma.user.findUnique({
    where: {
      id: session.user.id,
    },
  });

  if (!existingUser) {
    return NextResponse.json({ error: "no valid session" }, { status: 401 });
  }

  if (!((await hasher(currentPassword)) == existingUser.password)) {
    return NextResponse.json({ error: "Fehler beim Ändern (Passwort korrekt)?" }, { status: 401 });
  }

  try {
    const user = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        password: await hasher(password),
      },
      select: {
        id: true,
        name: true,
        email: true,
        ouId: true,
      },
    });

    // Create transporter
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
    });

    // Send email
    transporter.sendMail({
      from: "<EMAIL>",
      to: existingUser.email,
      subject: "Ihr Passwort im Ladeportal wurde geändert",
      text: `Moin ${existingUser.name} ${existingUser.lastName}!\nIhr Passwort im Ladeportal wurde soeben geändert. Wenn Sie ihr Passwort nicht selber geändert haben, wenden Sie sich bitte an den Support.`,
    });

    return NextResponse.json(user, { status: 200 });
  } catch (e: unknown) {
    if (typeof e === "string") {
      return NextResponse.json({ error: e.toUpperCase() }, { status: 500 });
    } else if (e instanceof Error) {
      return NextResponse.json({ error: e.message }, { status: 500 });
    }
  }
}
