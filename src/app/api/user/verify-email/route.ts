import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { z } from "zod";
import { env } from "~/env";
const HexStringLowerCaseSchema = z
  .string()
  .regex(
    /^[0-9a-f]+$/,
    'Ungültiger Hex-String. Er darf nur Kleinbuchstaben a-f und Zahlen 0-9 enthalten und kein führendes "#".',
  );
export async function GET(request: NextRequest) {
  const signupHash = request.nextUrl.searchParams.get("signupHash");
  const result = HexStringLowerCaseSchema.safeParse(signupHash);
  if (!signupHash) {
    return NextResponse.json({ error: "Unexpected Parameter" }, { status: 400 });
  }
  if (!result.success) {
    return NextResponse.json({ error: "Parameter validation missmatch" }, { status: 400 });
  }

  try {
    const userByHash = await prisma.user.findUnique({
      where: {
        signUpHash: signupHash,
      },
    });

    if (!userByHash) {
      return NextResponse.json({ error: "Unknown user" }, { status: 404 });
    }

    const user = await prisma.user.update({
      where: {
        email: userByHash.email,
      },
      data: {
        signUpHash: null,
        emailVerified: new Date(),
      },
      select: { ou: true },
    });

    const redirectUrl = new URL(
      `/register/${user.ou?.registrationSlug}/success`,
      env.NEXT_PUBLIC_SITE_URL,
    ); // Passe die URL an
    return NextResponse.redirect(redirectUrl, 302); //
  } catch (e: unknown) {
    if (e instanceof Error) {
      return NextResponse.json({ error: e.message }, { status: 500 });
    }
    return NextResponse.json({ error: "Unknown error occurred." }, { status: 500 });
  }
}
