import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";

import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

const UpdateUserProfileSchema = z.object({
  name: z.string(),
  lastName: z.string(),
  street: z.string(),
  streetNr: z.string(),
  city: z.string(),
  zip: z.string(),
  country: z.string(),
  addressId: z.string(),
  companyName: z.string(),
});
export async function PATCH(request: NextRequest) {
  const result = UpdateUserProfileSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json({ error: "No login" }, { status: 404 });
  }

  try {
    if (result.data.addressId) {
      await prisma.contactAddress.update({
        where: { id: result.data.addressId },
        data: {
          street: result.data.street,
          streetNr: result.data.streetNr,
          city: result.data.city,
          zip: result.data.zip,
          country: result.data.country,
        },
      });
    } else if (
      result.data.zip &&
      result.data.city &&
      result.data.street &&
      result.data.streetNr &&
      result.data.country
    ) {
      const address = await prisma.contactAddress.create({
        data: {
          zip: result.data.zip,
          city: result.data.city,
          street: result.data.street,
          streetNr: result.data.streetNr,
          country: result.data.country,
          validFrom: new Date(),
          validTo: new Date(2099, 0, 1), //dummy date für cardholder
          userId: userId,
        },
      });
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name: result.data.name,
        lastName: result.data.lastName,
        companyName: result.data.companyName,
      },
      select: {
        id: true,
        name: true,
        email: true,
        ouId: true,
      },
    });

    return NextResponse.json(updatedUser, { status: 200 });
  } catch (e: unknown) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === "P2016") {
        return NextResponse.json({ error: "User not found." }, { status: 404 });
      } else {
        return NextResponse.json({ error: "Unbekanter Fehler" }, { status: 404 });
      }
    }
    return NextResponse.json({ error: "An unexpected error occurred." }, { status: 500 });
  }
}
