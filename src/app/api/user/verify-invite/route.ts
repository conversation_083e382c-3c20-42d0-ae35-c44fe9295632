import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { hasher } from "~/server/hasher/hasher";

import { env } from "~/env.js";
import <PERSON><PERSON> from "stripe";

const RegisterUserSchema = z.object({
  name: z.string(),
  lastName: z.string(),
  password: z.string().min(8),
  passwordRepeat: z.string().min(8),
  country: z.string(),
  postalCode: z.string(),
  city: z.string(),
  street: z.string(),
  houseNumber: z.string(),
  phone: z.string().optional(),
  email: z.string().email(),
  signUpHash: z.string(),
});

export async function POST(request: NextRequest) {
  const result = RegisterUserSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const {
    name,
    email,
    lastName,
    password,
    country,
    postalCode,
    city,
    street,
    houseNumber,
    phone,
    signUpHash,
  } = result.data;

  const hashedPassword = await hasher(password);

  try {
    const userByHash = await prisma.user.findFirst({
      where: {
        signUpHash: signUpHash,
        email: email,
      },
    });

    if (!userByHash) {
      return NextResponse.json({ error: "Hash invalid or e-mail" }, { status: 404 });
    }

    void (await prisma.contactAddress.create({
      data: {
        validFrom: new Date(),
        validTo: new Date(2099, 11, 31, 23, 59, 59, 999),
        street: street,
        streetNr: houseNumber,
        city: city,
        zip: postalCode,
        country: country,
        userId: userByHash.id,
      },
    }));

    const user = await prisma.user.update({
      where: {
        email: userByHash.email,
      },
      data: {
        signUpHash: null,
        name: name,
        lastName: lastName,
        password: hashedPassword,
        phone: phone,
        emailVerified: new Date(),
      },
    });

    if (user) {
      const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
        apiVersion: "2022-11-15",
      });

      const customer = await stripe.customers.create({
        email: user.email,
        name: `${user.name}  ${user.lastName}`,
        metadata: { eulektroUserId: user.id },
      });
      const user_with_stripe = await prisma.user.update({
        where: { id: user.id },
        data: { stripeCustomerId: customer.id },
      });
    }

    return NextResponse.json(user, { status: 201 });
  } catch (e: unknown) {
    if (e instanceof Error) {
      return NextResponse.json({ error: e.message }, { status: 500 });
    }
    return NextResponse.json({ error: "Unknown error occurred." }, { status: 500 });
  }
}
