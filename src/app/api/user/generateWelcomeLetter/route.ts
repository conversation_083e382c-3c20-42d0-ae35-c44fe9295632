import { NextRequest, NextResponse } from "next/server";
import PDFDocument from "pdfkit";
import fs from "fs";
import { drawFooterAndLogo } from "~/utils/invoice/createInvoicePDF";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { getContactAdressByDate } from "~/utils/contact/getContactAdressByDate";
import { env } from "~/env.js";
import prisma from "~/server/db/prisma";
export async function GET(request: NextRequest, res: NextResponse) {
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN || session?.user?.role == Role.USER)) {
    return NextResponse.json(
      {
        message: "no auth",
      },
      { status: 401 },
    );
  }
  let address;
  let user;
  const userId = request.nextUrl.searchParams.get("userId");
  if (userId) {
    user = await prisma?.user.findUnique({
      where: { id: userId },
      include: { address: true },
    });
    if (user) {
      address = getContactAdressByDate(user.address, new Date());
    } else {
      return NextResponse.json(
        {
          message: "not found",
        },
        { status: 404 },
      );
    }
  }
  if (!address || !user) {
    return NextResponse.json(
      {
        message: "not found",
      },
      { status: 404 },
    );
  }
  try {
    const doc = new PDFDocument({
      margins: { left: 70, right: 50, top: 20, bottom: 20 },
      size: "A4",
      font: "public/SourceSans/SourceSans3-Regular.ttf",
    });

    if (!fs.existsSync(env.WELCOME_LETTER_FOLDER)) {
      fs.mkdirSync(env.WELCOME_LETTER_FOLDER);
    }

    const filePath = `${env.WELCOME_LETTER_FOLDER}/${userId}_welcome.pdf`;
    const writeStream = fs.createWriteStream(filePath);
    // Datei zum Schreiben des PDFs öffnen
    doc.pipe(writeStream);

    doc.image("public/logo/EULEKTRO_21697c_R33_G105_B124.png", 370, 0, {
      height: 145,
    });

    //doc.moveTo(y 785).lineTo(560, 785).lineWidth(0.5).strokeColor("grey").stroke();

    doc.moveDown(7);
    doc.fontSize(8);
    doc.text("Eulektro GmbH | Werderstraße 69 | 28199 Bremen", { align: "left", continued: true });
    doc.font("public/SourceSans/SourceSans3-Bold.ttf");
    doc.text("Eulektro GmbH", { align: "right" });

    doc.font("public/SourceSans/SourceSans3-Regular.ttf");
    doc.fontSize(10);
    doc.moveDown(1);

    doc.text(`${user.name} ${user.lastName}`, { align: "left", continued: true });
    doc.fontSize(8).lineGap(4);
    doc.text("Tel: 0421 175 128 90", { align: "right" });
    doc.fontSize(10).lineGap(4);
    doc.text(`${address.street} ${address.streetNr}`, { align: "left", continued: true });
    doc.fontSize(8).lineGap(4);
    doc.text("<EMAIL>", { align: "right" });
    doc.fontSize(10).lineGap(4);
    doc.text(`${address.zip} ${address.city}`, { align: "left" });
    doc.moveDown();
    doc.text(`${address.country}`, { align: "left" });

    doc.moveDown(2);

    doc.font("public/SourceSans/SourceSans3-Bold.ttf", 15);
    doc.fillColor("#21697c");
    doc.text("Ihre persönliche Ladekarte", { continued: true });
    doc.fillColor("black");
    doc.fontSize(8);
    doc.font("public/SourceSans/SourceSans3-Regular.ttf");
    const today = new Date();
    doc.text(
      `${today.getDate()} ${today.toLocaleString("de-DE", {
        month: "long",
      })} ${today.getFullYear()}`,
      {
        align: "right",
      },
    );

    doc.fontSize(10).lineGap(2);
    // Anschreibentext
    doc.moveDown();
    doc.text(`Sehr geehrte/r Herr/Frau ${user.lastName},`);
    doc.moveDown(1);
    doc.text(`wir freuen uns, Ihnen Ihre persönliche Ladekarte zusenden zu dürfen.`);

    doc.text(
      `Diese Ladekarte ermöglicht Ihnen den Zugang zu den Ladestationen Ihres Arbeitgebers oder Ihres Ladeclubs.`,
    );
    doc.font("public/SourceSans/SourceSans3-Bold.ttf", 10);
    doc.moveDown(1);
    doc.text("Aktivierung Ihrer persönlichen Ladekarte");
    doc.moveDown(2);
    doc.font("public/SourceSans/SourceSans3-Regular.ttf");

    doc.text(`Besuchen Sie unser Ladeportal unter app.eulektro.de und loggen Sie sich in Ihr Benutzerkonto ein. Navigieren Sie dort zum Menüpunkt „Ladekarten“.
Geben Sie dort nun bitte die Nummer, die auf der Rückseite Ihrer Ladekarte aufgedruckt ist, in das dafür vorgesehene Feld ein, um Ihre persönliche Ladekarte zu aktivieren.
Sobald die Aktivierung Ihrer Ladekarte abgeschlossen ist, können Sie alle verfügbaren Ladepunkte nutzen und erhalten eine klar aufgeschlüsselte Übersicht Ihrer Ladevorgänge und der entstandenen Kosten.`);
    doc.moveDown(1);
    doc.text(`Bei Fragen stehen wir Ihnen gerne zur Verfügung.`);
    doc.moveDown(2);

    doc.text("Mit freundlichen Grüßen,");
    doc.moveDown(2);
    doc.text("Team Eulektro");
    drawFooterAndLogo(doc, true, 22);
    doc.end();
    // Warten auf das Finish-Ereignis des WriteStreams
    await new Promise((resolve, reject) => {
      writeStream.on("finish", resolve);
      writeStream.on("error", reject);
    });

    // Asynchron das PDF lesen
    const pdf = await fs.promises.readFile(filePath);
    return new NextResponse(pdf, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${
          env.NODE_ENV != "production" ? "_DEV_" : ""
        }$${userId}_welcome.pdf"`,
      },
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      return NextResponse.json(
        {
          message: error.message,
        },
        { status: 500 },
      );
    }
  }

  return NextResponse.json(
    {
      message: "error",
    },
    { status: 500 },
  );
}
