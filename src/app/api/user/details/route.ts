import type { NextRequest } from "next/server";
import prisma from "../../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return false;
  }

  const userid = session?.user.id;

  const currentDate = new Date();

  const user = await prisma.user.findFirst({
    where: {
      id: userid,
      address: {
        some: {
          validFrom: {
            lte: currentDate,
          },
          validTo: {
            gte: currentDate,
          },
        },
      },
    },
    include: {
      address: true,
    },
  });
  if (user && user.address[0]) {
    return NextResponse.json(
      {
        name: user.name,
        lastName: user.lastName,
        email: user.email,
        street: user.address[0].street,
        streetNr: user.address[0].streetNr,
        city: user.address[0].city,
        zip: user.address[0].zip,
        country: user.address[0].country,
      },
      { status: 200 },
    );
  }
  return NextResponse.json({ error: "Error fetching user details" }, { status: 500 });
}
