import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";

export const revalidate = 0;

const getLogData = async () => {
  return await prisma.log.findMany({
    orderBy: {
      timestamp: "desc",
    },
    take: 1000,
  });
};

export async function GET(request: NextRequest) {
  const data = await getLogData();

  return NextResponse.json(data);
}
