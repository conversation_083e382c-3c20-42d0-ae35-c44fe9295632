import { ConnectorStatus } from "~/app/api/realtime/route";

export interface OperationalStatusCount {
  [key: string]: number;
}
export const countOperationalStatus = (
  connectorStatusList: ConnectorStatus[],
): OperationalStatusCount => {
  return connectorStatusList.reduce(
    (accumulator: OperationalStatusCount, connector) => {
      // Der operationalStatus des aktuellen Connectors wird als Schlüssel verwendet
      const { operationalStatus } = connector;

      // Wenn der operationalStatus bereits im Akkumulator ist, erhöhe den <PERSON>hler, sonst setze ihn auf 1
      accumulator[operationalStatus] = (accumulator[operationalStatus] || 0) + 1;

      return accumulator;
    },
    {
      Available: 0,
      Preparing: 0,
      Charging: 0,
      SuspendedEV: 0,
      SuspendedEVSE: 0,
      Finishing: 0,
      Faulted: 0,
      Unavailable: 0,
    },
  ); // Initialisiere den Akkumulator als leeres Objekt
};
