import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { generateCreditTask } from "~/server/task/generateCredit";
import { z } from "zod";
import { NextResponse } from "next/server";

const CreditFormValues = z.object({
  contactId: z.string().optional(),
  contactIds: z.array(z.string()),
  kindOfContact: z.string(),
  dateRange: z.tuple([
    z.string().transform((value) => new Date(value)),
    z.string().transform((value) => new Date(value)),
  ]),
  onlyPaid: z.boolean(),
});

export async function POST(request: NextRequest) {
  const data = await request.json();
  const parsed = CreditFormValues.safeParse(data);

  if (!parsed.success) {
    return new Response("Invalid input", { status: 400 });
  }

  const { contactId, contactIds, kindOfContact, dateRange, onlyPaid } = parsed.data;

  if (!contactId && !contactIds) {
    return new Response("no contact found", { status: 500 });
  }

  try {
    const results = await Promise.allSettled(
      contactIds.map((contactId) =>
        generateCreditTask({
          contactId,
          dateRange,
          onlyPaid,
        }),
      ),
    );

    const successfulInvoices = results.filter(
      (result) =>
        result.status === "fulfilled" &&
        result.value && // Sicherstellen, dass value vorhanden ist
        "id" in result.value, // Prüfen, ob die id-Eigenschaft existiert
    );
    const errors = results.filter((result) => result.status === "rejected");

    if (successfulInvoices.length > 0) {
      return new Response(
        JSON.stringify({
          message: "Some or all credit invoices were successfully created.",
          successCount: successfulInvoices.length,
          errorCount: errors.length,
          errorMessages: `${errors
            .map((error) => ("reason" in error ? error.reason : ""))
            .join(",")}`,
        }),
        { status: 200, headers: { "Content-Type": "application/json" } },
      );
    } else {
      return new Response(
        JSON.stringify({
          message: "No credit invoices were created.",
          errorCount: errors.length,
          errorMessages: `${errors
            .map((error) => ("reason" in error ? error.reason : ""))
            .join(",")}`,
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  } catch (e) {
    if (e instanceof Error) {
      return NextResponse.json(e.message, { status: 500 });
    }
  }

  return new Response("no invoice", { status: 500 });
}
