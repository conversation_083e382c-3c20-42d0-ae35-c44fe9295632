import type { NextRequest } from "next/server";
import prisma from "../../../../../server/db/prisma";

import { NextResponse } from "next/server";

export const revalidate = 0;

interface Props {
  params: {
    slug: string;
  };
}

export async function PUT(request: NextRequest, { params }: Props) {
  const data = await request.json();
  delete data.contactId;
  delete data.tokens;
  const tokenGroup = await prisma.tokenGroup.update({
    where: { id: params.slug },
    data: {
      ...data,
    },
    include: { tokens: true, contact: true },
  });

  return NextResponse.json(tokenGroup);
}
