import type { TokenGroup } from "@prisma/client";
import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";

import { NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const tokenGroups = await prisma.tokenGroup.findMany({});

  return NextResponse.json(tokenGroups);
}

export async function POST(request: NextRequest) {
  const tokenGroup = (await request.json()) as TokenGroup;

  try {
    const createdTokenGrojup = await prisma.tokenGroup.create({
      data: {
        ...tokenGroup,
      },
    });
    if (createdTokenGrojup.name == tokenGroup.name) {
      return NextResponse.json("ok");
    } else {
      return NextResponse.next({
        status: 500,
        statusText: "cant create tokengroup",
      });
    }
  } catch (e) {
    return NextResponse.next({
      status: 500,
      statusText: "cant create tokengroup",
    });
  }
}
