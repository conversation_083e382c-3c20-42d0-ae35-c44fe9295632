import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";

import { NextResponse } from "next/server";
import type { Token } from "@prisma/client";

export const revalidate = 0;

export async function DELETE(request: NextRequest) {
  const token = (await request.json()) as Token;
  const tokenGroups = await prisma.token.delete({
    where: {
      id: token.id,
    },
  });

  return NextResponse.json(tokenGroups);
}

export async function POST(request: NextRequest) {
  const token = (await request.json()) as Token;

  try {
    const createdToken = await prisma.token.create({
      data: token,
    });
    if (createdToken) {
      return NextResponse.json(createdToken);
    } else {
      return NextResponse.next({
        status: 500,
        statusText: "cant create tokengroup",
      });
    }
  } catch (e) {
    return NextResponse.next({
      status: 500,
      statusText: "cant create tokengroup",
    });
  }
}
