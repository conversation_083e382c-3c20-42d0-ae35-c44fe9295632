import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { z } from "zod";
import { Role } from "@prisma/client";
import { PrismaClient } from "@prisma/client";
import { PrismaClient as PrismaClientMongo } from "~/../prismaMongoAdhoc/client";

const prisma = new PrismaClient();
const prismaMongo = new PrismaClientMongo();

const LocationPriceSchema = z.object({
  id: z.string().optional(),
  locationId: z.string().optional(),
  start: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid date format",
  }),
  end: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid date format",
  }),
  energy_price: z.number().min(0),
  blocking_fee: z.number().min(0),
  current_type: z.enum(["AC", "DC"]).default("AC"),
  parking_price: z.number().min(0).optional(),
  blocking_fee_start: z.number().min(0),
  blocking_fee_max: z.number().min(0),
  session_fee: z.number().min(0),
  tax_rate: z.number().min(0).max(100),
  empId: z.string().optional(),
});

import { getEmpIdsByOu } from "~/server/model/contact/func";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Wenn der Benutzer ein Admin ist, zeige alle LocationPrices an
    if (session?.user?.role === Role.ADMIN) {
      const locationPrices = await prismaMongo.locationPrice.findMany({
        include: {
          location: true,
          Emp: true,
        },
      });
      return NextResponse.json(locationPrices);
    }

    // Für andere Benutzer: Filtere nach der ausgewählten OU
    const selectedOu = session.user.selectedOu;
    if (!selectedOu) {
      return NextResponse.json({ error: "No selected OU" }, { status: 400 });
    }

    // Finde alle EMP-IDs, die mit Contacts verbunden sind, die zur ausgewählten OU gehören
    const empIds = await getEmpIdsByOu(selectedOu);

    // Wenn keine EMP-IDs gefunden wurden, gib eine leere Liste zurück
    if (empIds.length === 0) {
      return NextResponse.json([]);
    }

    // Finde alle LocationPrices, die mit diesen EMP-IDs verbunden sind
    const locationPrices = await prismaMongo.locationPrice.findMany({
      where: {
        empId: { in: empIds },
      },
      include: {
        location: true,
        Emp: true,
      },
    });

    return NextResponse.json(locationPrices);
  } catch (error) {
    console.error("Error fetching location prices:", error);
    return NextResponse.json({ error: "Failed to fetch location prices" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { data } = await request.json();
    const validated = LocationPriceSchema.safeParse(data);

    if (!validated.success) {
      return NextResponse.json({ error: validated.error.format() }, { status: 400 });
    }

    const {
      id,
      locationId,
      start,
      end,
      energy_price,
      blocking_fee,
      current_type,
      parking_price,
      blocking_fee_start,
      blocking_fee_max,
      session_fee,
      tax_rate,
      empId
    } = validated.data;

    if (id) {
      // Update existing price
      const updatedPrice = await prismaMongo.locationPrice.update({
        where: { id },
        data: {
          locationId,
          start: new Date(start),
          end: new Date(end),
          energy_price,
          blocking_fee,
          current_type,
          parking_price,
          blocking_fee_start,
          blocking_fee_max,
          session_fee,
          tax_rate,
          empId,
        },
      });

      return NextResponse.json(updatedPrice);
    } else {
      // Create new price
      const newPrice = await prismaMongo.locationPrice.create({
        data: {
          locationId,
          start: new Date(start),
          end: new Date(end),
          energy_price,
          blocking_fee,
          current_type,
          parking_price,
          blocking_fee_start,
          blocking_fee_max,
          session_fee,
          tax_rate,
          empId,
        },
      });

      return NextResponse.json(newPrice);
    }
  } catch (error) {
    console.error("Error creating/updating location price:", error);
    return NextResponse.json({ error: "Failed to create/update location price" }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
    }

    await prismaMongo.locationPrice.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting location price:", error);
    return NextResponse.json({ error: "Failed to delete location price" }, { status: 500 });
  }
}
