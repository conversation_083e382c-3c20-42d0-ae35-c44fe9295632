import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const sensors = await prisma.parkingSensor.findMany();

  return NextResponse.json(sensors);
}

export async function POST(request: NextRequest) {
  try {
    console.log("dummy");
  } catch (e) {
    return new Response("cant update", { status: 500 });
  }

  return new Response("ok", { status: 200 });
}
