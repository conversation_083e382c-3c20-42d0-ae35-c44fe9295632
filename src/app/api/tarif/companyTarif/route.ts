import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  const internal = request.nextUrl.searchParams.get("internal");
  const user = session?.user;
  if (!session || !user) {
    return NextResponse.json("No Login", { status: 404 });
  }

  const userOuid = user?.ou?.id;
  const companyTariffs = await prisma.companyTarif.findMany({
    where: {
      ouId: userOuid,
      internal: !!internal,
    },
  });

  return NextResponse.json(companyTariffs);
}

export async function POST(request: NextRequest) {
  const { data } = await request.json();
  const session = await getServerSession(authOptions);
  if (!session || !session?.user) {
    return NextResponse.json("No Login", { status: 404 });
  }

  const ouId = session?.user?.selectedOu?.id;

  if (data && ouId) {
    try {
      if (data?.id) {
        return NextResponse.json(
          { success: false, message: "Updating tarif not yet implemented" },
          { status: 403 },
        );
        await prisma.companyTarif.update({
          where: {
            id: data?.id || 0,
          },
          data: data,
        });
      } else {
        if (
          // wenn kWH preis = 0 dann muss alles 0 sein
          Number(data.energyPrice) == 0 &&
          Number(data.sessionPrice) + Number(data.blockingFee) > 0
        ) {
          return NextResponse.json(
            { success: false, message: "Energycost must be > 0 if session or blocking fee is set" },
            { status: 500 },
          );
        }

        await prisma.companyTarif.create({
          data: {
            ...data,
            energyPrice: +data.energyPrice,
            minChargingTime: +data.minChargingTime,
            minChargingEnergy: +data.minChargingEnergy,
            sessionPrice: +data.sessionPrice,
            blockingFee: +data.blockingFee,
            blockingFeeMax: +data.blockingFeeMax,
            blockingFeeBeginAtMin: data.blockingFeeBeginAtMin ? +data.blockingFeeBeginAtMin : 0,
            oneTimeFee: +data.oneTimeFee,
            basicFee: +data.basicFee,
            ouId: ouId,
            internal: data.internal,
          },
        });
      }
      return NextResponse.json(data);
    } catch (e) {
      return NextResponse.json({ success: false, message: "DB Error" }, { status: 500 });
    }
  } else {
    return NextResponse.json({ success: false, message: "Invalid data" }, { status: 500 });
  }
}
