import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
export async function POST(request: NextRequest) {
  const { data } = await request.json();
  if (data) {
    try {
      if (data?.id) {
        await prisma.creditTarif.update({
          where: {
            id: data?.id || 0,
          },
          data: data,
        });
      } else {
        await prisma.creditTarif.create({
          data: {
            ...data,
            sessionCredit: +data.sessionCredit,
            energyCredit: +data.energyCredit,
            blockingCredit: +data.blockingCredit,
            maxBlockingCredit: +data.maxBlockingCredit,
            blockingFeeMinStart: data.blockingFeeMinStart ? +data.blockingFeeMinStart : 0,
          },
        });
      }
      return NextResponse.json(data);
    } catch (e) {
      return NextResponse.json({ success: false, message: "DB Error" }, { status: 500 });
    }
  } else {
    return NextResponse.json({ success: false, message: "Invalid data" }, { status: 500 });
  }
}
