import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { KindOfTarif } from "@prisma/client";
export async function POST(request: NextRequest) {
  const { data } = await request.json();
  if (data) {
    try {
      if (data?.id) {
        await prisma.tarif.update({
          where: {
            id: data?.id || 0,
          },
          data: {
            ...data,
            kwh: +data.kwh,
            minChargingTime: +data.minChargingTime,
            minChargingEnergy: +data.minChargingEnergy,
            sessionFee: +data.sessionFee,
            validFrom: new Date(data.validFrom),
            validTo: new Date(data.validTo),
            blockingFee: +data.blockingFee,
            blockingFeeMax: +data.blockingFeeMax,
            blockingFeeBeginAtMin: +data.blockingFeeBeginAtMin,
            kindOfTarif: data.kindOfTarif,
            contractId: data.kindOfTarif == KindOfTarif.DIRECT ? data.contractId : null,
          },
        });
      } else {
        await prisma.tarif.create({
          data: {
            ...data,
            kwh: +data.kwh,
            sessionFee: +data.sessionFee,
            minChargingTime: +data.minChargingTime,
            minChargingEnergy: +data.minChargingEnergy,
            validFrom: new Date(data.validFrom),
            validTo: new Date(data.validTo),
            blockingFee: +data.blockingFee,
            blockingFeeMax: +data.blockingFeeMax,
            blockingFeeBeginAtMin: +data.blockingFeeBeginAtMin,
            kindOfTarif: data.kindOfTarif,
            contractId: data.kindOfTarif == KindOfTarif.DIRECT ? data.contractId : null,
          },
        });
      }
      return NextResponse.json(data);
    } catch (e: unknown) {
      if (e instanceof Error) {
        return NextResponse.json(
          { success: false, message: `DB Error: ${e.message} ` },
          { status: 500 },
        );
      }
      return NextResponse.json({ success: false, message: `DB Error` }, { status: 500 });
    }
  } else {
    return NextResponse.json({ success: false, message: "Invalid data" }, { status: 500 });
  }
}
