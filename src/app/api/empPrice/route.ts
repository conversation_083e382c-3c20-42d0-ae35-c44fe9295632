import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { z } from "zod";
import { Role } from "@prisma/client";
import { PrismaClient } from "@prisma/client";
import { PrismaClient as PrismaClientMongo } from "~/../prismaMongoAdhoc/client";

const prisma = new PrismaClient();
const prismaMongo = new PrismaClientMongo();

const EMPPriceSchema = z.object({
  id: z.string().optional(),
  start: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid date format",
  }),
  end: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid date format",
  }),
  energy_price: z.number().min(0),
  blocking_fee: z.number().min(0),
  blocking_fee_start: z.number().min(0),
  blocking_fee_max: z.number().min(0),
  session_fee: z.number().min(0),
  tax_rate: z.number().min(0).max(100),
  empId: z.string().optional(),
  current_type: z.enum(["AC", "DC"]).default("AC"),
});

import { getEmpIdsByOu } from "~/server/model/contact/func";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Wenn der Benutzer ein Admin ist, zeige alle EMPPrices an
    if (session?.user?.role === Role.ADMIN) {
      const empPrices = await prismaMongo.empPrice.findMany({
        include: {
          Emp: true,
        },
      });
      return NextResponse.json(empPrices);
    }

    // Für andere Benutzer: Filtere nach der ausgewählten OU
    const selectedOu = session.user.selectedOu;
    if (!selectedOu) {
      return NextResponse.json({ error: "No selected OU" }, { status: 400 });
    }

    // Finde alle EMP-IDs, die mit Contacts verbunden sind, die zur ausgewählten OU gehören
    const empIds = await getEmpIdsByOu(selectedOu);

    // Wenn keine EMP-IDs gefunden wurden, gib eine leere Liste zurück
    if (empIds.length === 0) {
      return NextResponse.json([]);
    }

    // Finde alle EMPPrices, die mit diesen EMP-IDs verbunden sind
    const empPrices = await prismaMongo.empPrice.findMany({
      where: {
        empId: { in: empIds },
      },
      include: {
        Emp: true,
      },
    });

    return NextResponse.json(empPrices);
  } catch (error) {
    console.error("Error fetching EMP prices:", error);
    return NextResponse.json({ error: "Failed to fetch EMP prices" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { data } = await request.json();
    const validated = EMPPriceSchema.safeParse(data);

    if (!validated.success) {
      return NextResponse.json({ error: validated.error.format() }, { status: 400 });
    }

    const {
      id,
      start,
      end,
      energy_price,
      blocking_fee,
      blocking_fee_start,
      blocking_fee_max,
      session_fee,
      tax_rate,
      empId,
      current_type
    } = validated.data;

    if (id) {
      // Update existing price
      const updatedPrice = await prismaMongo.empPrice.update({
        where: { id },
        data: {
          start: new Date(start),
          end: new Date(end),
          energy_price,
          blocking_fee,
          blocking_fee_start,
          blocking_fee_max,
          session_fee,
          tax_rate,
          empId,
          current_type,
        },
      });

      return NextResponse.json(updatedPrice);
    } else {
      // Create new price
      const newPrice = await prismaMongo.empPrice.create({
        data: {
          start: new Date(start),
          end: new Date(end),
          energy_price,
          blocking_fee,
          blocking_fee_start,
          blocking_fee_max,
          session_fee,
          tax_rate,
          empId,
          current_type,
        },
      });

      return NextResponse.json(newPrice);
    }
  } catch (error) {
    console.error("Error creating/updating EMP price:", error);
    return NextResponse.json({ error: "Failed to create/update EMP price" }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
    }

    await prismaMongo.empPrice.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting EMP price:", error);
    return NextResponse.json({ error: "Failed to delete EMP price" }, { status: 500 });
  }
}
