import type { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
  const { uid, visualNumber, contactId, active, userGroupId } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session || !uid || !visualNumber) {
    return new Response("no auth", { status: 401 });
  }
  try {
    const cardExists = await prisma.physicalCard.findUnique({
      where: { uid: uid },
    });
    if (cardExists) {
      return new Response("Card already exists - please check UID", { status: 422 });
    }
  } catch (e) {
    return new Response("Error checking existence", { status: 500 });
  }

  // Validate userGroup if provided
  if (userGroupId) {
    try {
      const userGroup = await prisma.userGroup.findUnique({
        where: { id: userGroupId },
        include: { ou: true },
      });
      if (!userGroup) {
        return new Response("UserGroup not found", { status: 400 });
      }

      // Check if user has access to this UserGroup's OU
      if (session.user.role !== "ADMIN" && userGroup.ouId !== session.user.selectedOu.id) {
        return new Response("UserGroup belongs to different OU", { status: 403 });
      }
    } catch (e) {
      return new Response("Error validating UserGroup", { status: 500 });
    }
  }

  try {
    await prisma.physicalCard.create({
      data: {
        uid: uid,
        visualNumber: visualNumber,
        valid: active,
        cpoId: contactId,
        userGroupId: userGroupId || null,
      },
    });
    return new Response("ok", { status: 200 });
  } catch (e) {
    return new Response("cant create", { status: 500 });
  }
}
