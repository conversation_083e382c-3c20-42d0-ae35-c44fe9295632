import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { contactId } = await request.json();

    if (!contactId) {
      return NextResponse.json({ error: "contactId is required" }, { status: 400 });
    }

    // Find the contact and verify it's a CPO
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
      include: { ou: true },
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    if (!contact.cpo) {
      return NextResponse.json({ error: "Contact is not a CPO" }, { status: 400 });
    }

    if (!contact.ouId) {
      return NextResponse.json({ error: "Contact has no OU assigned" }, { status: 400 });
    }

    // Find all CPO users in the OU
    const cpoUsers = await prisma.user.findMany({
      where: {
        ouId: contact.ouId,
        role: Role.CPO,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    if (cpoUsers.length === 0) {
      return NextResponse.json({ error: "No CPO users found in the OU" }, { status: 404 });
    }

    // Create test notifications for all CPO users in the OU
    const notificationPromises = cpoUsers.map(user =>
      prisma.systemNotification.create({
        data: {
          nachricht: `Test: Eine Gutschrift für ${contact.name || 'CPO'} wurde abgeschlossen. Zeitraum: 01.01.2024 - 31.01.2024. Betrag: 150.75€. Gutschrift-Nr: TEST-GU2024-${Date.now()}`,
          type: NotificationType.INFO,
          userId: user.id,
        },
      })
    );

    const notifications = await Promise.all(notificationPromises);

    return NextResponse.json({
      success: true,
      contact: {
        id: contact.id,
        name: contact.name,
        cpo: contact.cpo,
        ouId: contact.ouId,
        ouName: contact.ou?.name,
      },
      users: cpoUsers,
      notifications: notifications.length,
      message: `Test notifications created for ${cpoUsers.length} CPO users in OU: ${contact.ou?.name}`
    });
  } catch (error) {
    console.error("Error creating test credit notifications:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
