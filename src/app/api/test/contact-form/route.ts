import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, NotificationType } from "@prisma/client";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Create test contact form notification
    await createSystemNotificationForAdmins({
      nachricht: `🟡 Test: Neue Kontaktanfrage von <PERSON> (<EMAIL>): "Problem mit Ladevorgang" (Benutzer: ${session.user.name}). Nachricht: Ich habe Probleme beim Laden meines Elektrofahrzeugs an der Station...`,
      type: NotificationType.INFO,
    });

    return NextResponse.json({ 
      success: true, 
      message: "Test contact form notification created successfully" 
    });
  } catch (error) {
    console.error("Error creating test contact form notification:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
