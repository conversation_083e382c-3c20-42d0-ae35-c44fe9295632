import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

const UpdateUserGroupSchema = z.object({
  name: z.string().min(1, "Name ist erforderlich").optional(),
  description: z.string().optional(),
});

interface Props {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userGroup = await prisma.userGroup.findUnique({
      where: { id: params.id },
      include: {
        ou: {
          select: {
            id: true,
            name: true,
          },
        },
        users: {
          select: {
            id: true,
            name: true,
            lastName: true,
            email: true,
            role: true,
          },
        },
        physicalCards: {
          select: {
            uid: true,
            visualNumber: true,
            valid: true,
          },
        },
        companyTarifs: {
          include: {
            tarif: {
              select: {
                id: true,
                name: true,
                energyPrice: true,
                sessionPrice: true,
                validFrom: true,
                validTo: true,
              },
            },
          },
        },
      },
    });

    if (!userGroup) {
      return NextResponse.json({ error: "User group not found" }, { status: 404 });
    }

    // Check if user has permission to view this user group
    if (session.user.role !== Role.ADMIN && userGroup.ouId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    return NextResponse.json(userGroup);
  } catch (error) {
    console.error("Error fetching user group:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const result = UpdateUserGroupSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.issues }, { status: 400 });
    }

    // Check if user group exists and user has permission
    const existingUserGroup = await prisma.userGroup.findUnique({
      where: { id: params.id },
    });

    if (!existingUserGroup) {
      return NextResponse.json({ error: "User group not found" }, { status: 404 });
    }

    if (session.user.role !== Role.ADMIN && existingUserGroup.ouId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    // Check for name conflicts if name is being updated
    if (result.data.name && result.data.name !== existingUserGroup.name) {
      const nameConflict = await prisma.userGroup.findFirst({
        where: {
          name: result.data.name,
          ouId: existingUserGroup.ouId,
          id: { not: params.id },
        },
      });

      if (nameConflict) {
        return NextResponse.json(
          { error: "Eine Nutzergruppe mit diesem Namen existiert bereits in dieser OU" },
          { status: 409 }
        );
      }
    }

    const userGroup = await prisma.userGroup.update({
      where: { id: params.id },
      data: result.data,
      include: {
        ou: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            users: true,
            physicalCards: true,
            companyTarifs: true,
          },
        },
      },
    });

    return NextResponse.json(userGroup);
  } catch (error) {
    console.error("Error updating user group:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Check if user group exists and user has permission
    const existingUserGroup = await prisma.userGroup.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            users: true,
            physicalCards: true,
          },
        },
      },
    });

    if (!existingUserGroup) {
      return NextResponse.json({ error: "User group not found" }, { status: 404 });
    }

    if (session.user.role !== Role.ADMIN && existingUserGroup.ouId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    // Check if user group is still in use
    if (existingUserGroup._count.users > 0 || existingUserGroup._count.physicalCards > 0) {
      return NextResponse.json(
        { error: "Nutzergruppe kann nicht gelöscht werden, da sie noch von Benutzern oder Karten verwendet wird" },
        { status: 409 }
      );
    }

    await prisma.userGroup.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: "User group deleted successfully" });
  } catch (error) {
    console.error("Error deleting user group:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
