import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user) {
    return NextResponse.json("no auth", { status: 401 });
  }

  const ouid = session?.user?.selectedOu?.id;
  const location = await prisma.location.findMany({ where: { ouId: ouid } });

  return NextResponse.json(location);
}
