import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { name, description } = await request.json();
    const { id } = params;

    if (!name) {
      return NextResponse.json(
        { error: "Name is required" },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await prisma.maintenanceCategory.findUnique({
      where: { id }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if name is already taken by another category
    const nameConflict = await prisma.maintenanceCategory.findFirst({
      where: {
        name,
        id: { not: id }
      }
    });

    if (nameConflict) {
      return NextResponse.json(
        { error: "Category with this name already exists" },
        { status: 409 }
      );
    }

    const updatedCategory = await prisma.maintenanceCategory.update({
      where: { id },
      data: {
        name,
        description
      }
    });

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error("Error updating maintenance category:", error);
    return NextResponse.json(
      { error: "Failed to update maintenance category" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { id } = params;

    // Check if category exists
    const existingCategory = await prisma.maintenanceCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { maintenanceRecords: true }
        }
      }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Don't allow deletion of default categories
    if (existingCategory.isDefault) {
      return NextResponse.json(
        { error: "Cannot delete default categories" },
        { status: 400 }
      );
    }

    // Don't allow deletion if category has maintenance records
    if (existingCategory._count.maintenanceRecords > 0) {
      return NextResponse.json(
        { error: "Cannot delete category with existing maintenance records" },
        { status: 400 }
      );
    }

    await prisma.maintenanceCategory.delete({
      where: { id }
    });

    return NextResponse.json({ message: "Category deleted successfully" });
  } catch (error) {
    console.error("Error deleting maintenance category:", error);
    return NextResponse.json(
      { error: "Failed to delete maintenance category" },
      { status: 500 }
    );
  }
}
