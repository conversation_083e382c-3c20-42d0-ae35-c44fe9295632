import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, MaintenanceTargetType } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const {
      date,
      description,
      notes,
      targetType,
      locationId,
      evseId,
      connectorId,
      categoryId
    } = await request.json();
    const { id } = params;

    if (!date || !description || !targetType || !categoryId) {
      return NextResponse.json(
        { error: "Date, description, targetType, and categoryId are required" },
        { status: 400 }
      );
    }

    // Validate targetType
    if (!Object.values(MaintenanceTargetType).includes(targetType)) {
      return NextResponse.json(
        { error: "Invalid targetType" },
        { status: 400 }
      );
    }

    // Check if record exists
    const existingRecord = await prisma.maintenanceRecord.findUnique({
      where: { id }
    });

    if (!existingRecord) {
      return NextResponse.json(
        { error: "Maintenance record not found" },
        { status: 404 }
      );
    }

    // Validate category exists
    const category = await prisma.maintenanceCategory.findUnique({
      where: { id: categoryId }
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Calculate next due date for DGUV-V3 (50 weeks)
    let nextDueDate = null;
    if (category.name === "DGUV-V3") {
      const maintenanceDate = new Date(date);
      nextDueDate = new Date(maintenanceDate);
      nextDueDate.setDate(nextDueDate.getDate() + (50 * 7)); // 50 weeks
    }

    const updatedRecord = await prisma.maintenanceRecord.update({
      where: { id },
      data: {
        date: new Date(date),
        description,
        notes,
        targetType,
        locationId: locationId || null,
        evseId: evseId || null,
        connectorId: connectorId || null,
        categoryId,
        nextDueDate
      },
      include: {
        category: true,
        user: {
          select: {
            id: true,
            name: true,
            lastName: true
          }
        },
        location: {
          select: {
            id: true,
            name: true
          }
        },
        evse: {
          select: {
            uid: true,
            evse_id: true
          }
        }
      }
    });

    return NextResponse.json(updatedRecord);
  } catch (error) {
    console.error("Error updating maintenance record:", error);
    return NextResponse.json(
      { error: "Failed to update maintenance record" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { id } = params;

    // Check if record exists
    const existingRecord = await prisma.maintenanceRecord.findUnique({
      where: { id }
    });

    if (!existingRecord) {
      return NextResponse.json(
        { error: "Maintenance record not found" },
        { status: 404 }
      );
    }

    await prisma.maintenanceRecord.delete({
      where: { id }
    });

    return NextResponse.json({ message: "Maintenance record deleted successfully" });
  } catch (error) {
    console.error("Error deleting maintenance record:", error);
    return NextResponse.json(
      { error: "Failed to delete maintenance record" },
      { status: 500 }
    );
  }
}
