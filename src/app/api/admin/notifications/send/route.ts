import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";

interface NotificationRequest {
  message: string;
  type: NotificationType;
  targetType: "user" | "ou" | "role";
  targetId: string;
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { message, type, targetType, targetId }: NotificationRequest = await request.json();

    if (!message || !type || !targetType || !targetId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    let userIds: string[] = [];

    switch (targetType) {
      case "user":
        // Send to specific user
        const user = await prisma.user.findUnique({
          where: { id: targetId },
          select: { id: true },
        });
        
        if (!user) {
          return NextResponse.json(
            { error: "User not found" },
            { status: 404 }
          );
        }
        
        userIds = [user.id];
        break;

      case "ou":
        // Send to all users in OU
        const ouUsers = await prisma.user.findMany({
          where: { ouId: targetId },
          select: { id: true },
        });
        
        if (ouUsers.length === 0) {
          return NextResponse.json(
            { error: "No users found in the selected OU" },
            { status: 404 }
          );
        }
        
        userIds = ouUsers.map(user => user.id);
        break;

      case "role":
        // Send to all users with specific role
        const roleUsers = await prisma.user.findMany({
          where: { role: targetId as Role },
          select: { id: true },
        });
        
        if (roleUsers.length === 0) {
          return NextResponse.json(
            { error: "No users found with the selected role" },
            { status: 404 }
          );
        }
        
        userIds = roleUsers.map(user => user.id);
        break;

      default:
        return NextResponse.json(
          { error: "Invalid target type" },
          { status: 400 }
        );
    }

    // Create notifications for all target users
    const notifications = await prisma.systemNotification.createMany({
      data: userIds.map(userId => ({
        nachricht: message,
        type: type,
        userId: userId,
      })),
    });

    return NextResponse.json({
      success: true,
      count: notifications.count,
      message: `Notification sent to ${notifications.count} users`,
    });

  } catch (error) {
    console.error("Error sending notifications:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
