import type { NextRequest } from "next/server";
import prisma from "../../../../server/db/prisma";
import { NextResponse } from "next/server";
import { endOfDay, startOfDay } from "~/utils/date/date";

interface Props {
  params: {
    slug: string;
  };
}

export async function PUT(request: NextRequest, { params }: Props) {
  const contractId = params.slug;

  const { contract } = await request.json();

  await prisma.powerContract.update({
    where: {
      id: contractId,
    },
    data: {
      ...contract,
      basePrice: parseFloat(contract.basePrice),
      kwhPrice: parseFloat(contract.kwhPrice),
      start: startOfDay(contract.start),
      end: endOfDay(contract.end),
    },
  });

  return NextResponse.json("ok");
}
