import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const ouId = searchParams.get("ouId");
  const userGroupId = searchParams.get("userGroupId");

  try {
    const targetOuId = ouId || session.user.selectedOu.id;

    // Validate OU access
    if (session.user.role !== Role.ADMIN && targetOuId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    // Validate UserGroup belongs to same OU if provided
    if (userGroupId) {
      const userGroup = await prisma.userGroup.findUnique({
        where: { id: userGroupId },
      });

      if (!userGroup) {
        return NextResponse.json({ error: "UserGroup not found" }, { status: 404 });
      }

      if (userGroup.ouId !== targetOuId) {
        return NextResponse.json({ error: "UserGroup belongs to different OU" }, { status: 400 });
      }
    }

    // Get all company tarifs for the OU
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const allTarifs = await prisma.companyTarif.findMany({
      where: {
        ouId: targetOuId,
        validFrom: {
          lte: today,
        },
        validTo: {
          gte: today,
        },
      },
      include: {
        userGroups: {
          include: {
            userGroup: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Filter tarifs based on userGroup restrictions
    const availableTarifs = allTarifs.filter((tarif) => {
      // If tarif has no userGroup restrictions, it's available for everyone
      if (tarif.userGroups.length === 0) {
        return true;
      }

      // If user has no userGroup, they can only access tarifs without restrictions
      if (!userGroupId) {
        return false;
      }

      // Check if user's userGroup is allowed for this tarif
      return tarif.userGroups.some(
        (tarifUserGroup) => tarifUserGroup.userGroup.id === userGroupId
      );
    });

    // Transform the response to remove the userGroups relation
    const response = availableTarifs.map((tarif) => ({
      id: tarif.id,
      name: tarif.name,
      energyPrice: tarif.energyPrice,
      sessionPrice: tarif.sessionPrice,
      minChargingTime: tarif.minChargingTime,
      minChargingEnergy: tarif.minChargingEnergy,
      blockingFee: tarif.blockingFee,
      blockingFeeBeginAtMin: tarif.blockingFeeBeginAtMin,
      blockingFeeMax: tarif.blockingFeeMax,
      currentType: tarif.currentType,
      validFrom: tarif.validFrom,
      validTo: tarif.validTo,
      optional: tarif.optional,
      description: tarif.description,
      ouId: tarif.ouId,
      oneTimeFee: tarif.oneTimeFee,
      basicFee: tarif.basicFee,
      oneTimeFeePayer: tarif.oneTimeFeePayer,
      kindOfTarif: tarif.kindOfTarif,
      internal: tarif.internal,
      allowedUserGroups: tarif.userGroups.map((ug) => ug.userGroup.name),
    }));

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching available tarifs:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
