import { NextRequest, NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import nodemailer from "nodemailer";
import { env } from "~/env.js";

export async function POST(request: NextRequest) {
  const { email } = await request.json();

  const user = await prisma.user.findUnique({
    where: { email },
  });

  if (!user) {
    return NextResponse.json(
      {
        message:
          "Vielen Dank - Sollte es sich um eine gültige E-Mail Adresse handeln, prüfen Sie bitte Ihre Emails",
      },
      { status: 200 },
    );
  }

  const token = Math.random().toString(36).substring(2, 15); // Einfacher Weg, ein Token zu generieren

  try {
    await prisma.user.update({
      where: { id: user.id },
      data: {
        signUpHash: token,
      },
    });

    // Create transporter
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
    });

    // Send email
    await transporter.sendMail({
      from: "<EMAIL>",
      to: user.email,
      subject: "Ladeportal - Passwort zurücksetzen",
      text: `Moin ${user.name} ${user.lastName}!\nÜber diesen Link können Sie ihr Passwort neu setzen:  ${env.NEXT_PUBLIC_SITE_URL}/passwortReset/${token}/new`,
    });

    return NextResponse.json(
      {
        message:
          "Vielen Dank - Sollte es sich um eine gültige E-Mail Adresse handeln, prüfen Sie bitte Ihre Emails",
      },
      { status: 200 },
    );
  } catch (error) {
    return NextResponse.json(
      {
        message: "Fehler beim Erstellen des Tokens oder Senden der E-Mail",
      },
      { status: 500 },
    );
  }
}
