import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function GET(request: NextRequest, res: NextResponse) {
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN || session?.user?.role == Role.USER)) {
    return NextResponse.json(
      {
        message: "no auth",
      },
      { status: 401 },
    );
  }

  const cardId = request.nextUrl.searchParams.get("cardId");
  const visualNumber = request.nextUrl.searchParams.get("visualNumber");
  if (cardId && visualNumber) {
    const empCard = await prisma?.eMPCard.update({
      where: { id: cardId },
      data: { sentOut: new Date(), note: `Card ${visualNumber} was sent to customer` },
    });
    if (empCard) {
      return NextResponse.json(
        {
          message: "saved",
        },
        { status: 200 },
      );
    }
  }

  return NextResponse.json(
    {
      message: "error",
    },
    { status: 500 },
  );
}
