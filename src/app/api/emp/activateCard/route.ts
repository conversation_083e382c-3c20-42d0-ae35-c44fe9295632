import type { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { pushEmpCard } from "~/utils/longship";
import { activateCard } from "~/utils/user/empcard";

export async function PATCH(request: NextRequest) {
  const { cardId, cardNumber } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session) {
    return new Response("no auth", { status: 401 });
  }

  const errors = await activateCard(cardId, cardNumber);
  if (errors.length) {
    return new Response(errors.join(", "), { status: 500 });
  }
  return new Response("Karte wurde aktiviert", { status: 200 });
}
