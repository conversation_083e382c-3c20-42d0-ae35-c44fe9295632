import type { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import nodemailer from "nodemailer";
import { env } from "~/env.js";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { NotificationType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const { selectedTarifIds } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session) {
    return new Response("no auth", { status: 401 });
  }

  const id = session?.user.id;
  if (id) {
    try {
      const newCard = await prisma?.eMPCard.create({
        data: {
          userId: id,
          tarifs: {
            create: selectedTarifIds.map((tid: string) => {
              return { tarif: { connect: { id: tid } } };
            }),
          },
        },
        include: { user: { include: { ou: true } } },
      });
      const transporter = nodemailer.createTransport({
        port: 465,
        host: env.EMAIL_SERVER_HOST,
        auth: {
          user: env.EMAIL_SERVER_USER,
          pass: env.EMAIL_SERVER_PASSWORD,
        },
      });

      // Send email
      await transporter.sendMail({
        from: "<EMAIL>",
        to: `we+newcard${env.NODE_ENV != "production" ? "_DEV_" : ""}@eulektro.de`,
        subject: `Neue Ladekarte wurde bestellt ${env.NODE_ENV != "production" ? "_DEV_" : ""}`,
        text: `${
          env.NODE_ENV != "production" ? "_DEV_" : ""
        }Eine neue Laderkarte wurde bestellt von: ${newCard?.user?.name}  ${newCard?.user
          ?.lastName}  ${newCard?.user?.ou.name}`,
      });

      // Create admin notification
      await createSystemNotificationForAdmins({
        nachricht: `Neue Ladekarte bestellt von ${newCard?.user?.name} ${newCard?.user?.lastName} (${newCard?.user?.ou.name})`,
        type: NotificationType.INFO,
      });
    } catch (e) {
      return new Response("cant create", { status: 500 });
    }

    return new Response("ok", { status: 200 });
  }
  return new Response("no auth", { status: 401 });
}
