import { type DefaultSession } from "../server/types/next-auth";
import type { Ou, Role, User as PrismaUser } from "@prisma/client";

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */

  interface User extends PrismaUser {
    id: string;
    role?: Role;
    ou: Ou;
    selectedOu: Ou;
    email: string;
    remoteStartToken?: string;
  }

  interface Session extends DefaultSession {
    user: User;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role?: Role;
    ou?: Ou;
  }
}
