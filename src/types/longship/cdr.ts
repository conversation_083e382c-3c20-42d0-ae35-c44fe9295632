export interface ChargeDetailRecord {
  CDR_ID: string;
  Start_datetime: string;
  End_datetime: string;
  Duration: string;
  Volume: string;
  Charge_Point_Address: string;
  Charge_Point_ZIP: string;
  Charge_Point_City: string;
  Charge_Point_Country: string;
  Charge_Point_Type: number;
  Product_Type: number;
  Tariff_Type: string;
  Authentication_ID: string;
  Contract_ID: string;
  Meter_ID: string;
  OBIS_Code: string;
  Charge_Point_ID: string;
  Service_Provider_ID: string;
  Infra_Provider_ID: string;
  Calculated_Cost: number;
  Timezone: string;
  LocalStart_datetime: string;
  LocalEnd_datetime: string;
  Location_ID: string;
  OU_Code: string;
  Tariff_Name: string;
  Start_Tariff: number;
  Tariff_kWh: number;
  Token_OU_Code: string;
  Token_OU_Name: string;
  OU_Name: string;
  Owner: string;
  Operator: string;
  Sub_Operator: string;
  MeterStart: number;
  MeterStop: number;
  ExternalReference: string;
  Charging_Time_Cost: number;
  Parking_Time_Cost: number;
  ConnectorId: number;
  EnergyCosts: number;
}
