export interface ApiResponse<T> {
  status: "success" | "error"; // <PERSON><PERSON>gt den Erfolg oder Fehler des Requests an
  payload?: T; // Generischer Typ, der die Nutzdaten bei Erfolg hält
  message?: string; // Ein<PERSON> Nachricht, die weiteren Kontext zum Ergebnis liefert
  errorCode?: string; // Ein optionaler Fehlercode, wenn `status` 'error' ist
  errorDetails?: any; // Zusätzliche Fehlerdetails, z.B. für Debugging-Zwecke
}
