export interface CdrWithIsoString {
  CDR_ID: string;
  Start_datetime: string;
  End_datetime: string;
  Duration: string | null;
  DurationInSec: number | null;
  Volume: number | null;
  Charge_Point_Address: string | null;
  Charge_Point_ZIP: string | null;
  Charge_Point_City: string | null;
  Charge_Point_Country: string | null;
  Charge_Point_Type: number | null;
  Product_Type: number | null;
  Tariff_Type: string | null;
  Authentication_ID: string | null;
  Contract_ID: string | null;
  Meter_ID: string | null;
  OBIS_Code: string | null;
  Charge_Point_ID: string | null;
  Service_Provider_ID: string | null;
  Infra_Provider_ID: string | null;
  Calculated_Cost: number | null;
  Timezone: string | null;
  LocalStart_datetime: string | null;
  LocalEnd_datetime: string | null;
  Location_ID: string | null;
  OU_Code: string | null;
  Tariff_Name: string | null;
  Start_Tariff: number | null;
  Tariff_kWh: number | null;
  Token_OU_Code: string | null;
  Token_OU_Name: string | null;
  OU_Name: string | null;
  Owner: string | null;
  Operator: string | null;
  Sub_Operator: string | null;
  MeterStart: number | null;
  MeterStop: number | null;
  ExternalReference: string | null;
  Charging_Time_Cost: number | null;
  Parking_Time_Cost: number | null;
  ConnectorId: number | null;
  invoiceId: string | null;
  tarifId: string | null;
  billable: boolean;
}
