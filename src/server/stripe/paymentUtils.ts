import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { stripe } from "~/utils/stripe/stripe";
import { Contact, User } from "@prisma/client";
import { ContactsWithAddress } from "~/types/prisma/contact";

export const getInvoices = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return false;
  }

  const id = session?.user.id;
  const invoices = await prisma.invoice.findMany({
    where: {
      userId: id,
    },
  });
  if (invoices.length === 0) {
    return false;
  }
  return invoices;
};

export const createStripeCompany = async (contact: Contact) => {
  if (contact?.invoiceMail && contact?.companyName) {
    const customer = await stripe.customers.create({
      email: contact.invoiceMail,
      name: contact.companyName,
      metadata: { eulektroContactId: contact.id },
    });
    if (customer) {
      const contactWithStripe = await prisma.contact.update({
        where: { id: contact.id },
        data: { stripeCustomerId: customer.id },
        include: { contactAddress: true },
      });
      return contactWithStripe;
    }
  }
};

export const createStripeUser = async (user: User) => {
  const customer = await stripe.customers.create({
    email: user.email,
    name: `${user.name}  ${user.lastName}`,
    metadata: { eulektroUserId: user.id },
  });

  const user_with_stripe = await prisma.user.update({
    where: { id: user.id },
    data: { stripeCustomerId: customer.id },
  });
  return user_with_stripe;
};

export const getPaymentMethods = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return [];
  }

  const id = session?.user.id;
  const user = await prisma.user.findUnique({
    where: {
      id: id,
    },
  });
  if (!user) {
    return [];
  }
  try {
    if (user.stripeCustomerId) {
      const stripeUser = await stripe.customers.retrieve(user.stripeCustomerId);
      const paymentMethods = await stripe.customers.listPaymentMethods(
        user.stripeCustomerId,
        { type: "sepa_debit" }, //todo add more
      );
      // need to check to access invoice_settings typesafe
      // if deleted is set there is no invoice_settings property
      if (!stripeUser.deleted) {
        const extractedData = paymentMethods.data.map((item) => ({
          name: item.billing_details.name,
          email: item.billing_details.email,
          last4: item?.sepa_debit?.last4,
          default: item.id === stripeUser.invoice_settings?.default_payment_method,
          id: item.id,
        }));
        return extractedData.sort((a, b) => (a.default ? -1 : 1));
      }
    }
  } catch (e) {
    return [];
  }
  return [];
};
