import type { CdrWithIncludes } from "../invoice/calculateCost";
import prisma from "../db/prisma";
import { Cdr, LogType, Prisma } from "@prisma/client";
import { computeRoamingCostByEmpPrice } from "../invoice/calculateCost";
import Logger from "~/server/logger/logger";

export const attachTariffToCdrs = async (cdrs: CdrWithIncludes[] | Cdr[]) => {
  const calcCdrs = await computeRoamingCostByEmpPrice(cdrs);

  const queries = [];
  for (const cdr of calcCdrs) {
    queries.push(
      prisma.cdr.update({
        where: {
          CDR_ID: cdr.CDR_ID,
        },
        data: {
          Calculated_Cost: cdr.Calculated_Cost,
          EnergyCosts: cdr.EnergyCosts,
          Service_Provider_ID: cdr.Service_Provider_ID,
          tarifId: cdr.tarifId,
          billable: cdr.billable,
          Tariff_kWh: cdr.Tariff_kWh,
          Start_Tariff: cdr.Start_Tariff,
          Tariff_Name: cdr.Tariff_Name,
          Parking_Time_Cost: cdr.Parking_Time_Cost,
          companyTarifId: cdr.companyTarifId,
        },
      }),
    );
  }
  await prisma.$transaction(queries, {
    isolationLevel: Prisma.TransactionIsolationLevel.Serializable, // optional, default defined by database configuration
  });

  return calcCdrs;
};
