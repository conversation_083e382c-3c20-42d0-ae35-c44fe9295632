import { getAllData } from "../../utils/longship";
import RedisClient from "../../utils/redis/redisClient";
import prisma from "../db/prisma";
import { getCurrentKw, getCurrentKwBySession } from "../../pages/api/realtime2";
import Logger from "../logger/logger";
import { LogType } from "@prisma/client";
import { endpoints } from "../api/longship/constants";

const sessionData = async (saveHistory = true) => {
  Logger(
    `Start with saveHistory ${saveHistory}`,
    "server/task/SessionData",
    `cron ${saveHistory}`,
    LogType.DEBUG,
  );
  const params = {
    runningOnly: "true",
    completedOnly: "false",
    orderBy: "start",
    descending: "true",
  };
  const now = new Date();
  Logger(
    `Url ${endpoints.SESSIONS}`,
    "server/task/SessionData",
    `cron ${saveHistory}`,
    LogType.DEBUG,
  );
  let sessionData: any = [];
  try {
    sessionData = await getAllData(endpoints.SESSIONS, { params });
  } catch (error) {
    if (error instanceof Error) {
      Logger(
        `getAllData Error: ${error.message}`,
        "server/task/SessionData",
        `cron ${saveHistory}`,
        LogType.ERROR,
      );
    } else {
      Logger(
        `An unknown error occurred during getAllData`,
        "server/task/SessionData",
        `cron ${saveHistory}`,
        LogType.ERROR,
      );
    }
    throw error; // Re-throw the error after logging
  } finally {
    Logger(
      `getAllData ${sessionData.length} finally`,
      "server/task/SessionData",
      `cron ${saveHistory}`,
      LogType.DEBUG,
    );
  }

  if (saveHistory) {
    let ouIds: string[] = sessionData.map((session: { ouId: string }) => session.ouId);
    ouIds = [...new Set(ouIds)];
    const currentPowerPerOu = ouIds.map((ouId) => {
      return {
        ouId: ouId,
        power: getCurrentKw(
          sessionData.filter((session: { ouId: string }) => session.ouId == ouId),
        ),
      };
    });
    await Promise.all(
      currentPowerPerOu.map(
        async (powerPerOu) =>
          await prisma.historyPowerByOu.create({
            data: {
              ou: {
                connect: {
                  id: powerPerOu.ouId,
                },
              },
              power: powerPerOu.power,
              timestamp: now,
            },
          }),
      ),
    );
    /*
      const power_data_by_charger = sessionData.map((x: any) => {
        return {
          power: getCurrentKwBySession(x),
          timestamp: now,
          chargePointId: x.chargePointId,
        };
      });

      const history = await prisma.historyPowerByCharger.findMany({
        orderBy: {
          timestamp: "desc",
        },
        where: {
          power: {
            gt: 0,
          },
        },
        distinct: ["chargePointId"],
      });


      const chargeSetToZero = history.filter((x) => {
        return !power_data_by_charger.find((y: any) => y.chargePointId == x.chargePointId);
      });

      if (chargeSetToZero) {
        chargeSetToZero.map((x) => {
          power_data_by_charger.push({
            power: 0,
            timestamp: now,
            chargePointId: x.chargePointId,
          });
        });
      }

      await prisma.historyPowerByCharger.createMany({
        data: power_data_by_charger,
      });*/
  }

  const redisClient = RedisClient.getInstance();
  return await redisClient.set("sessionData", sessionData);
};

export default sessionData;
