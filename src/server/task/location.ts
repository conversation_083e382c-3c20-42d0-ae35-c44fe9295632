// Import statements
import { getAllData } from "../../utils/longship";
import prisma from "~/server/db/prisma";
import { endpoints } from "../api/longship/constants";
import { z } from "zod";

// Types declaration
type Evse = {
  id: string;
  uid: string;
  evse_id: string;
  chargepointid: string;
  status: string;
  physical_reference: string;
  last_updated: string;
};

type LocationResult = {
  locationId: string;
  success: boolean;
  message: string;
  evseResults?: EvseResult[];
};

type EvseResult = {
  evseId: string;
  success: boolean;
  message: string;
};

// Function to upsert EVSEs with error handling
const upsertEvses = async (
  evsesArray: Evse[],
  locationId: string,
  ouId: string,
): Promise<EvseResult[]> => {
  const results: EvseResult[] = [];
  for (const evse of evsesArray) {
    if (evse.status.toLowerCase() !== "removed") {
      try {
        await prisma.evse.upsert({
          where: { uid: evse.uid },
          update: {
            connector: evse.id, //id is same like connector number only easier to access here
            status: evse.status,
            last_updated: new Date(evse.last_updated),
            ouId: ouId,
          },
          create: {
            connector: evse.id, //id is same like connector number only easier to access here
            uid: evse.uid,
            evse_id: evse.evse_id,
            status: evse.status,
            last_updated: new Date(evse.last_updated),
            physical_reference: evse.physical_reference,
            location: { connect: { id: locationId } },
            chargePoint: { connect: { id: evse.chargepointid } },
            Ou: { connect: { id: ouId } },
          },
        });
        results.push({ evseId: evse.uid, success: true, message: "Upsert successful" });
      } catch (error: unknown) {
        console.error(
          `Failed to upsert EVSE ${evse.uid}:`,
          error instanceof Error ? error.message : error,
        );
        results.push({
          evseId: evse.uid,
          success: false,
          message: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }
  }
  return results;
};

const LocationSchema = z
  .object({
    id: z.string(),
    country_code: z.string(),
    party_id: z.string(),
    publish: z.boolean(),
    name: z.string(),
    houseNumber: z.string(),
    street: z.string(),
    city: z.string(),
    postal_code: z.string(),
    country: z.string(),
    hotline_phonenumber: z.string(),
    parking_type: z.string(),
    coordinatesId: z.string().nullable().optional(),
    note: z.string().nullable().optional(),
    openingDate: z.date().optional(),
    ouId: z.string(),
  })
  .strip();
// Main function to fetch and upsert locations and their related data
const getLocation = async (): Promise<LocationResult[]> => {
  const locationResults: LocationResult[] = [];

  try {
    const data = await getAllData(endpoints.LOCATIONS);

    for (const location of data) {
      const locationResult: LocationResult = {
        locationId: location.id,
        success: true,
        message: "Upsert successful",
        evseResults: [],
      };
      try {
        const parsedLocation = LocationSchema.parse(location);

        await prisma.location.upsert({
          where: { id: parsedLocation.id },
          update: {
            ...parsedLocation,
          },
          create: {
            ...parsedLocation,
          },
        });

        if (location.evses) {
          const evseResults = await upsertEvses(location.evses, location.id, location.ouId);
          locationResult.evseResults = evseResults;
        }
      } catch (error: unknown) {
        console.error(
          `Failed to upsert location ${location.id}:`,
          error instanceof Error ? error.message : error,
        );
        locationResult.success = false;
        locationResult.message = error instanceof Error ? error.message : "Unknown error";
      }
      locationResults.push(locationResult);
    }
  } catch (error: unknown) {
    console.error("Failed to retrieve locations:", error instanceof Error ? error.message : error);
    return [
      {
        locationId: "N/A",
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      },
    ];
  }

  return locationResults;
};

export default getLocation;
