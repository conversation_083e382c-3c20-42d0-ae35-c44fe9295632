import { getAllData } from "../../utils/longship";
import RedisClient from "../../utils/redis/redisClient";
import { endpoints } from "../api/longship/constants";

const history = async () => {
  const chargepointStatus = await getAllData(endpoints.CHARGEPOINTSTATUS);

  const redisClient = RedisClient.getInstance();
  return await redisClient.set("chargepointstatus", chargepointStatus);
};

export default history;
