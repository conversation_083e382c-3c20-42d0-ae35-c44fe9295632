import type { EnergyStockPrice } from "@prisma/client";
import prisma from "../db/prisma";

export enum COPY_MODE {
  FULL = "full",
  DELTA = "delta",
}

const fetcAllData = async (copy_mode: COPY_MODE) => {
  let startDate = new Date();
  if (copy_mode == COPY_MODE.FULL) {
    startDate = new Date("2022-01-01");
  } else {
    startDate.setDate(startDate.getDate() - 1);
  }

  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 1);
  const currentDate = startDate;
  const data: EnergyStockPrice[] = [];

  while (currentDate <= endDate) {
    const day = currentDate.getDate();
    const month = currentDate.getMonth() + 1;
    const year = currentDate.getFullYear();
    const url = `https://www.voltego.de/?type=9998&tx_voltegoprice_price[day]=${day}&tx_voltegoprice_price[month]=${month}&tx_voltegoprice_price[year]=${year}`;

    const response = await fetch(url).catch((e) => console.error(e));
    let jsonData;
    if (response?.ok) {
      jsonData = await response.json();
      for (const x of jsonData[0].prices) {
        data.push({
          timestamp: new Date(x.priceYear, x.priceMonth - 1, day, x.priceHour, 0, 0, 0),
          amount: parseFloat(x.priceAmount),
          hour: x.priceHour,
          day: day,
          month: x.priceMonth,
          year: x.priceYear,
        });
      }
    } else {
      console.log("request failed", url);
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  await prisma.energyStockPrice.createMany({
    data: data,
    skipDuplicates: true,
  });

  return data;
};

const getExchangeElectricityPrice = async (copy_mode: COPY_MODE = COPY_MODE.DELTA) => {
  const data = await fetcAllData(copy_mode);
};

export default getExchangeElectricityPrice;
