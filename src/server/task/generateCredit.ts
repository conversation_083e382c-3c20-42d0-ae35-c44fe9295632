import Logger from "../logger/logger";
import { findMaxEndDatetime, findMinEndDatetime } from "~/utils/invoice/invoiceHelper";
import { getPowerType } from "~/utils/longship";
import { getContactAdressByDate } from "~/utils/contact/getContactAdressByDate";
import type { CompanyTarif, Prisma } from "@prisma/client";
import type { EmpPrice, LocationPrice } from "../../../prismaMongoAdhoc/client";
import type { Cdr, CdrPayout, Contact, CreditTarif, Invoice, Tarif } from "@prisma/client";
import { KindOfInvoice, KindOfTarif, LogType, StateOfInvoice } from "@prisma/client";
import { CreditTarifType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { getPriceForStationByEvseId } from "~/utils/adhoc/pricing";

export type ContactsWithIncludes = Prisma.ContactGetPayload<{
  include: { ou: true; contactAddress: true };
}>;

export type ContactsWithOuAddressProviders = Prisma.ContactGetPayload<{
  include: { ou: true; contactAddress: true; providers: true };
}>;

interface FindCreditParams {
  contact: ContactsWithIncludes;
  dateRange: {
    0: Date;
    1: Date;
  };
  onlyPaid: boolean;
}

const findCreditCdrBy = async ({ contact, dateRange, onlyPaid = false }: FindCreditParams) => {
  if (contact.ou === null) {
    throw new Error("no ou for contact found");
  }
  // add 1 day becase lower than (lt) used
  const end = new Date(dateRange[1]);
  const start = new Date(dateRange[0]);
  end.setDate(end.getDate() + 1);
  let prismaQueryObject: Prisma.CdrFindManyArgs = {
    where: {
      End_datetime: {
        gte: start,
        lt: end,
      },
      OU_Code: {
        equals: `'${contact.ou.code}'`, // ja das muss so, weil es mit ' in der DB steht
      },

      creditInvoice: null,
      billable: true,
    },
    include: {
      tarif: true,
      invoice: true,
      companyTarif: true,
    },
  };
  if (onlyPaid) {
    prismaQueryObject = {
      where: {
        End_datetime: {
          gte: start,
          lt: end,
        },
        OU_Code: {
          equals: `'${contact.ou.code}'`, // ja das muss so, weil es mit ' in der DB steht
        },

        invoice: { paidOnDate: { not: null } },
        creditInvoice: null,
        billable: true,
      },
      include: {
        tarif: true,
        companyTarif: true,
        invoice: true,
      },
    };
  }

  return prisma.cdr.findMany(prismaQueryObject);
};

const addFlexPayout = (
  tariff: Tarif | CompanyTarif,
  cdr: Cdr,
  payoutSummary: PayoutSummary,
  cdrPayouts: CdrPayout[],
  creditedCdrs: Cdr[],
) => {
  // wenn kein service provider da ist es ein CompanyTarif also DEEUL hat das Geld eingezogen
  // und muss ausshütten
  const serviceProviderId = cdr.Service_Provider_ID || "DEEUL";
  if (cdr.Volume && cdr.DurationInSec && tariff) {
    let blockingMinutes = cdr.DurationInSec / 60 - tariff.blockingFeeBeginAtMin;
    if (blockingMinutes > 0 && tariff.blockingFeeBeginAtMin > 0) {
      if (
        tariff.blockingFeeMax > 0 &&
        blockingMinutes * tariff.blockingFee > tariff.blockingFeeMax
      ) {
        blockingMinutes = tariff.blockingFeeMax / tariff.blockingFee;
      }
    } else {
      blockingMinutes = 0;
    }
    if (blockingMinutes < 0) {
      blockingMinutes = 0;
    }

    if (!payoutSummary[serviceProviderId]) {
      payoutSummary[serviceProviderId] = {
        [tariff.id]: {
          energyCredit: cdr.EnergyCosts || 0,
          sessionCredit: cdr.Start_Tariff || 0,
          blockingFeeCredit: cdr.Parking_Time_Cost || 0,
          kWh: cdr.Volume,
          blockingMinutes: blockingMinutes,
          tarif: tariff,
          counter: 1,
          creditCardFee: 0,
        },
      };
      cdrPayouts.push({
        energyPayout: cdr.EnergyCosts || 0,
        sessionPayout: cdr.Start_Tariff || 0,
        blockingPayout: cdr.Parking_Time_Cost || 0,
        payoutSum: cdr.Calculated_Cost || 0,
        cdrId: cdr.CDR_ID,
        stripeFee: null,
      });
      creditedCdrs.push(cdr);
    } else {
      const providerSummary = payoutSummary[serviceProviderId];

      if (providerSummary && providerSummary[tariff.id]) {
        const tarifSummary = providerSummary[tariff.id];
        if (tarifSummary) {
          tarifSummary.energyCredit += cdr.EnergyCosts || 0;
          tarifSummary.sessionCredit += cdr.Start_Tariff || 0;
          tarifSummary.blockingFeeCredit += cdr.Parking_Time_Cost || 0;
          tarifSummary.kWh += cdr.Volume;
          tarifSummary.counter += 1;
          tarifSummary.blockingMinutes += blockingMinutes;

          cdrPayouts.push({
            payoutSum: cdr.Calculated_Cost || 0,
            cdrId: cdr.CDR_ID,
            energyPayout: cdr.EnergyCosts || 0,
            sessionPayout: cdr.Start_Tariff || 0,
            blockingPayout: cdr.Parking_Time_Cost || 0,
            stripeFee: null,
          });
          creditedCdrs.push(cdr);
        }
      } else if (providerSummary) {
        providerSummary[tariff.id] = {
          energyCredit: cdr.EnergyCosts || 0,
          sessionCredit: cdr.Start_Tariff || 0,
          blockingFeeCredit: cdr.Parking_Time_Cost || 0,
          kWh: cdr.Volume,
          blockingMinutes: blockingMinutes,
          tarif: tariff,
          counter: 1,
          creditCardFee: 0,
        };
        cdrPayouts.push({
          payoutSum: cdr.Calculated_Cost || 0,
          cdrId: cdr.CDR_ID,
          energyPayout: cdr.EnergyCosts || 0,
          sessionPayout: cdr.Start_Tariff || 0,
          blockingPayout: cdr.Parking_Time_Cost || 0,
          stripeFee: null,
        });
        creditedCdrs.push(cdr);
      }
    }
  } else {
    Logger(`Problem Credit for CDR ${cdr.CDR_ID}`, "Credit Error", "Credit", LogType.WARN);
  }
};

const calcAdhocPayout = async (cdrs: Cdr[], payoutSummary: PayoutSummary) => {
  const cdrAdhocPayouts: CdrPayout[] = [];
  const serviceProviderId = "DEEUL";
  let payoutCdrIds: string[] = [];
  const adhocCdrs = cdrs.filter(
    (cdr) =>
      cdr.Service_Provider_ID == "DEEUL" &&
      (cdr?.Contract_ID == "n/a" || cdr.Contract_ID == "ADHOC"),
  ); // n/a = adhoc
  if (adhocCdrs.length == 0) {
    return { cdrAdhocPayouts, adhocCdrs };
  }

  const cdrIds = adhocCdrs.map((cdr) => cdr.CDR_ID);

  /*  Joine alle CDRs auf die Stripe Payout Items und berechne die Gebühren (summe_fee) und die Auschüttung (summe_payout)
        für jeden CDR.
  * */
  const sqlquery = `select paymentMetadataCdrId,
        sum(gross) as brutto,
        sum(net)   as summe_payout,
        sum(fee)   as summe_fee,
        paymentMetadataEvse
    from Cdr
    left join StripePayoutItem
    on CDR_ID = paymentMetadataCdrId
    where paymentMetadataCdrId in (${[...cdrIds].map((cdrid) => `'${cdrid}'`)})
    group by paymentMetadataCdrId`;
  try {
    const payoutData: {
      brutto: number;
      summe_payout: number;
      summe_fee: number;
      paymentMetadataCdrId: string;
    }[] = await prisma.$queryRawUnsafe(sqlquery);
    payoutCdrIds = payoutData.map((payItem) => payItem.paymentMetadataCdrId);
    for (const cdr of adhocCdrs) {
      const matchedPayoutItem = payoutData.find(
        (payoutItem) => payoutItem.paymentMetadataCdrId == cdr.CDR_ID,
      );
      if (matchedPayoutItem) {
        const adhocTarif = await getPriceForStationByEvseId(
          cdr.Charge_Point_ID ?? "none",
          cdr.Start_datetime,
          cdr?.Charge_Point_Type ?? 0,
        );
        if (
          adhocTarif &&
          adhocTarif.energy_price &&
          adhocTarif.tax_rate &&
          cdr.Volume &&
          cdr.DurationInSec
        ) {
          const stripeFee = parseFloat(matchedPayoutItem.summe_fee.toFixed(2));
          let blockingMinutes = cdr.DurationInSec / 60 - adhocTarif.blocking_fee_start;
          if (blockingMinutes > 0 && adhocTarif.blocking_fee_start > 0) {
            if (
              adhocTarif.blocking_fee_max > 0 &&
              blockingMinutes * adhocTarif.blocking_fee > adhocTarif.blocking_fee_max
            ) {
              blockingMinutes = adhocTarif.blocking_fee_max / adhocTarif.blocking_fee;
            }
          } else {
            blockingMinutes = 0;
          }
          if (blockingMinutes < 0) {
            blockingMinutes = 0;
          }

          if (!payoutSummary[serviceProviderId]) {
            payoutSummary[serviceProviderId] = {
              [adhocTarif.id]: {
                energyCredit: cdr.EnergyCosts || 0,
                sessionCredit: cdr.Start_Tariff || 0,
                blockingFeeCredit: cdr.Parking_Time_Cost || 0,
                kWh: cdr.Volume,
                blockingMinutes: blockingMinutes,

                tarif: adhocTarif,
                counter: 1,
                creditCardFee: stripeFee * -1,
              },
            };
            cdrAdhocPayouts.push({
              energyPayout: cdr.EnergyCosts || 0,
              sessionPayout: cdr.Start_Tariff || 0,
              blockingPayout: cdr.Parking_Time_Cost || 0,
              cdrId: cdr.CDR_ID,
              stripeFee: stripeFee,
              payoutSum: cdr.Calculated_Cost
                ? parseFloat((cdr.Calculated_Cost - stripeFee).toFixed(2))
                : 0,
            });
          } else {
            const providerSummary = payoutSummary[serviceProviderId];
            if (providerSummary && providerSummary[adhocTarif.id]) {
              const tarifSummary = providerSummary[adhocTarif.id];
              if (tarifSummary) {
                tarifSummary.energyCredit += cdr.EnergyCosts || 0;
                tarifSummary.sessionCredit += cdr.Start_Tariff || 0;
                tarifSummary.blockingFeeCredit += cdr.Parking_Time_Cost || 0;
                tarifSummary.kWh += cdr.Volume;
                tarifSummary.counter += 1;
                tarifSummary.creditCardFee += stripeFee * -1;
                tarifSummary.blockingMinutes += blockingMinutes;
                cdrAdhocPayouts.push({
                  energyPayout: cdr.EnergyCosts || 0,
                  sessionPayout: cdr.Start_Tariff || 0,
                  blockingPayout: cdr.Parking_Time_Cost || 0,
                  cdrId: cdr.CDR_ID,
                  stripeFee: stripeFee,
                  payoutSum: cdr.Calculated_Cost
                    ? parseFloat((cdr.Calculated_Cost - stripeFee).toFixed(2))
                    : 0,
                });
              }
            } else if (providerSummary) {
              payoutSummary[serviceProviderId][adhocTarif.id] = {
                energyCredit: cdr.EnergyCosts || 0,
                sessionCredit: cdr.Start_Tariff || 0,
                blockingFeeCredit: cdr.Parking_Time_Cost || 0,
                kWh: cdr.Volume,
                blockingMinutes: blockingMinutes,
                tarif: adhocTarif,
                counter: 1,
                creditCardFee: stripeFee * -1,
              };
              cdrAdhocPayouts.push({
                energyPayout: cdr.EnergyCosts || 0,
                sessionPayout: cdr.Start_Tariff || 0,
                blockingPayout: cdr.Parking_Time_Cost || 0,
                cdrId: cdr.CDR_ID,
                stripeFee: stripeFee,
                payoutSum: cdr.Calculated_Cost
                  ? parseFloat((cdr.Calculated_Cost - stripeFee).toFixed(2))
                  : 0,
              });
            }
          }
        }
      }
    }
  } catch (e) {
    throw new Error("Prisma Exepcetion Raw Query Payoutdata");
  }
  // wenn cdrid nicht in den payouts ist, dann filter diesen heraus, sodass nur CDRs zurückgegeben werden,
  // die auch gutgeschrieben werden
  const matchedCdrs = adhocCdrs.filter((cdr) => payoutCdrIds.includes(cdr.CDR_ID));
  for (const cdr of matchedCdrs) {
    cdr.Contract_ID = "Adhoc";
  }
  return { cdrAdhocPayouts, adhocCdrs: matchedCdrs };
};

const addFixPayout = (
  creditTarif: CreditTarif,
  cdr: Cdr,
  payoutSummary: PayoutSummary,
  cdrPayouts: CdrPayout[],
  creditedCdrs: Cdr[],
) => {
  const serviceProviderId = cdr.Service_Provider_ID ?? "";
  if (cdr.Volume && cdr.DurationInSec && creditTarif) {
    const energyCredit = parseFloat((creditTarif.energyCredit * cdr.Volume).toFixed(2));
    const sessionCredit = parseFloat((creditTarif.sessionCredit * cdr.Volume).toFixed(2));
    let blockingFeeCredit = 0;
    let blockingMinutes = 0;

    if (
      creditTarif.blockingCredit > 0 &&
      cdr.DurationInSec / 60 - creditTarif.blockingFeeMinStart > 0
    ) {
      blockingMinutes = cdr.DurationInSec / 60 - creditTarif.blockingFeeMinStart;
      if (blockingMinutes < 0) {
        blockingMinutes = 0;
      }
      blockingFeeCredit =
        (cdr.DurationInSec / 60 - creditTarif.blockingFeeMinStart) * creditTarif.blockingCredit;
      blockingFeeCredit =
        blockingFeeCredit > creditTarif.maxBlockingCredit && creditTarif.maxBlockingCredit > 0
          ? creditTarif.maxBlockingCredit
          : blockingFeeCredit;
    }
    if (!payoutSummary[serviceProviderId]) {
      payoutSummary[serviceProviderId] = {
        [creditTarif.id]: {
          energyCredit: energyCredit,
          sessionCredit: sessionCredit,
          blockingFeeCredit: blockingFeeCredit,
          kWh: cdr.Volume,
          blockingMinutes: blockingMinutes,
          tarif: creditTarif,
          counter: 1,
          creditCardFee: 0,
        },
      };
      cdrPayouts.push({
        energyPayout: energyCredit,
        sessionPayout: sessionCredit,
        blockingPayout: blockingFeeCredit,
        payoutSum: energyCredit + sessionCredit + blockingFeeCredit,
        cdrId: cdr.CDR_ID,
        stripeFee: null,
      });
      creditedCdrs.push(cdr);
    } else {
      const providerSummary = payoutSummary[serviceProviderId];
      if (providerSummary && providerSummary[creditTarif.id]) {
        const tarifSummary = providerSummary[creditTarif.id];
        if (tarifSummary) {
          tarifSummary.energyCredit += energyCredit;
          tarifSummary.sessionCredit += sessionCredit;
          tarifSummary.blockingFeeCredit += blockingFeeCredit;
          tarifSummary.blockingMinutes += blockingMinutes;
          tarifSummary.kWh += cdr.Volume;
          tarifSummary.counter += 1;

          cdrPayouts.push({
            energyPayout: energyCredit,
            sessionPayout: sessionCredit,
            blockingPayout: blockingFeeCredit,
            payoutSum: energyCredit + sessionCredit + blockingFeeCredit,
            cdrId: cdr.CDR_ID,
            stripeFee: null,
          });
          creditedCdrs.push(cdr);
        }
      } else if (providerSummary) {
        payoutSummary[serviceProviderId] = {
          [creditTarif.id]: {
            energyCredit: energyCredit,
            sessionCredit: sessionCredit,
            blockingFeeCredit: blockingFeeCredit,
            kWh: cdr.Volume,
            blockingMinutes: blockingMinutes,
            tarif: creditTarif,
            counter: 1,
            creditCardFee: 0,
          },
        };
        cdrPayouts.push({
          energyPayout: energyCredit,
          sessionPayout: sessionCredit,
          blockingPayout: blockingFeeCredit,
          payoutSum: energyCredit + sessionCredit + blockingFeeCredit,
          cdrId: cdr.CDR_ID,
          stripeFee: null,
        });
        creditedCdrs.push(cdr);
      }
    }
  }
};

type TarifPayoutSummary = {
  energyCredit: number;
  sessionCredit: number;
  blockingFeeCredit: number;
  blockingMinutes: number;
  kWh: number;
  tarif: CreditTarif | Tarif | EmpPrice | LocationPrice | CompanyTarif;
  counter: number;
  creditCardFee: number;
};

type PayoutSummary = {
  [serviceProviderId: string]: {
    [tarif: string]: TarifPayoutSummary;
  };
};

const calcCpoPayout = async (
  cdrs: Cdr[],
  contactId: string,
): Promise<{ payoutSummary: PayoutSummary; calculatedCdrs: Cdr[] }> => {
  const payoutSummary: PayoutSummary = {};
  const cpoTarifs = await prisma.contactCreditTarif.findMany({
    where: { contactId: contactId },
    include: { creditTarif: true },
  });
  // collects all cdrs for which a payout will be paid

  const { cdrAdhocPayouts, adhocCdrs } = await calcAdhocPayout(cdrs, payoutSummary);

  const { cdrDirectPaymentPayouts, directPaymentCdrs } = await calcDirectPaymentPayout(
    cdrs,
    payoutSummary,
  );
  const creditedCdrs = [...adhocCdrs, ...directPaymentCdrs];
  const cdrPayouts = [...cdrAdhocPayouts, ...cdrDirectPaymentPayouts];
  //
  const cdrsWithNoCreditTarif: string[] = [];

  for (const cdr of cdrs) {
    const powerType = getPowerType(cdr.Charge_Point_Type);
    const matchingTarifs = cpoTarifs.filter(
      (tarif) =>
        powerType == tarif.creditTarif.powerType &&
        cdr.Start_datetime >= tarif.creditTarif.validFrom &&
        (tarif.creditTarif.validTo == null || cdr.End_datetime <= tarif.creditTarif.validTo),
    );
    if (matchingTarifs?.length == 0) {
      cdrsWithNoCreditTarif.push(cdr.CDR_ID);
      Logger(
        `No matching tariff found vor CDR ${cdr.CDR_ID} `,
        "Credit calculation",
        "Credit",
        LogType.WARN,
      );
      continue;
    }

    if (matchingTarifs?.length > 1) {
      const tarifNames = matchingTarifs.map((t) => t.creditTarif.name);
      Logger(
        `Multiple tarifs found ${cdr.tarifId} `,
        "Credit calculation",
        "Credit",
        LogType.ERROR,
      );
      throw new Error(
        `Es wurden mehrere CreditTarifs für CDR ${cdr.CDR_ID} gefunden. -> ${tarifNames.join(",")}`,
      );
    }

    const creditTarif = matchingTarifs[0]?.creditTarif as CreditTarif; //TOdO;

    if (creditTarif.tarifType === CreditTarifType.FIX) {
      addFixPayout(creditTarif, cdr, payoutSummary, cdrPayouts, creditedCdrs);
    } else if (creditTarif.tarifType === CreditTarifType.FLEX) {
      if (cdr.tarifId) {
        // Nimm nur den tarif wenn er kein direct tarif ist, das wurde schon in calcCpoPayout berechnet
        const roamingTariff = await prisma.tarif.findUnique({
          where: { id: cdr.tarifId, kindOfTarif: { not: KindOfTarif.DIRECT } },
        });

        if (roamingTariff) {
          addFlexPayout(roamingTariff, cdr, payoutSummary, cdrPayouts, creditedCdrs);
        }
      } else if (cdr.companyTarifId) {
        const companyTariff = await prisma.companyTarif.findUnique({
          where: { id: cdr.companyTarifId },
        });

        if (companyTariff) {
          addFlexPayout(companyTariff, cdr, payoutSummary, cdrPayouts, creditedCdrs);
        }
      }
    }
  }
  if (cdrsWithNoCreditTarif.length > 0) {
    throw new Error(
      `Es wurde mindestens ein CDR gefunden, der keinen CreditTarif zugeordnet wurden konnten: ${cdrsWithNoCreditTarif.join(
        ",",
      )}`,
    );
  }
  await prisma.cdrPayout.createMany({
    data: cdrPayouts,
    skipDuplicates: true,
  });

  return { payoutSummary, calculatedCdrs: creditedCdrs };
};

interface CreateInvoiceProps {
  contact: Contact;
  payoutSummary: PayoutSummary;
  minDate: Date;
  maxDate: Date;
  calculatedCdrs: Cdr[];
  taxRate?: number;
}

const createCredit = async ({
  contact,
  payoutSummary,
  minDate,
  maxDate,
  calculatedCdrs,
  taxRate = 19,
}: CreateInvoiceProps) => {
  const positions: any[] = [];
  let posNumber = 0;
  let sumKwh = 0;
  let sumSession = 0;

  //not persistet currently
  let sumBlocking = 0;
  let sumCreditCardFee = 0;

  for (const serviceProviderId in payoutSummary) {
    const serviceProviderSummary = payoutSummary[serviceProviderId];
    if (!serviceProviderSummary) continue;

    for (const tarifId in serviceProviderSummary) {
      const tarifSummary = serviceProviderSummary[tarifId];
      if (!tarifSummary) continue;

      let powerType = ""; //TODO

      if ("energyCredit" in tarifSummary.tarif) {
        powerType = tarifSummary.tarif.powerType;
      } else if ("kwh" in tarifSummary.tarif) {
        powerType = tarifSummary.tarif.currentType ?? "AC";
      }
      let name = "";
      if ("name" in tarifSummary.tarif) {
        //füge tarifnamen hinzu wenn mitarbeitertarif sonst nicht

        if (tarifSummary.tarif?.kindOfTarif == KindOfTarif.COMPANY) {
          name = tarifSummary.tarif.name ?? "";
        }
        if (tarifSummary.tarif?.kindOfTarif == KindOfTarif.ROAMING) {
          name = "Roaming";
        }
        if (tarifSummary.tarif.kindOfTarif == KindOfTarif.DIRECT) {
          name = `Direct (tarif start:${tarifSummary.tarif.validFrom.toLocaleDateString()})`;
        }
      } else {
        if ("locationId" in tarifSummary.tarif) {
          name = `Adhoc ${tarifSummary.tarif.locationId} ${tarifSummary.tarif.current_type}`;
        } else {
          //füge namen hinzu wenn adhoc tarif
          name = `Adhoc Default ${tarifSummary.tarif.current_type}`;
        }
      }
      if (tarifSummary.energyCredit > 0) {
        // push line

        const sumNetRevenuekWh = tarifSummary.energyCredit;
        const sumGrossRevenuekWh = +(sumNetRevenuekWh * (taxRate / 100 + 1)).toFixed(2);
        const sumTaxRevenuekWh = +(sumNetRevenuekWh * (taxRate / 100)).toFixed(2);
        let tarifEnergyUnit = 0;
        // flex or fix
        //TODO unitPreis leer
        if ("energyCredit" in tarifSummary.tarif) {
          //fix

          tarifEnergyUnit = tarifSummary.tarif.energyCredit;
        } else if ("kwh" in tarifSummary.tarif) {
          // flex
          tarifEnergyUnit = tarifSummary.tarif.kwh;
        } else if ("energyPrice" in tarifSummary.tarif) {
          // flex
          tarifEnergyUnit = tarifSummary.tarif.energyPrice;
        } else if ("energy_price" in tarifSummary.tarif && tarifSummary.tarif.energy_price) {
          // brutto to netto umrechnung, da adhoc preis in brutto angegeben ist
          tarifEnergyUnit = tarifSummary.tarif.energy_price / (1 + 19 / 100);
        } else {
          Logger(
            `No energy price for tarif defined ${tarifSummary.tarif.id}`,
            "No Energy Price",
            "credit",
            LogType.INFO,
          );
        }
        posNumber += 1;

        positions.push({
          pos: posNumber,
          unitPrice: tarifEnergyUnit,
          amount: tarifSummary.kWh,
          title: `${serviceProviderId} ${name}`,
          sumNet: sumNetRevenuekWh,
          sumGross: sumGrossRevenuekWh,
          sumTax: sumTaxRevenuekWh,
          description: `${serviceProviderId} ${name} ${powerType ?? ""}`,
          unit: "kWh",
          taxRate: taxRate,
        });
      }
      if (tarifSummary.sessionCredit > 0) {
        // push line
        const sumNetCreditSession = tarifSummary.sessionCredit;
        const sumGrossCreditSession = +(sumNetCreditSession * (taxRate / 100 + 1)).toFixed(2);
        const sumTaxCreditSession = +(sumNetCreditSession * (taxRate / 100)).toFixed(2);

        let sessionTarifCredit = 0;
        // roaming tarif
        if ("sessionCredit" in tarifSummary.tarif) {
          sessionTarifCredit = tarifSummary.tarif.sessionCredit;
        }
        //credit tarif
        else if ("sessionFee" in tarifSummary.tarif) {
          sessionTarifCredit = tarifSummary.tarif.sessionFee || 0;
        }
        // companytarif
        else if ("sessionPrice" in tarifSummary.tarif) {
          sessionTarifCredit = tarifSummary.tarif.sessionPrice || 0;

          // EPMPrice or LocationPrice (aus Adhoc DB)
        } else if ("session_fee" in tarifSummary.tarif) {
          sessionTarifCredit = tarifSummary.tarif.session_fee / (1 + 19 / 100);
        }

        posNumber += 1;
        positions.push({
          pos: posNumber,
          unitPrice: sessionTarifCredit,
          amount: tarifSummary.counter,
          title: `${serviceProviderId} - ${name}`,
          sumNet: sumNetCreditSession,
          sumGross: sumGrossCreditSession,
          sumTax: sumTaxCreditSession,
          description: `${serviceProviderId} ${name} ${powerType ?? ""}`,
          unit: "session",
          taxRate: taxRate,
        });
      }
      if (tarifSummary.blockingFeeCredit > 0) {
        let blockingFeeTarif = 0;
        // fix
        if ("blockingCredit" in tarifSummary.tarif) {
          blockingFeeTarif = tarifSummary.tarif.blockingCredit;
        } // flex (company tarif, (roaming) tarif,
        else if ("blockingFee" in tarifSummary.tarif) {
          blockingFeeTarif = tarifSummary.tarif.blockingFee || 0;
        } else if ("blocking_fee" in tarifSummary.tarif) {
          const blockingFeeNet = +(
            tarifSummary.tarif.blocking_fee /
            (1 + tarifSummary.tarif.tax_rate / 100)
          ).toFixed(2);
          blockingFeeTarif = blockingFeeNet;
        }
        //todo wenn blocking in adhoc
        const sumNetRevenuekWh = tarifSummary.blockingFeeCredit;
        const sumGrossRevenuekWh = +(sumNetRevenuekWh * (taxRate / 100 + 1)).toFixed(2);
        const sumTaxRevenuekWh = +(sumNetRevenuekWh * (taxRate / 100)).toFixed(2);

        posNumber += 1;
        positions.push({
          pos: posNumber,
          unitPrice: blockingFeeTarif,
          amount: tarifSummary.blockingMinutes,
          title: `${serviceProviderId} - ${name}`,
          sumNet: sumNetRevenuekWh,
          sumGross: sumGrossRevenuekWh,
          sumTax: sumTaxRevenuekWh,
          description: `${serviceProviderId} ${name} ${powerType ?? ""}`,
          unit: "min",
          taxRate: taxRate,
        });
      }

      sumKwh += tarifSummary.kWh;
      sumSession += tarifSummary.counter;

      //not persisted currently
      sumBlocking += tarifSummary.blockingFeeCredit;
      sumCreditCardFee += tarifSummary.creditCardFee;
    }
  }
  if (positions.length == 0) {
    throw new Error(
      `Invoice ${contact.name} not created, because positions are empty. So no CDRs can be credited`,
    );
  }

  const createdInvoice: Invoice = await prisma.invoice.create({
    data: {
      subject: "Gutschrift",
      kindOfInvoice: KindOfInvoice.CREDIT,
      stateOfInvoice: StateOfInvoice.PREVIEW,
      servicePeriodFrom: minDate,
      servicePeriodTo: maxDate,
      createDate: new Date(),
      invoiceDate: new Date(),
      sumKwh: sumKwh,
      sumSession: sumSession,
      sumNet: positions.reduce((acc, curr) => acc + curr.sumNet, 0),
      sumGross: positions.reduce((acc, curr) => acc + curr.sumGross, 0),
      sumTax: positions.reduce((acc, curr) => acc + curr.sumTax, 0),
      contact: {
        connect: {
          id: contact.id,
        },
      },
      creditCdrs: {
        connect: calculatedCdrs.map((cdr) => {
          return {
            CDR_ID: cdr.CDR_ID,
          };
        }),
      },
      invoicePositions: {
        createMany: {
          data: positions,
        },
      },
    },
  });

  await prisma.cdr.updateMany({
    where: {
      CDR_ID: {
        in: calculatedCdrs.map((x) => x.CDR_ID),
      },
    },
    data: {
      creditInvoiceId: createdInvoice.id,
    },
  });

  return createdInvoice;
};

export type CreditTaskParams = {
  contactId: string;
  dateRange: {
    0: Date;
    1: Date;
  };
  onlyPaid?: boolean;
};
export const generateCreditTask = async ({
  contactId,
  dateRange,
  onlyPaid = false,
}: CreditTaskParams) => {
  const contact = await prisma.contact.findUnique({
    where: {
      id: contactId,
    },
    include: {
      contactAddress: true,
      ou: true,
    },
  });

  if (!contact) {
    return false;
  }

  const cdrs = await findCreditCdrBy({
    contact,
    dateRange,
    onlyPaid,
  });

  if (cdrs.length == 0) {
    Logger("no cdrs found", "Generate Credit", "invoice", LogType.WARN);
    throw new Error(`no cdrs found for ${contact.name}`);
  }

  const { payoutSummary, calculatedCdrs } = await calcCpoPayout(cdrs, contact.id);
  //todo minTime from cdrs oder calculatedCdrs
  const minDate = findMinEndDatetime(cdrs);
  const maxDate = findMaxEndDatetime(cdrs);

  if (!minDate || !maxDate) {
    throw new Error(`no min or max date found for ${contact.name}`);
  }

  // find cpoAddress which is valid for the period in order to get taxRate (which could change over time)
  const matchingContactAddress = await getContactAdressByDate(contact.contactAddress, minDate);
  const taxRate = matchingContactAddress?.isNetInvoice
    ? 0
    : matchingContactAddress?.invoiceTaxRate || 0;

  return await createCredit({
    contact,
    payoutSummary,
    minDate,
    maxDate,
    calculatedCdrs,
    taxRate: taxRate,
  });
};

const calcDirectPaymentPayout = async (cdrs: Cdr[], payoutSummary: PayoutSummary) => {
  const cdrDirectPaymentPayouts: CdrPayout[] = [];
  const serviceProviderId = "DEEUL";

  const contracts = await prisma?.cPOContract.findMany({ include: { tarifs: true } });

  const directPaymentTokens = contracts
    ?.map((contract) => contract.directPaymentToken)
    .filter((id) => id != "none" && id);

  const directPaymentCdrs = cdrs.filter(
    (cdr) =>
      cdr?.Authentication_ID &&
      directPaymentTokens.includes(cdr.Authentication_ID) &&
      cdr?.Service_Provider_ID == "DEEUL",
  );

  if (directPaymentCdrs.length == 0) {
    return { cdrDirectPaymentPayouts, directPaymentCdrs };
  }

  try {
    for (const cdr of directPaymentCdrs) {
      const matchingContract = contracts.find(
        (contract) =>
          contract.directPaymentToken == cdr.Authentication_ID &&
          contract.start <= cdr.Start_datetime &&
          contract.end >= cdr.Start_datetime,
      );
      let matchingTarif;
      if (matchingContract) {
        if (matchingContract.tarifs?.length > 0) {
          matchingTarif = matchingContract.tarifs.find(
            (tarif) => tarif.validFrom <= cdr.Start_datetime && tarif.validTo >= cdr.Start_datetime,
          );
        }
      }
      if (matchingContract && matchingTarif && cdr.Volume) {
        const creditCardFeePercent = matchingContract.directPaymentFeePercent ?? 0;
        const creditCardFee = cdr?.Calculated_Cost
          ? (cdr.Calculated_Cost / 100) * creditCardFeePercent
          : 0;
        if (!payoutSummary[serviceProviderId]) {
          payoutSummary[serviceProviderId] = {
            [matchingTarif.id]: {
              energyCredit: cdr.EnergyCosts || 0,
              sessionCredit: cdr.Start_Tariff || 0,
              blockingFeeCredit: 0,
              kWh: cdr.Volume,
              blockingMinutes: 0,
              tarif: matchingTarif,
              counter: 1,
              creditCardFee: creditCardFee * -1,
            },
          };
          cdrDirectPaymentPayouts.push({
            energyPayout: cdr.EnergyCosts || 0,
            sessionPayout: cdr.Start_Tariff || 0,
            blockingPayout: 0,
            cdrId: cdr.CDR_ID,
            stripeFee: creditCardFee,
            payoutSum: cdr.Calculated_Cost
              ? parseFloat((cdr.Calculated_Cost - creditCardFee).toFixed(2))
              : 0,
          });
        } else {
          const providerSummary = payoutSummary[serviceProviderId];
          if (providerSummary && providerSummary[matchingTarif.id]) {
            const tarifSummary = providerSummary[matchingTarif.id];
            if (tarifSummary) {
              tarifSummary.energyCredit += cdr.EnergyCosts || 0;
              tarifSummary.sessionCredit += cdr.Start_Tariff || 0;
              tarifSummary.kWh += cdr.Volume;
              tarifSummary.counter += 1;
              tarifSummary.creditCardFee += creditCardFee * -1;

              cdrDirectPaymentPayouts.push({
                energyPayout: cdr.EnergyCosts || 0,
                sessionPayout: cdr.Start_Tariff || 0,
                blockingPayout: 0,
                cdrId: cdr.CDR_ID,
                stripeFee: creditCardFee,
                payoutSum: cdr.Calculated_Cost
                  ? parseFloat((cdr.Calculated_Cost - creditCardFee).toFixed(2))
                  : 0,
              });
            }
          } else if (providerSummary) {
            payoutSummary[serviceProviderId][matchingTarif.id] = {
              energyCredit: cdr.EnergyCosts || 0,
              sessionCredit: cdr.Start_Tariff || 0,
              blockingFeeCredit: 0,
              kWh: cdr.Volume,
              blockingMinutes: 0,
              tarif: matchingTarif,
              counter: 1,
              creditCardFee: creditCardFee * -1,
            };
            cdrDirectPaymentPayouts.push({
              energyPayout: cdr.EnergyCosts || 0,
              sessionPayout: cdr.Start_Tariff || 0,
              blockingPayout: 0,
              cdrId: cdr.CDR_ID,
              stripeFee: creditCardFee,
              payoutSum: cdr.Calculated_Cost
                ? parseFloat((cdr.Calculated_Cost - creditCardFee).toFixed(2))
                : 0,
            });
          }
        }
      }
    }
  } catch (e) {
    throw new Error("Prisma Exepcetion Raw Query Payoutdata Directpayment");
  }
  // wenn cdrid nicht in den payouts ist, dann filter diesen heraus, sodass nur CDRs zurückgegeben werden,
  // die auch gutgeschrieben werden
  //const matchedCdrs = adhocCdrs.filter((cdr) => payoutCdrIds.includes(cdr.CDR_ID));
  return { cdrDirectPaymentPayouts, directPaymentCdrs };
};
