import { LogType } from "@prisma/client";
import prisma from "../db/prisma";
import { env } from "~/env.js";

const save = async (msg: string, headline: string, topic: string, type: LogType) => {
  await prisma.log.create({
    data: {
      timestamp: new Date(),
      message: msg,
      topic: topic,
      headline: headline,
      type: type,
    },
  });
};

const Logger = (msg: string, headline: string, topic = "Root", type: LogType = LogType.WARN) => {
  if (type == LogType.DEBUG && [LogType.DEBUG].includes(env.LOGLEVEL)) {
    console.log(`Logger (${type}/${env.LOGLEVEL}):${headline} - ${topic} - ${msg} `);
    void save(msg, headline, topic, type);
    return;
  }
  if (type == LogType.INFO && [LogType.DEBUG, LogType.INFO].includes(env.LOGLEVEL)) {
    console.log(`Logger (${type}/${env.LOGLEVEL}):${headline} - ${topic} - ${msg} `);
    void save(msg, headline, topic, type);
    return;
  }
  if (type == LogType.WARN && [LogType.DEBUG, LogType.INFO, LogType.WARN].includes(env.LOGLEVEL)) {
    console.log(`Logger (${type}/${env.LOGLEVEL}):${headline} - ${topic} - ${msg} `);
    void save(msg, headline, topic, type);
    return;
  }
  if (
    type == LogType.ERROR &&
    [LogType.DEBUG, LogType.INFO, LogType.WARN, LogType.ERROR].includes(env.LOGLEVEL)
  ) {
    console.log(`Logger (${type}/${env.LOGLEVEL}):${headline} - ${topic} - ${msg} `);
    void save(msg, headline, topic, type);
    return;
  }
};

export default Logger;
