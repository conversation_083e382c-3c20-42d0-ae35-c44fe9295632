import type { Ou } from "@prisma/client";
import prisma from "~/server/db/prisma";

const getOuByUser = async (email: string): Promise<Ou | null> => {
  return await prisma.user
    .findUnique({
      where: {
        email: email,
      },
      include: {
        ou: true,
      },
    })
    .then((user) => user?.ou || null);
};

export const getOusBelowOu = async (ou: Ou) => {
  const allOus = await prisma.ou.findMany({ where: { deleted: { equals: null } } });

  const findChildren = (parentId: string): Ou[] => {
    return allOus.filter((ou) => ou.parentId === parentId);
  };

  const traverseOus = (ouId: string): Ou[] => {
    const children = findChildren(ouId);
    return children.reduce((acc, childOu) => {
      return acc.concat(childOu, traverseOus(childOu.id));
    }, [] as Ou[]);
  };
  const childrenOus = traverseOus(ou.id);
  return [ou, ...childrenOus];
};

export const getOusBelowUserOu = async (email: string) => {
  const ou = await getOuByUser(email);

  if (!ou) {
    return [];
  }

  return await getOusBelowOu(ou);
};
