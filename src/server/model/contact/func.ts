import prisma from "~/server/db/prisma";
import type { Ou } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";

/**
 * Findet alle Contacts, die mit einer bestimmten OU oder deren Child-OUs verbunden sind
 * @param ou Die OU, für die Contacts gefunden werden sollen
 * @returns Array von Contact-IDs
 */
export const getContactsByOu = async (ou: Ou) => {
  try {
    // Alle OUs unterhalb der ausgewählten OU finden (inkl. der ausgewählten OU selbst)
    const ousBelow = await getOusBelowOu(ou);
    const ouIds = ousBelow.map((ou) => ou.id);

    // Alle Contacts finden, die mit diesen OUs verbunden sind
    const contacts = await prisma.contact.findMany({
      where: {
        ouId: { in: ouIds },
        cpo: true, // Nur CPO-Contacts berücksichtigen
      },
      select: {
        id: true,
        adhocEmpId: true,
      },
    });

    return contacts;
  } catch (error) {
    console.error("Error fetching contacts by OU:", error);
    return [];
  }
};

/**
 * Findet alle EMPIDs, die mit Contacts verbunden sind, die zu einer bestimmten OU oder deren Child-OUs gehören
 * @param ou Die OU, für die EMPIDs gefunden werden sollen
 * @returns Array von EMP-IDs
 */
export const getEmpIdsByOu = async (ou: Ou) => {
  try {
    const contacts = await getContactsByOu(ou);
    
    // Extrahiere alle adhocEmpIds, die nicht null oder leer sind
    const empIds = contacts
      .map((contact) => contact.adhocEmpId)
      .filter((id): id is string => !!id);
    
    return empIds;
  } catch (error) {
    console.error("Error fetching EMP IDs by OU:", error);
    return [];
  }
};
