import { INode } from "~/app/(app)/tenantconfiguration/component/Tree";
import prisma from "~/server/db/prisma";
import type { Ou } from "@prisma/client";

const getChildren = (parent: Ou, ous: Ou[]): INode[] => {
  const children = ous.filter((ou) => ou.parentId === parent.id);

  return children.map((ou) => {
    return {
      id: ou.id,
      name: ou.name,
      children: getChildren(ou, ous),
    };
  });
};

export const getOUTree = async (): Promise<INode[]> => {
  const ous = await prisma.ou.findMany();

  const rootOus = ous.filter((ou) => !ou.parentId);

  const nodes = rootOus.map((ou) => {
    return {
      id: ou.id,
      name: ou.name,
      children: getChildren(ou, ous),
    };
  });

  return nodes;
};
