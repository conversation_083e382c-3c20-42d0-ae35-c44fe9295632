import { PrismaClient } from "@prisma/client";
import { env } from "../../env.js";

declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}
const prismaLogoParams = ["query", "error", "warn"];
const noLog: any[] = [];
const prisma =
  global.prisma ||
  new PrismaClient({
    log: env.NODE_ENV === "development" ? noLog : ["error"],
  });

if (env.NODE_ENV !== "production") {
  global.prisma = prisma;
}

export default prisma;
