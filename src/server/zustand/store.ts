import { create } from "zustand";

export interface UserState {
  selectedOuId: string;
  setSelectedOuId: (selectedOu: string) => void;
  selectedOuName: string;
  setSelectedOuName: (name: string) => void;
}

export const userStore = create<UserState>((set) => ({
  selectedOuId: "",
  setSelectedOuId: (selectedOu: string) => set({ selectedOuId: selectedOu }),
  selectedOuName: "...",
  setSelectedOuName: (name: string) => set({ selectedOuName: name }),
}));
